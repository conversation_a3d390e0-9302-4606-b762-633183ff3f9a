hoistPattern:
  - '*'
hoistedDependencies:
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  chalk@4.1.2:
    chalk: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  emoji-regex@8.0.0:
    emoji-regex: private
  escalade@3.2.0:
    escalade: private
  get-caller-file@2.0.5:
    get-caller-file: private
  has-flag@4.0.0:
    has-flag: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  require-directory@2.1.1:
    require-directory: private
  rxjs@7.8.2:
    rxjs: private
  shell-quote@1.8.3:
    shell-quote: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  supports-color@8.1.1:
    supports-color: private
  tree-kill@1.2.2:
    tree-kill: private
  tslib@2.8.1:
    tslib: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Wed, 29 Oct 2025 05:27:39 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped: []
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
