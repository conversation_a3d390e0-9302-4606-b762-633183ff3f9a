{"name": "vee-validate", "version": "4.15.1", "description": "Painless forms for Vue.js", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "module": "dist/vee-validate.mjs", "unpkg": "dist/vee-validate.iife.js", "main": "dist/vee-validate.mjs", "type": "module", "exports": {".": {"types": "./dist/vee-validate.d.ts", "import": "./dist/vee-validate.mjs", "require": "./dist/vee-validate.cjs"}, "./dist/*": "./dist/*"}, "types": "dist/vee-validate.d.ts", "homepage": "https://vee-validate.logaretm.com/", "repository": {"url": "https://github.com/logaretm/vee-validate.git", "type": "git", "directory": "packages/vee-validate"}, "sideEffects": false, "keywords": ["VueJS", "<PERSON><PERSON>", "validation", "validator", "inputs", "form"], "files": ["dist/*.js", "dist/*.d.ts", "dist/*.cjs", "dist/*.mjs"], "peerDependencies": {"vue": "^3.4.26"}, "dependencies": {"@vue/devtools-api": "^7.5.2", "type-fest": "^4.8.3"}}