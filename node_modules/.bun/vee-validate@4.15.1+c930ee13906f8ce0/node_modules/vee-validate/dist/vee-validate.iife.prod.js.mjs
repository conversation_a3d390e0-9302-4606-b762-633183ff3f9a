/**
  * vee-validate v4.15.1
  * (c) 2025 <PERSON><PERSON><PERSON><PERSON>
  * @license MIT
  */
var VeeValidate=function(e,t){"use strict";function n(e){return"function"==typeof e}function r(e){return null==e}const l=e=>null!==e&&!!e&&"object"==typeof e&&!Array.isArray(e);function a(e){return Number(e)>=0}function u(e){if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function i(e,t){return Object.keys(t).forEach((n=>{if(u(t[n])&&u(e[n]))return e[n]||(e[n]={}),void i(e[n],t[n]);e[n]=t[n]})),e}function o(e){const t=e.split(".");if(!t.length)return"";let n=String(t[0]);for(let e=1;e<t.length;e++)a(t[e])?n+=`[${t[e]}]`:n+=`.${t[e]}`;return n}const s={};function d(e,t,n){"object"==typeof n.value&&(n.value=c(n.value)),n.enumerable&&!n.get&&!n.set&&n.configurable&&n.writable&&"__proto__"!==t?e[t]=n.value:Object.defineProperty(e,t,n)}function c(e){if("object"!=typeof e)return e;var t,n,r,l=0,a=Object.prototype.toString.call(e);if("[object Object]"===a?r=Object.create(e.__proto__||null):"[object Array]"===a?r=Array(e.length):"[object Set]"===a?(r=new Set,e.forEach((function(e){r.add(c(e))}))):"[object Map]"===a?(r=new Map,e.forEach((function(e,t){r.set(c(t),c(e))}))):"[object Date]"===a?r=new Date(+e):"[object RegExp]"===a?r=new RegExp(e.source,e.flags):"[object DataView]"===a?r=new e.constructor(c(e.buffer)):"[object ArrayBuffer]"===a?r=e.slice(0):"Array]"===a.slice(-6)&&(r=new e.constructor(e)),r){for(n=Object.getOwnPropertySymbols(e);l<n.length;l++)d(r,n[l],Object.getOwnPropertyDescriptor(e,n[l]));for(l=0,n=Object.getOwnPropertyNames(e);l<n.length;l++)Object.hasOwnProperty.call(r,t=n[l])&&r[t]===e[t]||d(r,t,Object.getOwnPropertyDescriptor(e,t))}return r||e}const v=Symbol("vee-validate-form"),f=Symbol("vee-validate-form-context"),p=Symbol("vee-validate-field-instance"),m=Symbol("Default empty value"),h="undefined"!=typeof window;function y(e){return n(e)&&!!e.__locatorRef}function g(e){return!!e&&n(e.parse)&&"VVTypedSchema"===e.__type}function V(e){return!!e&&n(e.validate)}function b(e){return"checkbox"===e||"radio"===e}function O(e){return/^\[.+\]$/i.test(e)}function j(e){return"SELECT"===e.tagName}function A(e,t){return!function(e,t){const n=![!1,null,void 0,0].includes(t.multiple)&&!Number.isNaN(t.multiple);return"select"===e&&"multiple"in t&&n}(e,t)&&"file"!==t.type&&!b(t.type)}function F(e){return S(e)&&e.target&&"submit"in e.target}function S(e){return!!e&&(!!("undefined"!=typeof Event&&n(Event)&&e instanceof Event)||!(!e||!e.srcElement))}function E(e,t){return t in e&&e[t]!==m}function k(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;var n,r,l;if(Array.isArray(e)){if((n=e.length)!=t.length)return!1;for(r=n;0!=r--;)if(!k(e[r],t[r]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(r of e.entries())if(!t.has(r[0]))return!1;for(r of e.entries())if(!k(r[1],t.get(r[0])))return!1;return!0}if(I(e)&&I(t))return e.size===t.size&&(e.name===t.name&&(e.lastModified===t.lastModified&&e.type===t.type));if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(r of e.entries())if(!t.has(r[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if((n=e.length)!=t.length)return!1;for(r=n;0!=r--;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(e=w(e),t=w(t),(n=(l=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!Object.prototype.hasOwnProperty.call(t,l[r]))return!1;for(r=n;0!=r--;){var a=l[r];if(!k(e[a],t[a]))return!1}return!0}return e!=e&&t!=t}function w(e){return Object.fromEntries(Object.entries(e).filter((([,e])=>void 0!==e)))}function I(e){return!!h&&e instanceof File}function C(e){return O(e)?e.replace(/\[|\]/gi,""):e}function M(e,t,n){if(!e)return n;if(O(t))return e[C(t)];return(t||"").split(/\.|\[(\d+)\]/).filter(Boolean).reduce(((e,t)=>{return(l(r=e)||Array.isArray(r))&&t in e?e[t]:n;var r}),e)}function T(e,t,n){if(O(t))return void(e[C(t)]=n);const l=t.split(/\.|\[(\d+)\]/).filter(Boolean);let u=e;for(let e=0;e<l.length;e++){if(e===l.length-1)return void(u[l[e]]=n);l[e]in u&&!r(u[l[e]])||(u[l[e]]=a(l[e+1])?[]:{}),u=u[l[e]]}}function B(e,t){Array.isArray(e)&&a(t)?e.splice(Number(t),1):l(e)&&delete e[t]}function P(e,t){if(O(t))return void delete e[C(t)];const n=t.split(/\.|\[(\d+)\]/).filter(Boolean);let a=e;for(let e=0;e<n.length;e++){if(e===n.length-1){B(a,n[e]);break}if(!(n[e]in a)||r(a[n[e]]))break;a=a[n[e]]}const u=n.map(((t,r)=>M(e,n.slice(0,r).join("."))));for(let t=u.length-1;t>=0;t--)i=u[t],(Array.isArray(i)?0===i.length:l(i)&&0===Object.keys(i).length)&&(0!==t?B(u[t-1],n[t-1]):B(e,n[0]));var i}function _(e){return Object.keys(e)}function x(e,n=void 0){const r=t.getCurrentInstance();return(null==r?void 0:r.provides[e])||t.inject(e,n)}function R(e,t,n){if(Array.isArray(e)){const n=[...e],r=n.findIndex((e=>k(e,t)));return r>=0?n.splice(r,1):n.push(t),n}return k(e,t)?n:t}function U(e,t=0){let n=null,r=[];return function(...l){return n&&clearTimeout(n),n=setTimeout((()=>{const t=e(...l);r.forEach((e=>e(t))),r=[]}),t),new Promise((e=>r.push(e)))}}function N(e,t){return l(t)&&t.number?function(e){const t=parseFloat(e);return isNaN(t)?e:t}(e):e}function D(e,t){let n;return async function(...r){const l=e(...r);n=l;const a=await l;return l!==n?a:(n=void 0,t(a,r))}}function $({get:e,set:n}){const r=t.ref(c(e()));return t.watch(e,(e=>{k(e,r.value)||(r.value=c(e))}),{deep:!0}),t.watch(r,(t=>{k(t,e())||n(c(t))}),{deep:!0}),r}function z(e){return Array.isArray(e)?e:e?[e]:[]}function q(e){const n=x(v),r=e?t.computed((()=>null==n?void 0:n.getPathState(t.toValue(e)))):void 0,l=e?void 0:t.inject(p);return!l&&(null==r||r.value),r||l}function W(e,t){const n={};for(const r in e)t.includes(r)||(n[r]=e[r]);return n}function K(e,t,n){return t.slots.default?"string"!=typeof e&&e?{default:()=>{var e,r;return null===(r=(e=t.slots).default)||void 0===r?void 0:r.call(e,n())}}:t.slots.default(n()):t.slots.default}function L(e){if(G(e))return e._value}function G(e){return"_value"in e}function X(e){if(!S(e))return e;const t=e.target;if(b(t.type)&&G(t))return L(t);if("file"===t.type&&t.files){const e=Array.from(t.files);return t.multiple?e:e[0]}if(j(n=t)&&n.multiple)return Array.from(t.options).filter((e=>e.selected&&!e.disabled)).map(L);var n;if(j(t)){const e=Array.from(t.options).find((e=>e.selected));return e?L(e):t.value}return function(e){return"number"===e.type||"range"===e.type?Number.isNaN(e.valueAsNumber)?e.value:e.valueAsNumber:e.value}(t)}function H(e){const t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?l(e)&&e._$$isNormalized?e:l(e)?Object.keys(e).reduce(((t,n)=>{const r=function(e){if(!0===e)return[];if(Array.isArray(e))return e;if(l(e))return e;return[e]}(e[n]);return!1!==e[n]&&(t[n]=J(r)),t}),t):"string"!=typeof e?t:e.split("|").reduce(((e,t)=>{const n=Q(t);return n.name?(e[n.name]=J(n.params),e):e}),t):t}function J(e){const t=e=>"string"==typeof e&&"@"===e[0]?function(e){const t=t=>{var n;return null!==(n=M(t,e))&&void 0!==n?n:t[e]};return t.__locatorRef=e,t}(e.slice(1)):e;return Array.isArray(e)?e.map(t):e instanceof RegExp?[e]:Object.keys(e).reduce(((n,r)=>(n[r]=t(e[r]),n)),{})}const Q=e=>{let t=[];const n=e.split(":")[0];return e.includes(":")&&(t=e.split(":").slice(1).join(":").split(",")),{name:n,params:t}};let Y=Object.assign({},{generateMessage:({field:e})=>`${e} is not valid.`,bails:!0,validateOnBlur:!0,validateOnChange:!0,validateOnInput:!1,validateOnModelUpdate:!0});const Z=()=>Y,ee=e=>{Y=Object.assign(Object.assign({},Y),e)};async function te(e,t,r={}){const l=null==r?void 0:r.bails,a={name:(null==r?void 0:r.name)||"{field}",rules:t,label:null==r?void 0:r.label,bails:null==l||l,formData:(null==r?void 0:r.values)||{}},u=await async function(e,t){const r=e.rules;if(g(r)||V(r))return async function(e,t){const n=g(t.rules)?t.rules:ne(t.rules),r=await n.parse(e,{formData:t.formData}),l=[];for(const e of r.errors)e.errors.length&&l.push(...e.errors);return{value:r.value,errors:l}}(t,Object.assign(Object.assign({},e),{rules:r}));if(n(r)||Array.isArray(r)){const n={field:e.label||e.name,name:e.name,label:e.label,form:e.formData,value:t},l=Array.isArray(r)?r:[r],a=l.length,u=[];for(let r=0;r<a;r++){const a=l[r],i=await a(t,n);if(!("string"!=typeof i&&!Array.isArray(i)&&i)){if(Array.isArray(i))u.push(...i);else{const e="string"==typeof i?i:le(n);u.push(e)}if(e.bails)return{errors:u}}}return{errors:u}}const l=Object.assign(Object.assign({},e),{rules:H(r)}),a=[],u=Object.keys(l.rules),i=u.length;for(let n=0;n<i;n++){const r=u[n],i=await re(l,t,{name:r,params:l.rules[r]});if(i.error&&(a.push(i.error),e.bails))return{errors:a}}return{errors:a}}(a,e);return Object.assign(Object.assign({},u),{valid:!u.errors.length})}function ne(e){return{__type:"VVTypedSchema",async parse(t,n){var r;try{return{output:await e.validate(t,{abortEarly:!1,context:(null==n?void 0:n.formData)||{}}),errors:[]}}catch(e){if(!function(e){return!!e&&"ValidationError"===e.name}(e))throw e;if(!(null===(r=e.inner)||void 0===r?void 0:r.length)&&e.errors.length)return{errors:[{path:e.path,errors:e.errors}]};const t=e.inner.reduce(((e,t)=>{const n=t.path||"";return e[n]||(e[n]={errors:[],path:n}),e[n].errors.push(...t.errors),e}),{});return{errors:Object.values(t)}}}}}async function re(e,t,n){const r=(l=n.name,s[l]);var l;if(!r)throw new Error(`No such validator '${n.name}' exists.`);const a=function(e,t){const n=e=>y(e)?e(t):e;if(Array.isArray(e))return e.map(n);return Object.keys(e).reduce(((t,r)=>(t[r]=n(e[r]),t)),{})}(n.params,e.formData),u={field:e.label||e.name,name:e.name,label:e.label,value:t,form:e.formData,rule:Object.assign(Object.assign({},n),{params:a})},i=await r(t,a,u);return"string"==typeof i?{error:i}:{error:i?void 0:le(u)}}function le(e){const t=Z().generateMessage;return t?t(e):"Field is invalid"}async function ae(e,t,n){const r=_(e).map((async r=>{var l,a,u;const i=null===(l=null==n?void 0:n.names)||void 0===l?void 0:l[r],o=await te(M(t,r),e[r],{name:(null==i?void 0:i.name)||r,label:null==i?void 0:i.label,values:t,bails:null===(u=null===(a=null==n?void 0:n.bailsMap)||void 0===a?void 0:a[r])||void 0===u||u});return Object.assign(Object.assign({},o),{path:r})}));let l=!0;const a=await Promise.all(r),u={},i={};for(const e of a)u[e.path]={valid:e.valid,errors:e.errors},e.valid||(l=!1,i[e.path]=e.errors[0]);return{valid:l,results:u,errors:i,source:"schema"}}let ue=0;function ie(e,n){const{value:r,initialValue:l,setInitialValue:a}=function(e,n,r){const l=t.ref(t.unref(n));function a(){return r?M(r.initialValues.value,t.unref(e),t.unref(l)):t.unref(l)}function u(n){r?r.setFieldInitialValue(t.unref(e),n,!0):l.value=n}const i=t.computed(a);if(!r){return{value:t.ref(a()),initialValue:i,setInitialValue:u}}const o=function(e,n,r,l){if(t.isRef(e))return t.unref(e);if(void 0!==e)return e;return M(n.values,t.unref(l),t.unref(r))}(n,r,i,e);r.stageInitialValue(t.unref(e),o,!0);const s=t.computed({get:()=>M(r.values,t.unref(e)),set(n){r.setFieldValue(t.unref(e),n,!1)}});return{value:s,initialValue:i,setInitialValue:u}}(e,n.modelValue,n.form);if(!n.form){const{errors:o,setErrors:s}=function(){const e=t.ref([]);return{errors:e,setErrors:t=>{e.value=z(t)}}}(),d=ue>=Number.MAX_SAFE_INTEGER?0:++ue,c=function(e,n,r,l){const a=t.computed((()=>{var e,n,r;return null!==(r=null===(n=null===(e=t.toValue(l))||void 0===e?void 0:e.describe)||void 0===n?void 0:n.call(e).required)&&void 0!==r&&r})),u=t.reactive({touched:!1,pending:!1,valid:!0,required:a,validated:!!t.unref(r).length,initialValue:t.computed((()=>t.unref(n))),dirty:t.computed((()=>!k(t.unref(e),t.unref(n))))});return t.watch(r,(e=>{u.valid=!e.length}),{immediate:!0,flush:"sync"}),u}(r,l,o,n.schema);function v(e){var t;"value"in e&&(r.value=e.value),"errors"in e&&s(e.errors),"touched"in e&&(c.touched=null!==(t=e.touched)&&void 0!==t?t:c.touched),"initialValue"in e&&a(e.initialValue)}return{id:d,path:e,value:r,initialValue:l,meta:c,flags:{pendingUnmount:{[d]:!1},pendingReset:!1},errors:o,setState:v}}const u=n.form.createPathState(e,{bails:n.bails,label:n.label,type:n.type,validate:n.validate,schema:n.schema}),i=t.computed((()=>u.errors));return{id:Array.isArray(u.id)?u.id[u.id.length-1]:u.id,path:e,value:r,errors:i,meta:u,initialValue:l,flags:u.__flags,setState:function(l){var u,i,o;"value"in l&&(r.value=l.value),"errors"in l&&(null===(u=n.form)||void 0===u||u.setFieldError(t.unref(e),l.errors)),"touched"in l&&(null===(i=n.form)||void 0===i||i.setFieldTouched(t.unref(e),null!==(o=l.touched)&&void 0!==o&&o)),"initialValue"in l&&a(l.initialValue)}}}function oe(e,n,r){return b(null==r?void 0:r.type)?function(e,n,r){const l=(null==r?void 0:r.standalone)?void 0:x(v),a=null==r?void 0:r.checkedValue,u=null==r?void 0:r.uncheckedValue;function i(n){const i=n.handleChange,o=t.computed((()=>{const e=t.toValue(n.value),r=t.toValue(a);return Array.isArray(e)?e.findIndex((e=>k(e,r)))>=0:k(r,e)}));function s(s,d=!0){var c,v;if(o.value===(null===(c=null==s?void 0:s.target)||void 0===c?void 0:c.checked))return void(d&&n.validate());const f=t.toValue(e),p=null==l?void 0:l.getPathState(f),m=X(s);let h=null!==(v=t.toValue(a))&&void 0!==v?v:m;l&&(null==p?void 0:p.multiple)&&"checkbox"===p.type?h=R(M(l.values,f)||[],h,void 0):"checkbox"===(null==r?void 0:r.type)&&(h=R(t.toValue(n.value),h,t.toValue(u))),i(h,d)}return Object.assign(Object.assign({},n),{checked:o,checkedValue:a,uncheckedValue:u,handleChange:s})}return i(se(e,n,r))}(e,n,r):se(e,n,r)}function se(e,r,l){const{initialValue:a,validateOnMount:u,bails:i,type:s,checkedValue:d,label:f,validateOnValueUpdate:h,uncheckedValue:b,controlled:O,keepValueOnUnmount:j,syncVModel:A,form:F}=function(e){const n=()=>({initialValue:void 0,validateOnMount:!1,bails:!0,label:void 0,validateOnValueUpdate:!0,keepValueOnUnmount:void 0,syncVModel:!1,controlled:!0}),r=!!(null==e?void 0:e.syncVModel),l="string"==typeof(null==e?void 0:e.syncVModel)?e.syncVModel:(null==e?void 0:e.modelPropName)||"modelValue",a=r&&!("initialValue"in(e||{}))?de(t.getCurrentInstance(),l):null==e?void 0:e.initialValue;if(!e)return Object.assign(Object.assign({},n()),{initialValue:a});const u="valueProp"in e?e.valueProp:e.checkedValue,i="standalone"in e?!e.standalone:e.controlled,o=(null==e?void 0:e.modelPropName)||(null==e?void 0:e.syncVModel)||!1;return Object.assign(Object.assign(Object.assign({},n()),e||{}),{initialValue:a,controlled:null==i||i,checkedValue:u,syncVModel:o})}(l),S=O?x(v):void 0,E=F||S,w=t.computed((()=>o(t.toValue(e)))),I=t.computed((()=>{if(t.toValue(null==E?void 0:E.schema))return;const e=t.unref(r);return V(e)||g(e)||n(e)||Array.isArray(e)?e:H(e)})),C=!n(I.value)&&g(t.toValue(r)),{id:T,value:B,initialValue:P,meta:R,setState:U,errors:$,flags:z}=ie(w,{modelValue:a,form:E,bails:i,label:f,type:s,validate:I.value?G:void 0,schema:C?r:void 0}),q=t.computed((()=>$.value[0]));A&&function({prop:e,value:n,handleChange:r,shouldValidate:l}){const a=t.getCurrentInstance();if(!a||!e)return;const u="string"==typeof e?e:"modelValue",i=`update:${u}`;if(!(u in a.props))return;t.watch(n,(e=>{k(e,de(a,u))||a.emit(i,e)})),t.watch((()=>de(a,u)),(e=>{if(e===m&&void 0===n.value)return;const t=e===m?void 0:e;k(t,n.value)||r(t,l())}))}({value:B,prop:A,handleChange:J,shouldValidate:()=>h&&!z.pendingReset});async function W(e){var n,r;if(null==E?void 0:E.validateSchema){const{results:r}=await E.validateSchema(e);return null!==(n=r[t.toValue(w)])&&void 0!==n?n:{valid:!0,errors:[]}}return I.value?te(B.value,I.value,{name:t.toValue(w),label:t.toValue(f),values:null!==(r=null==E?void 0:E.values)&&void 0!==r?r:{},bails:i}):{valid:!0,errors:[]}}const K=D((async()=>(R.pending=!0,R.validated=!0,W("validated-only"))),(e=>(z.pendingUnmount[ne.id]||(U({errors:e.errors}),R.pending=!1,R.valid=e.valid),e))),L=D((async()=>W("silent")),(e=>(R.valid=e.valid,e)));function G(e){return"silent"===(null==e?void 0:e.mode)?L():K()}function J(e,t=!0){Z(X(e),t)}function Q(e){var t;const n=e&&"value"in e?e.value:P.value;U({value:c(n),initialValue:c(n),touched:null!==(t=null==e?void 0:e.touched)&&void 0!==t&&t,errors:(null==e?void 0:e.errors)||[]}),R.pending=!1,R.validated=!1,L()}t.onMounted((()=>{if(u)return K();E&&E.validateSchema||L()}));const Y=t.getCurrentInstance();function Z(e,t=!0){B.value=Y&&A?N(e,Y.props.modelModifiers):e;(t?K:L)()}const ee=t.computed({get:()=>B.value,set(e){Z(e,h)}}),ne={id:T,name:w,label:f,value:ee,meta:R,errors:$,errorMessage:q,type:s,checkedValue:d,uncheckedValue:b,bails:i,keepValueOnUnmount:j,resetField:Q,handleReset:()=>Q(),validate:G,handleChange:J,handleBlur:(e,t=!1)=>{R.touched=!0,t&&K()},setState:U,setTouched:function(e){R.touched=e},setErrors:function(e){U({errors:Array.isArray(e)?e:[e]})},setValue:Z};if(t.provide(p,ne),t.isRef(r)&&"function"!=typeof t.unref(r)&&t.watch(r,((e,t)=>{k(e,t)||(R.validated?K():L())}),{deep:!0}),!E)return ne;const re=t.computed((()=>{const e=I.value;return!e||n(e)||V(e)||g(e)||Array.isArray(e)?{}:Object.keys(e).reduce(((t,n)=>{const r=(l=e[n],Array.isArray(l)?l.filter(y):_(l).filter((e=>y(l[e]))).map((e=>l[e]))).map((e=>e.__locatorRef)).reduce(((e,t)=>{const n=M(E.values,t)||E.values[t];return void 0!==n&&(e[t]=n),e}),{});var l;return Object.assign(t,r),t}),{})}));return t.watch(re,((e,t)=>{if(!Object.keys(e).length)return;!k(e,t)&&(R.validated?K():L())})),t.onBeforeUnmount((()=>{var e;const n=null!==(e=t.toValue(ne.keepValueOnUnmount))&&void 0!==e?e:t.toValue(E.keepValuesOnUnmount),r=t.toValue(w);if(n||!E||z.pendingUnmount[ne.id])return void(null==E||E.removePathState(r,T));z.pendingUnmount[ne.id]=!0;const l=E.getPathState(r);if(Array.isArray(null==l?void 0:l.id)&&(null==l?void 0:l.multiple)?null==l?void 0:l.id.includes(ne.id):(null==l?void 0:l.id)===ne.id){if((null==l?void 0:l.multiple)&&Array.isArray(l.value)){const e=l.value.findIndex((e=>k(e,t.toValue(ne.checkedValue))));if(e>-1){const t=[...l.value];t.splice(e,1),E.setFieldValue(r,t)}Array.isArray(l.id)&&l.id.splice(l.id.indexOf(ne.id),1)}else E.unsetPathValue(t.toValue(w));E.removePathState(r,T)}})),ne}function de(e,t){if(e)return e.props[t]}function ce(e,t){let n=e.as||"";return e.as||t.slots.default||(n="input"),n}function ve(e,t){return b(t.attrs.type)?E(e,"modelValue")?e.modelValue:void 0:E(e,"modelValue")?e.modelValue:t.attrs.value}const fe=t.defineComponent({name:"Field",inheritAttrs:!1,props:{as:{type:[String,Object],default:void 0},name:{type:String,required:!0},rules:{type:[Object,String,Function],default:void 0},validateOnMount:{type:Boolean,default:!1},validateOnBlur:{type:Boolean,default:void 0},validateOnChange:{type:Boolean,default:void 0},validateOnInput:{type:Boolean,default:void 0},validateOnModelUpdate:{type:Boolean,default:void 0},bails:{type:Boolean,default:()=>Z().bails},label:{type:String,default:void 0},uncheckedValue:{type:null,default:void 0},modelValue:{type:null,default:m},modelModifiers:{type:null,default:()=>({})},"onUpdate:modelValue":{type:null,default:void 0},standalone:{type:Boolean,default:!1},keepValue:{type:Boolean,default:void 0}},setup(e,r){const l=t.toRef(e,"rules"),a=t.toRef(e,"name"),u=t.toRef(e,"label"),i=t.toRef(e,"uncheckedValue"),o=t.toRef(e,"keepValue"),{errors:s,value:d,errorMessage:c,validate:v,handleChange:f,handleBlur:p,setTouched:m,resetField:h,handleReset:y,meta:g,checked:V,setErrors:O,setValue:j}=oe(a,l,{validateOnMount:e.validateOnMount,bails:e.bails,standalone:e.standalone,type:r.attrs.type,initialValue:ve(e,r),checkedValue:r.attrs.value,uncheckedValue:i,label:u,validateOnValueUpdate:e.validateOnModelUpdate,keepValueOnUnmount:o,syncVModel:!0}),F=function(e,t=!0){f(e,t)},S=t.computed((()=>{const{validateOnInput:t,validateOnChange:l,validateOnBlur:a,validateOnModelUpdate:u}=function(e){var t,n,r,l;const{validateOnInput:a,validateOnChange:u,validateOnBlur:i,validateOnModelUpdate:o}=Z();return{validateOnInput:null!==(t=e.validateOnInput)&&void 0!==t?t:a,validateOnChange:null!==(n=e.validateOnChange)&&void 0!==n?n:u,validateOnBlur:null!==(r=e.validateOnBlur)&&void 0!==r?r:i,validateOnModelUpdate:null!==(l=e.validateOnModelUpdate)&&void 0!==l?l:o}}(e);const i={name:e.name,onBlur:function(e){p(e,a),n(r.attrs.onBlur)&&r.attrs.onBlur(e)},onInput:function(e){F(e,t),n(r.attrs.onInput)&&r.attrs.onInput(e)},onChange:function(e){F(e,l),n(r.attrs.onChange)&&r.attrs.onChange(e)},"onUpdate:modelValue":e=>F(e,u)};return i})),E=t.computed((()=>{const t=Object.assign({},S.value);b(r.attrs.type)&&V&&(t.checked=V.value);return A(ce(e,r),r.attrs)&&(t.value=d.value),t})),k=t.computed((()=>Object.assign(Object.assign({},S.value),{modelValue:d.value})));function w(){return{field:E.value,componentField:k.value,value:d.value,meta:g,errors:s.value,errorMessage:c.value,validate:v,resetField:h,handleChange:F,handleInput:e=>F(e,!1),handleReset:y,handleBlur:S.value.onBlur,setTouched:m,setErrors:O,setValue:j}}return r.expose({value:d,meta:g,errors:s,errorMessage:c,setErrors:O,setTouched:m,setValue:j,reset:h,validate:v,handleChange:f}),()=>{const n=t.resolveDynamicComponent(ce(e,r)),l=K(n,r,w);return n?t.h(n,Object.assign(Object.assign({},r.attrs),E.value),l):l}}});let pe=0;const me=["bails","fieldsCount","id","multiple","type","validate"];function he(e){const r=(null==e?void 0:e.initialValues)||{},l=Object.assign({},t.toValue(r)),a=t.unref(null==e?void 0:e.validationSchema);return a&&g(a)&&n(a.cast)?c(a.cast(l)||{}):c(l)}function ye(e){var r;const l=pe++,a=(null==e?void 0:e.name)||"Form";let u=0;const s=t.ref(!1),d=t.ref(!1),p=t.ref(0),m=[],h=t.reactive(he(e)),y=t.ref([]),b=t.ref({}),O=t.ref({}),j=function(e){let n=null,r=[];return function(...l){const a=t.nextTick((()=>{if(n!==a)return;const t=e(...l);r.forEach((e=>e(t))),r=[],n=null}));return n=a,new Promise((e=>r.push(e)))}}((()=>{O.value=y.value.reduce(((e,n)=>(e[o(t.toValue(n.path))]=n,e)),{})}));function A(e,t){const n=ee(e);if(n){if("string"==typeof e){const t=o(e);b.value[t]&&delete b.value[t]}n.errors=z(t),n.valid=!n.errors.length}else"string"==typeof e&&(b.value[o(e)]=z(t))}function S(e){_(e).forEach((t=>{A(t,e[t])}))}(null==e?void 0:e.initialErrors)&&S(e.initialErrors);const E=t.computed((()=>{const e=y.value.reduce(((e,n)=>(n.errors.length&&(e[t.toValue(n.path)]=n.errors),e)),{});return Object.assign(Object.assign({},b.value),e)})),w=t.computed((()=>_(E.value).reduce(((e,t)=>{const n=E.value[t];return(null==n?void 0:n.length)&&(e[t]=n[0]),e}),{}))),I=t.computed((()=>y.value.reduce(((e,n)=>(e[t.toValue(n.path)]={name:t.toValue(n.path)||"",label:n.label||""},e)),{}))),C=t.computed((()=>y.value.reduce(((e,n)=>{var r;return e[t.toValue(n.path)]=null===(r=n.bails)||void 0===r||r,e}),{}))),B=Object.assign({},(null==e?void 0:e.initialErrors)||{}),x=null!==(r=null==e?void 0:e.keepValuesOnUnmount)&&void 0!==r&&r,{initialValues:R,originalInitialValues:N,setInitialValues:$}=function(e,n,r){const l=he(r),a=t.ref(l),u=t.ref(c(l));function o(r,l){(null==l?void 0:l.force)?(a.value=c(r),u.value=c(r)):(a.value=i(c(a.value)||{},c(r)),u.value=i(c(u.value)||{},c(r))),(null==l?void 0:l.updateFields)&&e.value.forEach((e=>{if(e.touched)return;const r=M(a.value,t.toValue(e.path));T(n,t.toValue(e.path),c(r))}))}return{initialValues:a,originalInitialValues:u,setInitialValues:o}}(y,h,e),q=function(e,n,r,l){const a={touched:"some",pending:"some",valid:"every"},u=t.computed((()=>!k(n,t.unref(r))));function i(){const t=e.value;return _(a).reduce(((e,n)=>{const r=a[n];return e[n]=t[r]((e=>e[n])),e}),{})}const o=t.reactive(i());return t.watchEffect((()=>{const e=i();o.touched=e.touched,o.valid=e.valid,o.pending=e.pending})),t.computed((()=>Object.assign(Object.assign({initialValues:t.unref(r)},o),{valid:o.valid&&!_(l.value).length,dirty:u.value})))}(y,h,N,w),K=t.computed((()=>y.value.reduce(((e,n)=>{const r=M(h,t.toValue(n.path));return T(e,t.toValue(n.path),r),e}),{}))),L=null==e?void 0:e.validationSchema;function G(e,n){var r,l;const a=t.computed((()=>M(R.value,t.toValue(e)))),i=O.value[t.toValue(e)],o="checkbox"===(null==n?void 0:n.type)||"radio"===(null==n?void 0:n.type);if(i&&o){i.multiple=!0;const e=u++;return Array.isArray(i.id)?i.id.push(e):i.id=[i.id,e],i.fieldsCount++,i.__flags.pendingUnmount[e]=!1,i}const s=t.computed((()=>M(h,t.toValue(e)))),d=t.toValue(e),v=re.findIndex((e=>e===d));-1!==v&&re.splice(v,1);const f=t.computed((()=>{var r,l,a,u;const i=t.toValue(L);if(g(i))return null!==(l=null===(r=i.describe)||void 0===r?void 0:r.call(i,t.toValue(e)).required)&&void 0!==l&&l;const o=t.toValue(null==n?void 0:n.schema);return!!g(o)&&(null!==(u=null===(a=o.describe)||void 0===a?void 0:a.call(o).required)&&void 0!==u&&u)})),p=u++,m=t.reactive({id:p,path:e,touched:!1,pending:!1,valid:!0,validated:!!(null===(r=B[d])||void 0===r?void 0:r.length),required:f,initialValue:a,errors:t.shallowRef([]),bails:null!==(l=null==n?void 0:n.bails)&&void 0!==l&&l,label:null==n?void 0:n.label,type:(null==n?void 0:n.type)||"default",value:s,multiple:!1,__flags:{pendingUnmount:{[p]:!1},pendingReset:!1},fieldsCount:1,validate:null==n?void 0:n.validate,dirty:t.computed((()=>!k(t.unref(s),t.unref(a))))});return y.value.push(m),O.value[d]=m,j(),w.value[d]&&!B[d]&&t.nextTick((()=>{Ve(d,{mode:"silent"})})),t.isRef(e)&&t.watch(e,(e=>{j();const n=c(s.value);O.value[e]=m,t.nextTick((()=>{T(h,e,n)}))})),m}const H=U(je,5),J=U(je,5),Q=D((async e=>await("silent"===e?H():J())),((e,[n])=>{const r=_(ie.errorBag.value),l=[...new Set([..._(e.results),...y.value.map((e=>e.path)),...r])].sort().reduce(((r,l)=>{var a;const u=l,i=ee(u)||function(e){const n=y.value.filter((n=>e.startsWith(t.toValue(n.path))));return n.reduce(((e,t)=>e?t.path.length>e.path.length?t:e:t),void 0)}(u),o=(null===(a=e.results[u])||void 0===a?void 0:a.errors)||[],s=t.toValue(null==i?void 0:i.path)||u,d=function(e,t){if(!t)return e;return{valid:e.valid&&t.valid,errors:[...e.errors,...t.errors]}}({errors:o,valid:!o.length},r.results[s]);return r.results[s]=d,d.valid||(r.errors[s]=d.errors[0]),i&&b.value[s]&&delete b.value[s],i?(i.valid=d.valid,"silent"===n?r:"validated-only"!==n||i.validated?(A(i,d.errors),r):r):(A(s,o),r)}),{valid:e.valid,results:{},errors:{},source:e.source});return e.values&&(l.values=e.values,l.source=e.source),_(l.results).forEach((e=>{var t;const r=ee(e);r&&"silent"!==n&&("validated-only"!==n||r.validated)&&A(r,null===(t=l.results[e])||void 0===t?void 0:t.errors)})),l}));function Y(e){y.value.forEach(e)}function ee(e){const t="string"==typeof e?o(e):e;return"string"==typeof t?O.value[t]:t}let te,re=[];function le(e){return function(t,n){return function(r){return r instanceof Event&&(r.preventDefault(),r.stopPropagation()),Y((e=>e.touched=!0)),s.value=!0,p.value++,ge().then((l=>{const a=c(h);if(l.valid&&"function"==typeof t){const n=c(K.value);let u=e?n:a;return l.values&&(u="schema"===l.source?l.values:Object.assign({},u,l.values)),t(u,{evt:r,controlledValues:n,setErrors:S,setFieldError:A,setTouched:ve,setFieldTouched:ce,setValues:se,setFieldValue:oe,resetForm:ye,resetField:fe})}l.valid||"function"!=typeof n||n({values:a,evt:r,errors:l.errors,results:l.results})})).then((e=>(s.value=!1,e)),(e=>{throw s.value=!1,e}))}}}const ue=le(!1);ue.withControlled=le(!0);const ie={name:a,formId:l,values:h,controlledValues:K,errorBag:E,errors:w,schema:L,submitCount:p,meta:q,isSubmitting:s,isValidating:d,fieldArrays:m,keepValuesOnUnmount:x,validateSchema:t.unref(L)?Q:void 0,validate:ge,setFieldError:A,validateField:Ve,setFieldValue:oe,setValues:se,setErrors:S,setFieldTouched:ce,setTouched:ve,resetForm:ye,resetField:fe,handleSubmit:ue,useFieldModel:function(e){if(!Array.isArray(e))return de(e);return e.map((e=>de(e,!0)))},defineInputBinds:function(e,n){const[r,l]=Fe(e,n);function a(){l.value.onBlur()}function u(n){const r=X(n);oe(t.toValue(e),r,!1),l.value.onInput()}function i(n){const r=X(n);oe(t.toValue(e),r,!1),l.value.onChange()}return t.computed((()=>Object.assign(Object.assign({},l.value),{onBlur:a,onInput:u,onChange:i,value:r.value})))},defineComponentBinds:function(e,r){const[l,a]=Fe(e,r),u=ee(t.toValue(e));function i(e){l.value=e}return t.computed((()=>{const e=n(r)?r(W(u,me)):r||{};return Object.assign({[e.model||"modelValue"]:l.value,[`onUpdate:${e.model||"modelValue"}`]:i},a.value)}))},defineField:Fe,stageInitialValue:function(t,n,r=!1){Oe(t,n),T(h,t,n),r&&!(null==e?void 0:e.initialValues)&&T(N.value,t,c(n))},unsetInitialValue:be,setFieldInitialValue:Oe,createPathState:G,getPathState:ee,unsetPathValue:function(e){return re.push(e),te||(te=t.nextTick((()=>{[...re].sort().reverse().forEach((e=>{P(h,e)})),re=[],te=null}))),te},removePathState:function(e,n){const r=y.value.findIndex((t=>t.path===e&&(Array.isArray(t.id)?t.id.includes(n):t.id===n))),l=y.value[r];if(-1!==r&&l){if(t.nextTick((()=>{Ve(e,{mode:"silent",warn:!1})})),l.multiple&&l.fieldsCount&&l.fieldsCount--,Array.isArray(l.id)){const e=l.id.indexOf(n);e>=0&&l.id.splice(e,1),delete l.__flags.pendingUnmount[n]}(!l.multiple||l.fieldsCount<=0)&&(y.value.splice(r,1),be(e),j(),delete O.value[e])}},initialValues:R,getAllPathStates:()=>y.value,destroyPath:function(e){_(O.value).forEach((t=>{t.startsWith(e)&&delete O.value[t]})),y.value=y.value.filter((t=>!t.path.startsWith(e))),t.nextTick((()=>{j()}))},isFieldTouched:function(e){const t=ee(e);if(t)return t.touched;return y.value.filter((t=>t.path.startsWith(e))).some((e=>e.touched))},isFieldDirty:function(e){const t=ee(e);if(t)return t.dirty;return y.value.filter((t=>t.path.startsWith(e))).some((e=>e.dirty))},isFieldValid:function(e){const t=ee(e);if(t)return t.valid;return y.value.filter((t=>t.path.startsWith(e))).every((e=>e.valid))}};function oe(e,t,n=!0){const r=c(t),l="string"==typeof e?e:e.path;ee(l)||G(l),T(h,l,r),n&&Ve(l)}function se(e,t=!0){i(h,e),m.forEach((e=>e&&e.reset())),t&&ge()}function de(e,n){const r=ee(t.toValue(e))||G(e);return t.computed({get:()=>r.value,set(r){var l;oe(t.toValue(e),r,null!==(l=t.toValue(n))&&void 0!==l&&l)}})}function ce(e,t){const n=ee(e);n&&(n.touched=t)}function ve(e){"boolean"!=typeof e?_(e).forEach((t=>{ce(t,!!e[t])})):Y((t=>{t.touched=e}))}function fe(e,n){var r;const l=n&&"value"in n?n.value:M(R.value,e),a=ee(e);a&&(a.__flags.pendingReset=!0),Oe(e,c(l),!0),oe(e,l,!1),ce(e,null!==(r=null==n?void 0:n.touched)&&void 0!==r&&r),A(e,(null==n?void 0:n.errors)||[]),t.nextTick((()=>{a&&(a.__flags.pendingReset=!1)}))}function ye(e,r){let l=c((null==e?void 0:e.values)?e.values:N.value);l=(null==r?void 0:r.force)?l:i(N.value,l),l=g(L)&&n(L.cast)?L.cast(l):l,$(l,{force:null==r?void 0:r.force}),Y((n=>{var r;n.__flags.pendingReset=!0,n.validated=!1,n.touched=(null===(r=null==e?void 0:e.touched)||void 0===r?void 0:r[t.toValue(n.path)])||!1,oe(t.toValue(n.path),M(l,t.toValue(n.path)),!1),A(t.toValue(n.path),void 0)})),(null==r?void 0:r.force)?function(e,t=!0){_(h).forEach((e=>{delete h[e]})),_(e).forEach((t=>{oe(t,e[t],!1)})),t&&ge()}(l,!1):se(l,!1),S((null==e?void 0:e.errors)||{}),p.value=(null==e?void 0:e.submitCount)||0,t.nextTick((()=>{ge({mode:"silent"}),Y((e=>{e.__flags.pendingReset=!1}))}))}async function ge(e){const n=(null==e?void 0:e.mode)||"force";if("force"===n&&Y((e=>e.validated=!0)),ie.validateSchema)return ie.validateSchema(n);d.value=!0;const r=await Promise.all(y.value.map((n=>n.validate?n.validate(e).then((e=>({key:t.toValue(n.path),valid:e.valid,errors:e.errors,value:e.value}))):Promise.resolve({key:t.toValue(n.path),valid:!0,errors:[],value:void 0}))));d.value=!1;const l={},a={},u={};for(const e of r)l[e.key]={valid:e.valid,errors:e.errors},e.value&&T(u,e.key,e.value),e.errors.length&&(a[e.key]=e.errors[0]);return{valid:r.every((e=>e.valid)),results:l,errors:a,values:u,source:"fields"}}async function Ve(e,t){const n=ee(e);if(n&&"silent"!==(null==t?void 0:t.mode)&&(n.validated=!0),L){const{results:n}=await Q((null==t?void 0:t.mode)||"validated-only");return n[e]||{errors:[],valid:!0}}return(null==n?void 0:n.validate)?n.validate(t):(!n&&(null==t?void 0:t.warn),Promise.resolve({errors:[],valid:!0}))}function be(e){P(R.value,e)}function Oe(e,t,n=!1){T(R.value,e,c(t)),n&&T(N.value,e,c(t))}async function je(){const e=t.unref(L);if(!e)return{valid:!0,results:{},errors:{},source:"none"};d.value=!0;const n=V(e)||g(e)?await async function(e,t){const n=g(e)?e:ne(e),r=await n.parse(c(t),{formData:c(t)}),l={},a={};for(const e of r.errors){const t=e.errors,n=(e.path||"").replace(/\["(\d+)"\]/g,((e,t)=>`[${t}]`));l[n]={valid:!t.length,errors:t},t.length&&(a[n]=t[0])}return{valid:!r.errors.length,results:l,errors:a,values:r.value,source:"schema"}}(e,h):await ae(e,h,{names:I.value,bailsMap:C.value});return d.value=!1,n}const Ae=ue(((e,{evt:t})=>{F(t)&&t.target.submit()}));function Fe(e,r){const l=n(r)||null==r?void 0:r.label,a=ee(t.toValue(e))||G(e,{label:l}),u=()=>n(r)?r(W(a,me)):r||{};function i(){var e;a.touched=!0;(null!==(e=u().validateOnBlur)&&void 0!==e?e:Z().validateOnBlur)&&Ve(t.toValue(a.path))}function o(){var e;(null!==(e=u().validateOnInput)&&void 0!==e?e:Z().validateOnInput)&&t.nextTick((()=>{Ve(t.toValue(a.path))}))}function s(){var e;(null!==(e=u().validateOnChange)&&void 0!==e?e:Z().validateOnChange)&&t.nextTick((()=>{Ve(t.toValue(a.path))}))}const d=t.computed((()=>{const e={onChange:s,onInput:o,onBlur:i};return n(r)?Object.assign(Object.assign({},e),r(W(a,me)).props||{}):(null==r?void 0:r.props)?Object.assign(Object.assign({},e),r.props(W(a,me))):e})),c=de(e,(()=>{var e,t,n;return null===(n=null!==(e=u().validateOnModelUpdate)&&void 0!==e?e:null===(t=Z())||void 0===t?void 0:t.validateOnModelUpdate)||void 0===n||n}));return[c,d]}t.onMounted((()=>{(null==e?void 0:e.initialErrors)&&S(e.initialErrors),(null==e?void 0:e.initialTouched)&&ve(e.initialTouched),(null==e?void 0:e.validateOnMount)?ge():ie.validateSchema&&ie.validateSchema("silent")})),t.isRef(L)&&t.watch(L,(()=>{var e;null===(e=ie.validateSchema)||void 0===e||e.call(ie,"validated-only")})),t.provide(v,ie);const Se=Object.assign(Object.assign({},ie),{values:t.readonly(h),handleReset:()=>ye(),submitForm:Ae});return t.provide(f,Se),Se}const ge=t.defineComponent({name:"Form",inheritAttrs:!1,props:{as:{type:null,default:"form"},validationSchema:{type:Object,default:void 0},initialValues:{type:Object,default:void 0},initialErrors:{type:Object,default:void 0},initialTouched:{type:Object,default:void 0},validateOnMount:{type:Boolean,default:!1},onSubmit:{type:Function,default:void 0},onInvalidSubmit:{type:Function,default:void 0},keepValues:{type:Boolean,default:!1},name:{type:String,default:"Form"}},setup(e,n){const r=t.toRef(e,"validationSchema"),l=t.toRef(e,"keepValues"),{errors:a,errorBag:u,values:i,meta:o,isSubmitting:s,isValidating:d,submitCount:v,controlledValues:f,validate:p,validateField:m,handleReset:h,resetForm:y,handleSubmit:g,setErrors:V,setFieldError:b,setFieldValue:O,setValues:j,setFieldTouched:A,setTouched:E,resetField:k}=ye({validationSchema:r.value?r:void 0,initialValues:e.initialValues,initialErrors:e.initialErrors,initialTouched:e.initialTouched,validateOnMount:e.validateOnMount,keepValuesOnUnmount:l,name:e.name}),w=g(((e,{evt:t})=>{F(t)&&t.target.submit()}),e.onInvalidSubmit),I=e.onSubmit?g(e.onSubmit,e.onInvalidSubmit):w;function C(e){S(e)&&e.preventDefault(),h(),"function"==typeof n.attrs.onReset&&n.attrs.onReset()}function M(t,n){return g("function"!=typeof t||n?n:t,e.onInvalidSubmit)(t)}function T(){return c(i)}function B(){return c(o.value)}function P(){return c(a.value)}function _(){return{meta:o.value,errors:a.value,errorBag:u.value,values:i,isSubmitting:s.value,isValidating:d.value,submitCount:v.value,controlledValues:f.value,validate:p,validateField:m,handleSubmit:M,handleReset:h,submitForm:w,setErrors:V,setFieldError:b,setFieldValue:O,setValues:j,setFieldTouched:A,setTouched:E,resetForm:y,resetField:k,getValues:T,getMeta:B,getErrors:P}}return n.expose({setFieldError:b,setErrors:V,setFieldValue:O,setValues:j,setFieldTouched:A,setTouched:E,resetForm:y,validate:p,validateField:m,resetField:k,getValues:T,getMeta:B,getErrors:P,values:i,meta:o,errors:a}),function(){const r="form"===e.as?e.as:e.as?t.resolveDynamicComponent(e.as):null,l=K(r,n,_);if(!r)return l;const a="form"===r?{novalidate:!0}:{};return t.h(r,Object.assign(Object.assign(Object.assign({},a),n.attrs),{onSubmit:I,onReset:C}),l)}}}),Ve=ge;function be(e){const n=x(v,void 0),l=t.ref([]),a=()=>{},u={fields:l,remove:a,push:a,swap:a,insert:a,update:a,replace:a,prepend:a,move:a};if(!n)return u;if(!t.unref(e))return u;const i=n.fieldArrays.find((n=>t.unref(n.path)===t.unref(e)));if(i)return i;let o=0;function s(){return M(null==n?void 0:n.values,t.toValue(e),[])||[]}function d(){const e=s();Array.isArray(e)&&(l.value=e.map(((e,t)=>p(e,t,l.value))),f())}function f(){const e=l.value.length;for(let t=0;t<e;t++){const n=l.value[t];n.isFirst=0===t,n.isLast=t===e-1}}function p(a,u,i){if(i&&!r(u)&&i[u])return i[u];const s=o++,d={key:s,value:$({get(){const r=M(null==n?void 0:n.values,t.toValue(e),[])||[],u=l.value.findIndex((e=>e.key===s));return-1===u?a:r[u]},set(e){const t=l.value.findIndex((e=>e.key===s));-1!==t&&h(t,e)}}),isFirst:!1,isLast:!1};return d}function m(){f(),null==n||n.validate({mode:"silent"})}function h(r,l){const a=t.toValue(e),u=M(null==n?void 0:n.values,a);!Array.isArray(u)||u.length-1<r||(T(n.values,`${a}[${r}]`,l),null==n||n.validate({mode:"validated-only"}))}d();const y={fields:l,remove:function(r){const a=t.toValue(e),u=M(null==n?void 0:n.values,a);if(!u||!Array.isArray(u))return;const i=[...u];i.splice(r,1);const o=a+`[${r}]`;n.destroyPath(o),n.unsetInitialValue(o),T(n.values,a,i),l.value.splice(r,1),m()},push:function(a){const u=c(a),i=t.toValue(e),o=M(null==n?void 0:n.values,i),s=r(o)?[]:o;if(!Array.isArray(s))return;const d=[...s];d.push(u),n.stageInitialValue(i+`[${d.length-1}]`,u),T(n.values,i,d),l.value.push(p(u)),m()},swap:function(r,a){const u=t.toValue(e),i=M(null==n?void 0:n.values,u);if(!Array.isArray(i)||!(r in i)||!(a in i))return;const o=[...i],s=[...l.value],d=o[r];o[r]=o[a],o[a]=d;const c=s[r];s[r]=s[a],s[a]=c,T(n.values,u,o),l.value=s,f()},insert:function(r,a){const u=c(a),i=t.toValue(e),o=M(null==n?void 0:n.values,i);if(!Array.isArray(o)||o.length<r)return;const s=[...o],d=[...l.value];s.splice(r,0,u),d.splice(r,0,p(u)),T(n.values,i,s),l.value=d,m()},update:h,replace:function(r){const l=t.toValue(e);n.stageInitialValue(l,r),T(n.values,l,r),d(),m()},prepend:function(a){const u=c(a),i=t.toValue(e),o=M(null==n?void 0:n.values,i),s=r(o)?[]:o;if(!Array.isArray(s))return;const d=[u,...s];T(n.values,i,d),n.stageInitialValue(i+"[0]",u),l.value.unshift(p(u)),m()},move:function(a,u){const i=t.toValue(e),o=M(null==n?void 0:n.values,i),s=r(o)?[]:[...o];if(!Array.isArray(o)||!(a in o)||!(u in o))return;const d=[...l.value],c=d[a];d.splice(a,1),d.splice(u,0,c);const v=s[a];s.splice(a,1),s.splice(u,0,v),T(n.values,i,s),l.value=d,m()}};return n.fieldArrays.push(Object.assign({path:e,reset:d},y)),t.onBeforeUnmount((()=>{const r=n.fieldArrays.findIndex((n=>t.toValue(n.path)===t.toValue(e)));r>=0&&n.fieldArrays.splice(r,1)})),t.watch(s,(e=>{k(e,l.value.map((e=>e.value)))||d()})),y}const Oe=t.defineComponent({name:"FieldArray",inheritAttrs:!1,props:{name:{type:String,required:!0}},setup(e,t){const{push:n,remove:r,swap:l,insert:a,replace:u,update:i,prepend:o,move:s,fields:d}=be((()=>e.name));function c(){return{fields:d.value,push:n,remove:r,swap:l,insert:a,update:i,replace:u,prepend:o,move:s}}return t.expose({push:n,remove:r,swap:l,insert:a,update:i,replace:u,prepend:o,move:s}),()=>K(void 0,t,c)}}),je=t.defineComponent({name:"ErrorMessage",props:{as:{type:String,default:void 0},name:{type:String,required:!0}},setup(e,n){const r=t.inject(v,void 0),l=t.computed((()=>null==r?void 0:r.errors.value[e.name]));function a(){return{message:l.value}}return()=>{if(!l.value)return;const r=e.as?t.resolveDynamicComponent(e.as):e.as,u=K(r,n,a),i=Object.assign({role:"alert"},n.attrs);return r||!Array.isArray(u)&&u||!(null==u?void 0:u.length)?!Array.isArray(u)&&u||(null==u?void 0:u.length)?t.h(r,i,u):t.h(r||"span",i,l.value):u}}});return e.ErrorMessage=je,e.Field=fe,e.FieldArray=Oe,e.FieldContextKey=p,e.Form=Ve,e.FormContextKey=v,e.IS_ABSENT=m,e.PublicFormContextKey=f,e.cleanupNonNestedPath=C,e.configure=ee,e.defineRule=function(e,t){!function(e,t){if(n(t))return;throw new Error(`Extension Error: The validator '${e}' must be a function.`)}(e,t),s[e]=t},e.isNotNestedPath=O,e.normalizeRules=H,e.useField=oe,e.useFieldArray=be,e.useFieldError=function(e){const n=x(v),r=e?void 0:t.inject(p);return t.computed((()=>e?null==n?void 0:n.errors.value[t.toValue(e)]:null==r?void 0:r.errorMessage.value))},e.useFieldValue=function(e){const n=x(v),r=e?void 0:t.inject(p);return t.computed((()=>e?M(null==n?void 0:n.values,t.toValue(e)):t.toValue(null==r?void 0:r.value)))},e.useForm=ye,e.useFormContext=function(){return t.inject(f)},e.useFormErrors=function(){const e=x(v);return t.computed((()=>(null==e?void 0:e.errors.value)||{}))},e.useFormValues=function(){const e=x(v);return t.computed((()=>(null==e?void 0:e.values)||{}))},e.useIsFieldDirty=function(e){const n=q(e);return t.computed((()=>{var e,t;return!!n&&(null!==(t="meta"in n?n.meta.dirty:null===(e=null==n?void 0:n.value)||void 0===e?void 0:e.dirty)&&void 0!==t&&t)}))},e.useIsFieldTouched=function(e){const n=q(e);return t.computed((()=>{var e,t;return!!n&&(null!==(t="meta"in n?n.meta.touched:null===(e=null==n?void 0:n.value)||void 0===e?void 0:e.touched)&&void 0!==t&&t)}))},e.useIsFieldValid=function(e){const n=q(e);return t.computed((()=>{var e,t;return!!n&&(null!==(t="meta"in n?n.meta.valid:null===(e=null==n?void 0:n.value)||void 0===e?void 0:e.valid)&&void 0!==t&&t)}))},e.useIsFormDirty=function(){const e=x(v);return t.computed((()=>{var t;return null!==(t=null==e?void 0:e.meta.value.dirty)&&void 0!==t&&t}))},e.useIsFormTouched=function(){const e=x(v);return t.computed((()=>{var t;return null!==(t=null==e?void 0:e.meta.value.touched)&&void 0!==t&&t}))},e.useIsFormValid=function(){const e=x(v);return t.computed((()=>{var t;return null!==(t=null==e?void 0:e.meta.value.valid)&&void 0!==t&&t}))},e.useIsSubmitting=function(){const e=x(v);return t.computed((()=>{var t;return null!==(t=null==e?void 0:e.isSubmitting.value)&&void 0!==t&&t}))},e.useIsValidating=function(){const e=x(v);return t.computed((()=>{var t;return null!==(t=null==e?void 0:e.isValidating.value)&&void 0!==t&&t}))},e.useResetForm=function(){const e=x(v);return function(t,n){if(e)return e.resetForm(t,n)}},e.useSetFieldError=function(e){const n=x(v),r=e?void 0:t.inject(p);return function(l){e&&n?n.setFieldError(t.toValue(e),l):r&&r.setErrors(l||[])}},e.useSetFieldTouched=function(e){const n=x(v),r=e?void 0:t.inject(p);return function(l){e&&n?n.setFieldTouched(t.toValue(e),l):r&&r.setTouched(l)}},e.useSetFieldValue=function(e){const n=x(v),r=e?void 0:t.inject(p);return function(l,a=!0){e&&n?n.setFieldValue(t.toValue(e),l,a):r&&r.setValue(l,a)}},e.useSetFormErrors=function(){const e=x(v);return function(t){e&&e.setErrors(t)}},e.useSetFormTouched=function(){const e=x(v);return function(t){e&&e.setTouched(t)}},e.useSetFormValues=function(){const e=x(v);return function(t,n=!0){e&&e.setValues(t,n)}},e.useSubmitCount=function(){const e=x(v);return t.computed((()=>{var t;return null!==(t=null==e?void 0:e.submitCount.value)&&void 0!==t?t:0}))},e.useSubmitForm=function(e){const t=x(v),n=t?t.handleSubmit(e):void 0;return function(e){if(n)return n(e)}},e.useValidateField=function(e){const n=x(v),r=e?void 0:t.inject(p);return function(){return r?r.validate():n&&e?null==n?void 0:n.validateField(t.toValue(e)):Promise.resolve({errors:[],valid:!0})}},e.useValidateForm=function(){const e=x(v);return function(){return e?e.validate():Promise.resolve({results:{},errors:{},valid:!0,source:"none"})}},e.validate=te,e.validateObject=ae,e}({},Vue);