{"name": "vue-router", "version": "4.6.3", "main": "index.js", "unpkg": "dist/vue-router.global.js", "jsdelivr": "dist/vue-router.global.js", "module": "dist/vue-router.mjs", "types": "dist/vue-router.d.mts", "exports": {".": {"types": "./dist/vue-router.d.mts", "node": {"import": {"production": "./vue-router.node.mjs", "development": "./vue-router.node.mjs", "default": "./vue-router.node.mjs"}, "require": {"production": "./dist/vue-router.prod.cjs", "development": "./dist/vue-router.cjs", "default": "./index.js"}}, "import": "./dist/vue-router.mjs", "require": "./index.js"}, "./dist/*": "./dist/*", "./vetur/*": "./vetur/*", "./package.json": "./package.json", "./experimental": "./dist/experimental/index.mjs", "./auto-resolver": {"types": "./vue-router-auto-resolver.d.mts"}, "./auto-routes": {"types": "./vue-router-auto-routes.d.ts", "node": {"import": {"production": "./vue-router.node.mjs", "development": "./vue-router.node.mjs", "default": "./vue-router.node.mjs"}, "require": {"production": "./dist/vue-router.prod.cjs", "development": "./dist/vue-router.cjs", "default": "./index.js"}}, "import": "./dist/vue-router.mjs", "require": "./index.js"}, "./auto": {"types": "./vue-router-auto.d.ts", "node": {"import": {"production": "./vue-router.node.mjs", "development": "./vue-router.node.mjs", "default": "./vue-router.node.mjs"}, "require": {"production": "./dist/vue-router.prod.cjs", "development": "./dist/vue-router.cjs", "default": "./index.js"}}, "import": "./dist/vue-router.mjs", "require": "./index.js"}}, "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/posva", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/router.git"}, "bugs": {"url": "https://github.com/vuejs/router/issues"}, "homepage": "https://router.vuejs.org", "files": ["vue-router-auto-resolver.d.mts", "index.js", "vue-router.node.mjs", "vue-router-auto.d.ts", "vue-router-auto-routes.d.ts", "dist/**/*.{js,cjs,mjs}", "dist/**/*.d.{ts,mts}", "vetur/tags.json", "vetur/attributes.json", "README.md"], "peerDependencies": {"vue": "^3.5.0"}, "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}, "dependencies": {"@vue/devtools-api": "^6.6.4"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.7", "@rollup/plugin-node-resolve": "^16.0.3", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@types/jsdom": "^21.1.7", "@types/nightwatch": "^2.3.32", "@typescript/native-preview": "7.0.0-dev.20251013.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/compiler-sfc": "~3.5.22", "@vue/server-renderer": "~3.5.22", "@vue/test-utils": "^2.4.6", "browserstack-local": "^1.5.8", "chromedriver": "^138.0.5", "connect-history-api-fallback": "^1.6.0", "dotenv": "^17.2.3", "faked-promise": "^2.2.2", "geckodriver": "^6.0.2", "happy-dom": "^20.0.0", "nightwatch": "^3.12.2", "nightwatch-helpers": "^1.2.0", "rimraf": "^6.0.1", "rollup": "^4.52.4", "rollup-plugin-typescript2": "^0.36.0", "tsdown": "^0.15.7", "tsup": "^8.5.0", "vite": "^7.1.10", "vue": "~3.6.0-alpha.2"}, "scripts": {"dev": "vitest --ui", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "build": "tsdown", "build:old": "rimraf dist && rollup -c rollup.config.mjs", "build:playground": "vue-tsc --noEmit && vite build --config playground/vite.config.ts", "build:e2e": "vue-tsc --noEmit && vite build --config e2e/vite.config.mjs", "build:size": "pnpm run build && rollup -c size-checks/rollup.config.mjs", "dev:e2e": "vite --config e2e/vite.config.mjs", "test:types": "tsc --build tsconfig.json", "test:unit": "vitest --coverage run", "test": "pnpm run build && pnpm run test:types && pnpm run test:unit && pnpm run test:e2e", "test:e2e": "pnpm run test:e2e:headless", "test:e2e:headless": "node e2e/runner.mjs --env chrome-headless", "test:e2e:native": "node e2e/runner.mjs --env chrome", "test:e2e:ci": "node e2e/runner.mjs --env chrome-headless --retries 2", "test:e2e:bs": "node e2e/runner.mjs --local -e android5 --tag browserstack", "test:e2e:bs-test": "node e2e/runner.mjs --local --env browserstack.local_chrome --tag browserstack"}}