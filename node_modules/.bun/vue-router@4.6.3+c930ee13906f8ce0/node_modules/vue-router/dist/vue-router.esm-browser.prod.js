/*!
* vue-router v4.6.3
* (c) 2025 <PERSON>
* @license MIT
*/
import{computed as e,defineComponent as t,h as n,inject as r,nextTick as i,onActivated as a,onDeactivated as o,onUnmounted as s,provide as c,reactive as l,ref as u,shallowReactive as d,shallowRef as f,unref as p,watch as ee}from"vue";const m=typeof document<`u`;function h(e){return typeof e==`object`||`displayName`in e||`props`in e||`__vccOpts`in e}function te(e){return e.__esModule||e[Symbol.toStringTag]===`Module`||e.default&&h(e.default)}const g=Object.assign;function ne(e,t){let n={};for(let r in t){let i=t[r];n[r]=v(i)?i.map(e):e(i)}return n}const _=()=>{},v=Array.isArray;function re(e,t){let n={};for(let r in e)n[r]=r in t?t[r]:e[r];return n}const ie=/#/g,ae=/&/g,oe=/\//g,y=/=/g,b=/\?/g,x=/\+/g,S=/%5B/g,se=/%5D/g,ce=/%5E/g,C=/%60/g,le=/%7B/g,w=/%7C/g,T=/%7D/g,ue=/%20/g;function E(e){return e==null?``:encodeURI(``+e).replace(w,`|`).replace(S,`[`).replace(se,`]`)}function de(e){return E(e).replace(le,`{`).replace(T,`}`).replace(ce,`^`)}function D(e){return E(e).replace(x,`%2B`).replace(ue,`+`).replace(ie,`%23`).replace(ae,`%26`).replace(C,"`").replace(le,`{`).replace(T,`}`).replace(ce,`^`)}function fe(e){return D(e).replace(y,`%3D`)}function O(e){return E(e).replace(ie,`%23`).replace(b,`%3F`)}function pe(e){return O(e).replace(oe,`%2F`)}function k(e){if(e==null)return null;try{return decodeURIComponent(``+e)}catch{}return``+e}const me=/\/$/,A=e=>e.replace(me,``);function he(e,t,n=`/`){let r,i={},a=``,o=``,s=t.indexOf(`#`),c=t.indexOf(`?`);return c=s>=0&&c>s?-1:c,c>=0&&(r=t.slice(0,c),a=t.slice(c,s>0?s:t.length),i=e(a.slice(1))),s>=0&&(r||=t.slice(0,s),o=t.slice(s,t.length)),r=F(r??t,n),{fullPath:r+a+o,path:r,query:i,hash:k(o)}}function ge(e,t){let n=t.query?e(t.query):``;return t.path+(n&&`?`)+n+(t.hash||``)}function j(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||`/`}function _e(e,t,n){let r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&M(t.matched[r],n.matched[i])&&N(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function M(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function N(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let n in e)if(!ve(e[n],t[n]))return!1;return!0}function ve(e,t){return v(e)?P(e,t):v(t)?P(t,e):e===t}function P(e,t){return v(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):e.length===1&&e[0]===t}function F(e,t){if(e.startsWith(`/`))return e;if(!e)return t;let n=t.split(`/`),r=e.split(`/`),i=r[r.length-1];(i===`..`||i===`.`)&&r.push(``);let a=n.length-1,o,s;for(o=0;o<r.length;o++)if(s=r[o],s!==`.`)if(s===`..`)a>1&&a--;else break;return n.slice(0,a).join(`/`)+`/`+r.slice(o).join(`/`)}const I={path:`/`,name:void 0,params:{},query:{},hash:``,fullPath:`/`,matched:[],meta:{},redirectedFrom:void 0};let L=function(e){return e.pop=`pop`,e.push=`push`,e}({}),R=function(e){return e.back=`back`,e.forward=`forward`,e.unknown=``,e}({});function z(e){if(!e)if(m){let t=document.querySelector(`base`);e=t&&t.getAttribute(`href`)||`/`,e=e.replace(/^\w+:\/\/[^\/]+/,``)}else e=`/`;return e[0]!==`/`&&e[0]!==`#`&&(e=`/`+e),A(e)}const B=/^[^#]+#/;function ye(e,t){return e.replace(B,`#`)+t}function be(e,t){let n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const V=()=>({left:window.scrollX,top:window.scrollY});function xe(e){let t;if(`el`in e){let n=e.el,r=typeof n==`string`&&n.startsWith(`#`),i=typeof n==`string`?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=be(i,e)}else t=e;`scrollBehavior`in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left==null?window.scrollX:t.left,t.top==null?window.scrollY:t.top)}function Se(e,t){return(history.state?history.state.position-t:-1)+e}const Ce=new Map;function we(e,t){Ce.set(e,t)}function Te(e){let t=Ce.get(e);return Ce.delete(e),t}let Ee=()=>location.protocol+`//`+location.host;function De(e,t){let{pathname:n,search:r,hash:i}=t,a=e.indexOf(`#`);if(a>-1){let t=i.includes(e.slice(a))?e.slice(a).length:1,n=i.slice(t);return n[0]!==`/`&&(n=`/`+n),j(n,``)}return j(n,e)+r+i}function Oe(e,t,n,r){let i=[],a=[],o=null,s=({state:a})=>{let s=De(e,location),c=n.value,l=t.value,u=0;if(a){if(n.value=s,t.value=a,o&&o===c){o=null;return}u=l?a.position-l.position:0}else r(s);i.forEach(e=>{e(n.value,c,{delta:u,type:L.pop,direction:u?u>0?R.forward:R.back:R.unknown})})};function c(){o=n.value}function l(e){i.push(e);let t=()=>{let t=i.indexOf(e);t>-1&&i.splice(t,1)};return a.push(t),t}function u(){if(document.visibilityState===`hidden`){let{history:e}=window;if(!e.state)return;e.replaceState(g({},e.state,{scroll:V()}),``)}}function d(){for(let e of a)e();a=[],window.removeEventListener(`popstate`,s),window.removeEventListener(`pagehide`,u),document.removeEventListener(`visibilitychange`,u)}return window.addEventListener(`popstate`,s),window.addEventListener(`pagehide`,u),document.addEventListener(`visibilitychange`,u),{pauseListeners:c,listen:l,destroy:d}}function ke(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?V():null}}function Ae(e){let{history:t,location:n}=window,r={value:De(e,n)},i={value:t.state};i.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(r,a,o){let s=e.indexOf(`#`),c=s>-1?(n.host&&document.querySelector(`base`)?e:e.slice(s))+r:Ee()+e+r;try{t[o?`replaceState`:`pushState`](a,``,c),i.value=a}catch(e){console.error(e),n[o?`replace`:`assign`](c)}}function o(e,n){a(e,g({},t.state,ke(i.value.back,e,i.value.forward,!0),n,{position:i.value.position}),!0),r.value=e}function s(e,n){let o=g({},i.value,t.state,{forward:e,scroll:V()});a(o.current,o,!0),a(e,g({},ke(r.value,e,null),{position:o.position+1},n),!1),r.value=e}return{location:r,state:i,push:s,replace:o}}function je(e){e=z(e);let t=Ae(e),n=Oe(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}let i=g({location:``,base:e,go:r,createHref:ye.bind(null,e)},t,n);return Object.defineProperty(i,`location`,{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,`state`,{enumerable:!0,get:()=>t.state.value}),i}function Me(e=``){let t=[],n=[[``,{}]],r=0;e=z(e);function i(e,t={}){r++,r!==n.length&&n.splice(r),n.push([e,t])}function a(e,n,{direction:r,delta:i}){let a={direction:r,delta:i,type:L.pop};for(let r of t)r(e,n,a)}let o={location:``,state:{},base:e,createHref:ye.bind(null,e),replace(e,t){n.splice(r--,1),i(e,t)},push(e,t){i(e,t)},listen(e){return t.push(e),()=>{let n=t.indexOf(e);n>-1&&t.splice(n,1)}},destroy(){t=[],n=[[``,{}]],r=0},go(e,t=!0){let i=this.location,o=e<0?R.back:R.forward;r=Math.max(0,Math.min(r+e,n.length-1)),t&&a(this.location,i,{direction:o,delta:e})}};return Object.defineProperty(o,`location`,{enumerable:!0,get:()=>n[r][0]}),Object.defineProperty(o,`state`,{enumerable:!0,get:()=>n[r][1]}),o}function Ne(e){return e=location.host?e||location.pathname+location.search:``,e.includes(`#`)||(e+=`#`),je(e)}function Pe(e){return typeof e==`string`||e&&typeof e==`object`}function Fe(e){return typeof e==`string`||typeof e==`symbol`}let H=function(e){return e[e.MATCHER_NOT_FOUND=1]=`MATCHER_NOT_FOUND`,e[e.NAVIGATION_GUARD_REDIRECT=2]=`NAVIGATION_GUARD_REDIRECT`,e[e.NAVIGATION_ABORTED=4]=`NAVIGATION_ABORTED`,e[e.NAVIGATION_CANCELLED=8]=`NAVIGATION_CANCELLED`,e[e.NAVIGATION_DUPLICATED=16]=`NAVIGATION_DUPLICATED`,e}({});const Ie=Symbol(``);let Le=function(e){return e[e.aborted=4]=`aborted`,e[e.cancelled=8]=`cancelled`,e[e.duplicated=16]=`duplicated`,e}({});H.MATCHER_NOT_FOUND,H.NAVIGATION_GUARD_REDIRECT,H.NAVIGATION_ABORTED,H.NAVIGATION_CANCELLED,H.NAVIGATION_DUPLICATED;function U(e,t){return g(Error(),{type:e,[Ie]:!0},t)}function W(e,t){return e instanceof Error&&Ie in e&&(t==null||!!(e.type&t))}let G=function(e){return e[e.Static=0]=`Static`,e[e.Param=1]=`Param`,e[e.Group=2]=`Group`,e}({});var K=function(e){return e[e.Static=0]=`Static`,e[e.Param=1]=`Param`,e[e.ParamRegExp=2]=`ParamRegExp`,e[e.ParamRegExpEnd=3]=`ParamRegExpEnd`,e[e.EscapeNext=4]=`EscapeNext`,e}(K||{});const Re={type:G.Static,value:``},ze=/[a-zA-Z0-9_]/;function Be(e){if(!e)return[[]];if(e===`/`)return[[Re]];if(!e.startsWith(`/`))throw Error(`Invalid path "${e}"`);function t(e){throw Error(`ERR (${n})/"${l}": ${e}`)}let n=K.Static,r=n,i=[],a;function o(){a&&i.push(a),a=[]}let s=0,c,l=``,u=``;function d(){l&&=(n===K.Static?a.push({type:G.Static,value:l}):n===K.Param||n===K.ParamRegExp||n===K.ParamRegExpEnd?(a.length>1&&(c===`*`||c===`+`)&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),a.push({type:G.Param,value:l,regexp:u,repeatable:c===`*`||c===`+`,optional:c===`*`||c===`?`})):t(`Invalid state to consume buffer`),``)}function f(){l+=c}for(;s<e.length;){if(c=e[s++],c===`\\`&&n!==K.ParamRegExp){r=n,n=K.EscapeNext;continue}switch(n){case K.Static:c===`/`?(l&&d(),o()):c===`:`?(d(),n=K.Param):f();break;case K.EscapeNext:f(),n=r;break;case K.Param:c===`(`?n=K.ParamRegExp:ze.test(c)?f():(d(),n=K.Static,c!==`*`&&c!==`?`&&c!==`+`&&s--);break;case K.ParamRegExp:c===`)`?u[u.length-1]==`\\`?u=u.slice(0,-1)+c:n=K.ParamRegExpEnd:u+=c;break;case K.ParamRegExpEnd:d(),n=K.Static,c!==`*`&&c!==`?`&&c!==`+`&&s--,u=``;break;default:t(`Unknown state`);break}}return n===K.ParamRegExp&&t(`Unfinished custom RegExp for param "${l}"`),d(),o(),i}const Ve=`[^/]+?`,He={sensitive:!1,strict:!1,start:!0,end:!0};var q=function(e){return e[e._multiplier=10]=`_multiplier`,e[e.Root=90]=`Root`,e[e.Segment=40]=`Segment`,e[e.SubSegment=30]=`SubSegment`,e[e.Static=40]=`Static`,e[e.Dynamic=20]=`Dynamic`,e[e.BonusCustomRegExp=10]=`BonusCustomRegExp`,e[e.BonusWildcard=-50]=`BonusWildcard`,e[e.BonusRepeatable=-20]=`BonusRepeatable`,e[e.BonusOptional=-8]=`BonusOptional`,e[e.BonusStrict=.7000000000000001]=`BonusStrict`,e[e.BonusCaseSensitive=.25]=`BonusCaseSensitive`,e}(q||{});const Ue=/[.+*?^${}()[\]/\\]/g;function We(e,t){let n=g({},He,t),r=[],i=n.start?`^`:``,a=[];for(let t of e){let e=t.length?[]:[q.Root];n.strict&&!t.length&&(i+=`/`);for(let r=0;r<t.length;r++){let o=t[r],s=q.Segment+(n.sensitive?q.BonusCaseSensitive:0);if(o.type===G.Static)r||(i+=`/`),i+=o.value.replace(Ue,`\\$&`),s+=q.Static;else if(o.type===G.Param){let{value:e,repeatable:n,optional:c,regexp:l}=o;a.push({name:e,repeatable:n,optional:c});let u=l||Ve;if(u!==Ve){s+=q.BonusCustomRegExp;try{`${u}`}catch(t){throw Error(`Invalid custom RegExp for param "${e}" (${u}): `+t.message)}}let d=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;r||(d=c&&t.length<2?`(?:/${d})`:`/`+d),c&&(d+=`?`),i+=d,s+=q.Dynamic,c&&(s+=q.BonusOptional),n&&(s+=q.BonusRepeatable),u===`.*`&&(s+=q.BonusWildcard)}e.push(s)}r.push(e)}if(n.strict&&n.end){let e=r.length-1;r[e][r[e].length-1]+=q.BonusStrict}n.strict||(i+=`/?`),n.end?i+=`$`:n.strict&&!i.endsWith(`/`)&&(i+=`(?:/|$)`);let o=new RegExp(i,n.sensitive?``:`i`);function s(e){let t=e.match(o),n={};if(!t)return null;for(let e=1;e<t.length;e++){let r=t[e]||``,i=a[e-1];n[i.name]=r&&i.repeatable?r.split(`/`):r}return n}function c(t){let n=``,r=!1;for(let i of e){(!r||!n.endsWith(`/`))&&(n+=`/`),r=!1;for(let e of i)if(e.type===G.Static)n+=e.value;else if(e.type===G.Param){let{value:a,repeatable:o,optional:s}=e,c=a in t?t[a]:``;if(v(c)&&!o)throw Error(`Provided param "${a}" is an array but it is not repeatable (* or + modifiers)`);let l=v(c)?c.join(`/`):c;if(!l)if(s)i.length<2&&(n.endsWith(`/`)?n=n.slice(0,-1):r=!0);else throw Error(`Missing required param "${a}"`);n+=l}}return n||`/`}return{re:o,score:r,keys:a,parse:s,stringify:c}}function Ge(e,t){let n=0;for(;n<e.length&&n<t.length;){let r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===q.Static+q.Segment?-1:1:e.length>t.length?t.length===1&&t[0]===q.Static+q.Segment?1:-1:0}function Ke(e,t){let n=0,r=e.score,i=t.score;for(;n<r.length&&n<i.length;){let e=Ge(r[n],i[n]);if(e)return e;n++}if(Math.abs(i.length-r.length)===1){if(qe(r))return 1;if(qe(i))return-1}return i.length-r.length}function qe(e){let t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Je={strict:!1,end:!0,sensitive:!1};function Ye(e,t,n){let r=g(We(Be(e.path),n),{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Xe(e,t){let n=[],r=new Map;t=re(Je,t);function i(e){return r.get(e)}function a(e,n,r){let i=!r,s=Qe(e);s.aliasOf=r&&r.record;let l=re(t,e),u=[s];if(`alias`in e){let t=typeof e.alias==`string`?[e.alias]:e.alias;for(let e of t)u.push(Qe(g({},s,{components:r?r.record.components:s.components,path:e,aliasOf:r?r.record:s})))}let d,f;for(let t of u){let{path:u}=t;if(n&&u[0]!==`/`){let e=n.record.path,r=e[e.length-1]===`/`?``:`/`;t.path=n.record.path+(u&&r+u)}if(d=Ye(t,n,l),r?r.alias.push(d):(f||=d,f!==d&&f.alias.push(d),i&&e.name&&!et(d)&&o(e.name)),it(d)&&c(d),s.children){let e=s.children;for(let t=0;t<e.length;t++)a(e[t],d,r&&r.children[t])}r||=d}return f?()=>{o(f)}:_}function o(e){if(Fe(e)){let t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{let t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function s(){return n}function c(e){let t=nt(e,n);n.splice(t,0,e),e.record.name&&!et(e)&&r.set(e.record.name,e)}function l(e,t){let i,a={},o,s;if(`name`in e&&e.name){if(i=r.get(e.name),!i)throw U(H.MATCHER_NOT_FOUND,{location:e});s=i.record.name,a=g(Ze(t.params,i.keys.filter(e=>!e.optional).concat(i.parent?i.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Ze(e.params,i.keys.map(e=>e.name))),o=i.stringify(a)}else if(e.path!=null)o=e.path,i=n.find(e=>e.re.test(o)),i&&(a=i.parse(o),s=i.record.name);else{if(i=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!i)throw U(H.MATCHER_NOT_FOUND,{location:e,currentLocation:t});s=i.record.name,a=g({},t.params,e.params),o=i.stringify(a)}let c=[],l=i;for(;l;)c.unshift(l.record),l=l.parent;return{name:s,path:o,params:a,matched:c,meta:tt(c)}}e.forEach(e=>a(e));function u(){n.length=0,r.clear()}return{addRoute:a,resolve:l,removeRoute:o,clearRoutes:u,getRoutes:s,getRecordMatcher:i}}function Ze(e,t){let n={};for(let r of t)r in e&&(n[r]=e[r]);return n}function Qe(e){let t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:$e(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:`components`in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,`mods`,{value:{}}),t}function $e(e){let t={},n=e.props||!1;if(`component`in e)t.default=n;else for(let r in e.components)t[r]=typeof n==`object`?n[r]:n;return t}function et(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function tt(e){return e.reduce((e,t)=>g(e,t.meta),{})}function nt(e,t){let n=0,r=t.length;for(;n!==r;){let i=n+r>>1;Ke(e,t[i])<0?r=i:n=i+1}let i=rt(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function rt(e){let t=e;for(;t=t.parent;)if(it(t)&&Ke(e,t)===0)return t}function it({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function at(e){let t={};if(e===``||e===`?`)return t;let n=(e[0]===`?`?e.slice(1):e).split(`&`);for(let e=0;e<n.length;++e){let r=n[e].replace(x,` `),i=r.indexOf(`=`),a=k(i<0?r:r.slice(0,i)),o=i<0?null:k(r.slice(i+1));if(a in t){let e=t[a];v(e)||(e=t[a]=[e]),e.push(o)}else t[a]=o}return t}function ot(e){let t=``;for(let n in e){let r=e[n];if(n=fe(n),r==null){r!==void 0&&(t+=(t.length?`&`:``)+n);continue}(v(r)?r.map(e=>e&&D(e)):[r&&D(r)]).forEach(e=>{e!==void 0&&(t+=(t.length?`&`:``)+n,e!=null&&(t+=`=`+e))})}return t}function st(e){let t={};for(let n in e){let r=e[n];r!==void 0&&(t[n]=v(r)?r.map(e=>e==null?null:``+e):r==null?r:``+r)}return t}const J=Symbol(``),ct=Symbol(``),Y=Symbol(``),X=Symbol(``),Z=Symbol(``);function Q(){let e=[];function t(t){return e.push(t),()=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function lt(e,t,n){let r=()=>{e[t].delete(n)};s(r),o(r),a(()=>{e[t].add(n)}),e[t].add(n)}function ut(e){let t=r(J,{}).value;t&&lt(t,`leaveGuards`,e)}function dt(e){let t=r(J,{}).value;t&&lt(t,`updateGuards`,e)}function $(e,t,n,r,i,a=e=>e()){let o=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((s,c)=>{let l=e=>{e===!1?c(U(H.NAVIGATION_ABORTED,{from:n,to:t})):e instanceof Error?c(e):Pe(e)?c(U(H.NAVIGATION_GUARD_REDIRECT,{from:t,to:e})):(o&&r.enterCallbacks[i]===o&&typeof e==`function`&&o.push(e),s())},u=a(()=>e.call(r&&r.instances[i],t,n,l)),d=Promise.resolve(u);e.length<3&&(d=d.then(l)),d.catch(e=>c(e))})}function ft(e,t,n,r,i=e=>e()){let a=[];for(let o of e)for(let e in o.components){let s=o.components[e];if(!(t!==`beforeRouteEnter`&&!o.instances[e]))if(h(s)){let c=(s.__vccOpts||s)[t];c&&a.push($(c,n,r,o,e,i))}else{let c=s();a.push(()=>c.then(a=>{if(!a)throw Error(`Couldn't resolve component "${e}" at "${o.path}"`);let s=te(a)?a.default:a;o.mods[e]=a,o.components[e]=s;let c=(s.__vccOpts||s)[t];return c&&$(c,n,r,o,e,i)()}))}}return a}function pt(e){return e.matched.every(e=>e.redirect)?Promise.reject(Error(`Cannot load a route that redirects.`)):Promise.all(e.matched.map(e=>e.components&&Promise.all(Object.keys(e.components).reduce((t,n)=>{let r=e.components[n];return typeof r==`function`&&!(`displayName`in r)&&t.push(r().then(t=>{if(!t)return Promise.reject(Error(`Couldn't resolve component "${n}" at "${e.path}". Ensure you passed a function that returns a promise.`));let r=te(t)?t.default:t;e.mods[n]=t,e.components[n]=r})),t},[])))).then(()=>e)}function mt(e,t){let n=[],r=[],i=[],a=Math.max(t.matched.length,e.matched.length);for(let o=0;o<a;o++){let a=t.matched[o];a&&(e.matched.find(e=>M(e,a))?r.push(a):n.push(a));let s=e.matched[o];s&&(t.matched.find(e=>M(e,s))||i.push(s))}return[n,r,i]}function ht(t){let n=r(Y),i=r(X),a=e(()=>{let e=p(t.to);return n.resolve(e)}),o=e(()=>{let{matched:e}=a.value,{length:t}=e,n=e[t-1],r=i.matched;if(!n||!r.length)return-1;let o=r.findIndex(M.bind(null,n));if(o>-1)return o;let s=bt(e[t-2]);return t>1&&bt(n)===s&&r[r.length-1].path!==s?r.findIndex(M.bind(null,e[t-2])):o}),s=e(()=>o.value>-1&&yt(i.params,a.value.params)),c=e(()=>o.value>-1&&o.value===i.matched.length-1&&N(i.params,a.value.params));function l(e={}){if(vt(e)){let e=n[p(t.replace)?`replace`:`push`](p(t.to)).catch(_);return t.viewTransition&&typeof document<`u`&&`startViewTransition`in document&&document.startViewTransition(()=>e),e}return Promise.resolve()}return{route:a,href:e(()=>a.value.href),isActive:s,isExactActive:c,navigate:l}}function gt(e){return e.length===1?e[0]:e}const _t=t({name:`RouterLink`,compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:`page`},viewTransition:Boolean},useLink:ht,setup(t,{slots:i}){let a=l(ht(t)),{options:o}=r(Y),s=e(()=>({[xt(t.activeClass,o.linkActiveClass,`router-link-active`)]:a.isActive,[xt(t.exactActiveClass,o.linkExactActiveClass,`router-link-exact-active`)]:a.isExactActive}));return()=>{let e=i.default&&gt(i.default(a));return t.custom?e:n(`a`,{"aria-current":a.isExactActive?t.ariaCurrentValue:null,href:a.href,onClick:a.navigate,class:s.value},e)}}});function vt(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){let t=e.currentTarget.getAttribute(`target`);if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function yt(e,t){for(let n in t){let r=t[n],i=e[n];if(typeof r==`string`){if(r!==i)return!1}else if(!v(i)||i.length!==r.length||r.some((e,t)=>e!==i[t]))return!1}return!0}function bt(e){return e?e.aliasOf?e.aliasOf.path:e.path:``}const xt=(e,t,n)=>e??t??n,St=t({name:`RouterView`,inheritAttrs:!1,props:{name:{type:String,default:`default`},route:Object},compatConfig:{MODE:3},setup(t,{attrs:i,slots:a}){let o=r(Z),s=e(()=>t.route||o.value),l=r(ct,0),d=e(()=>{let e=p(l),{matched:t}=s.value,n;for(;(n=t[e])&&!n.components;)e++;return e}),f=e(()=>s.value.matched[d.value]);c(ct,e(()=>d.value+1)),c(J,f),c(Z,s);let m=u();return ee(()=>[m.value,f.value,t.name],([e,t,n],[r,i,a])=>{t&&(t.instances[n]=e,i&&i!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=i.leaveGuards),t.updateGuards.size||(t.updateGuards=i.updateGuards))),e&&t&&(!i||!M(t,i)||!r)&&(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:`post`}),()=>{let e=s.value,r=t.name,o=f.value,c=o&&o.components[r];if(!c)return Ct(a.default,{Component:c,route:e});let l=o.props[r],u=n(c,g({},l?l===!0?e.params:typeof l==`function`?l(e):l:null,i,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(o.instances[r]=null)},ref:m}));return Ct(a.default,{Component:u,route:e})||u}}});function Ct(e,t){if(!e)return null;let n=e(t);return n.length===1?n[0]:n}const wt=St;function Tt(e){let t=Xe(e.routes,e),n=e.parseQuery||at,r=e.stringifyQuery||ot,a=e.history,o=Q(),s=Q(),c=Q(),l=f(I),u=I;m&&e.scrollBehavior&&`scrollRestoration`in history&&(history.scrollRestoration=`manual`);let ee=ne.bind(null,e=>``+e),h=ne.bind(null,pe),te=ne.bind(null,k);function re(e,n){let r,i;return Fe(e)?(r=t.getRecordMatcher(e),i=n):i=e,t.addRoute(i,r)}function ie(e){let n=t.getRecordMatcher(e);n&&t.removeRoute(n)}function ae(){return t.getRoutes().map(e=>e.record)}function oe(e){return!!t.getRecordMatcher(e)}function y(e,i){if(i=g({},i||l.value),typeof e==`string`){let r=he(n,e,i.path),o=t.resolve({path:r.path},i),s=a.createHref(r.fullPath);return g(r,o,{params:te(o.params),hash:k(r.hash),redirectedFrom:void 0,href:s})}let o;if(e.path!=null)o=g({},e,{path:he(n,e.path,i.path).path});else{let t=g({},e.params);for(let e in t)t[e]??delete t[e];o=g({},e,{params:h(t)}),i.params=h(i.params)}let s=t.resolve(o,i),c=e.hash||``;s.params=ee(te(s.params));let u=ge(r,g({},e,{hash:de(c),path:s.path})),d=a.createHref(u);return g({fullPath:u,hash:c,query:r===ot?st(e.query):e.query||{}},s,{redirectedFrom:void 0,href:d})}function b(e){return typeof e==`string`?he(n,e,l.value.path):g({},e)}function x(e,t){if(u!==e)return U(H.NAVIGATION_CANCELLED,{from:t,to:e})}function S(e){return C(e)}function se(e){return S(g(b(e),{replace:!0}))}function ce(e,t){let n=e.matched[e.matched.length-1];if(n&&n.redirect){let{redirect:r}=n,i=typeof r==`function`?r(e,t):r;return typeof i==`string`&&(i=i.includes(`?`)||i.includes(`#`)?i=b(i):{path:i},i.params={}),g({query:e.query,hash:e.hash,params:i.path==null?e.params:{}},i)}}function C(e,t){let n=u=y(e),i=l.value,a=e.state,o=e.force,s=e.replace===!0,c=ce(n,i);if(c)return C(g(b(c),{state:typeof c==`object`?g({},a,c.state):a,force:o,replace:s}),t||n);let d=n;d.redirectedFrom=t;let f;return!o&&_e(r,i,n)&&(f=U(H.NAVIGATION_DUPLICATED,{to:d,from:i}),ve(i,i,!0,!1)),(f?Promise.resolve(f):T(d,i)).catch(e=>W(e)?W(e,H.NAVIGATION_GUARD_REDIRECT)?e:N(e):j(e,d,i)).then(e=>{if(e){if(W(e,H.NAVIGATION_GUARD_REDIRECT))return C(g({replace:s},b(e.to),{state:typeof e.to==`object`?g({},a,e.to.state):a,force:o}),t||d)}else e=E(d,i,!0,s,a);return ue(d,i,e),e})}function le(e,t){let n=x(e,t);return n?Promise.reject(n):Promise.resolve()}function w(e){let t=R.values().next().value;return t&&typeof t.runWithContext==`function`?t.runWithContext(e):e()}function T(e,t){let n,[r,i,a]=mt(e,t);n=ft(r.reverse(),`beforeRouteLeave`,e,t);for(let i of r)i.leaveGuards.forEach(r=>{n.push($(r,e,t))});let c=le.bind(null,e,t);return n.push(c),B(n).then(()=>{n=[];for(let r of o.list())n.push($(r,e,t));return n.push(c),B(n)}).then(()=>{n=ft(i,`beforeRouteUpdate`,e,t);for(let r of i)r.updateGuards.forEach(r=>{n.push($(r,e,t))});return n.push(c),B(n)}).then(()=>{n=[];for(let r of a)if(r.beforeEnter)if(v(r.beforeEnter))for(let i of r.beforeEnter)n.push($(i,e,t));else n.push($(r.beforeEnter,e,t));return n.push(c),B(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=ft(a,`beforeRouteEnter`,e,t,w),n.push(c),B(n))).then(()=>{n=[];for(let r of s.list())n.push($(r,e,t));return n.push(c),B(n)}).catch(e=>W(e,H.NAVIGATION_CANCELLED)?e:Promise.reject(e))}function ue(e,t,n){c.list().forEach(r=>w(()=>r(e,t,n)))}function E(e,t,n,r,i){let o=x(e,t);if(o)return o;let s=t===I,c=m?history.state:{};n&&(r||s?a.replace(e.fullPath,g({scroll:s&&c&&c.scroll},i)):a.push(e.fullPath,i)),l.value=e,ve(e,t,n,s),N()}let D;function fe(){D||=a.listen((e,t,n)=>{if(!z.listening)return;let r=y(e),i=ce(r,z.currentRoute.value);if(i){C(g(i,{replace:!0,force:!0}),r).catch(_);return}u=r;let o=l.value;m&&we(Se(o.fullPath,n.delta),V()),T(r,o).catch(e=>W(e,H.NAVIGATION_ABORTED|H.NAVIGATION_CANCELLED)?e:W(e,H.NAVIGATION_GUARD_REDIRECT)?(C(g(b(e.to),{force:!0}),r).then(e=>{W(e,H.NAVIGATION_ABORTED|H.NAVIGATION_DUPLICATED)&&!n.delta&&n.type===L.pop&&a.go(-1,!1)}).catch(_),Promise.reject()):(n.delta&&a.go(-n.delta,!1),j(e,r,o))).then(e=>{e||=E(r,o,!1),e&&(n.delta&&!W(e,H.NAVIGATION_CANCELLED)?a.go(-n.delta,!1):n.type===L.pop&&W(e,H.NAVIGATION_ABORTED|H.NAVIGATION_DUPLICATED)&&a.go(-1,!1)),ue(r,o,e)}).catch(_)})}let O=Q(),me=Q(),A;function j(e,t,n){N(e);let r=me.list();return r.length?r.forEach(r=>r(e,t,n)):console.error(e),Promise.reject(e)}function M(){return A&&l.value!==I?Promise.resolve():new Promise((e,t)=>{O.add([e,t])})}function N(e){return A||(A=!e,fe(),O.list().forEach(([t,n])=>e?n(e):t()),O.reset()),e}function ve(t,n,r,a){let{scrollBehavior:o}=e;if(!m||!o)return Promise.resolve();let s=!r&&Te(Se(t.fullPath,0))||(a||!r)&&history.state&&history.state.scroll||null;return i().then(()=>o(t,n,s)).then(e=>e&&xe(e)).catch(e=>j(e,t,n))}let P=e=>a.go(e),F,R=new Set,z={currentRoute:l,listening:!0,addRoute:re,removeRoute:ie,clearRoutes:t.clearRoutes,hasRoute:oe,getRoutes:ae,resolve:y,options:e,push:S,replace:se,go:P,back:()=>P(-1),forward:()=>P(1),beforeEach:o.add,beforeResolve:s.add,afterEach:c.add,onError:me.add,isReady:M,install(e){e.component(`RouterLink`,_t),e.component(`RouterView`,wt),e.config.globalProperties.$router=z,Object.defineProperty(e.config.globalProperties,`$route`,{enumerable:!0,get:()=>p(l)}),m&&!F&&l.value===I&&(F=!0,S(a.location).catch(e=>{}));let t={};for(let e in I)Object.defineProperty(t,e,{get:()=>l.value[e],enumerable:!0});e.provide(Y,z),e.provide(X,d(t)),e.provide(Z,l);let n=e.unmount;R.add(e),e.unmount=function(){R.delete(e),R.size<1&&(u=I,D&&D(),D=null,l.value=I,F=!1,A=!1),n()}}};function B(e){return e.reduce((e,t)=>e.then(()=>w(t)),Promise.resolve())}return z}function Et(){return r(Y)}function Dt(e){return r(X)}export{Le as NavigationFailureType,_t as RouterLink,wt as RouterView,I as START_LOCATION,Me as createMemoryHistory,Tt as createRouter,Xe as createRouterMatcher,Ne as createWebHashHistory,je as createWebHistory,W as isNavigationFailure,pt as loadRouteLocation,J as matchedRouteKey,ut as onBeforeRouteLeave,dt as onBeforeRouteUpdate,at as parseQuery,X as routeLocationKey,Y as routerKey,Z as routerViewLocationKey,ot as stringifyQuery,ht as useLink,Dt as useRoute,Et as useRouter,ct as viewDepthKey};