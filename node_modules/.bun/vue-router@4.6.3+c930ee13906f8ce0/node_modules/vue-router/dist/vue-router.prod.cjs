/*!
* vue-router v4.6.3
* (c) 2025 <PERSON>
* @license MIT
*/
var e=Object.create,t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.getPrototypeOf,a=Object.prototype.hasOwnProperty,o=(e,i,o,s)=>{if(i&&typeof i==`object`||typeof i==`function`)for(var c=r(i),l=0,u=c.length,d;l<u;l++)d=c[l],!a.call(e,d)&&d!==o&&t(e,d,{get:(e=>i[e]).bind(null,d),enumerable:!(s=n(i,d))||s.enumerable});return e},s=(n,r,a)=>(a=n==null?{}:e(i(n)),o(r||!n||!n.__esModule?t(a,`default`,{value:n,enumerable:!0}):a,n));let c=require(`vue`);c=s(c);let l=require(`@vue/devtools-api`);l=s(l);const u=typeof document<`u`;function d(e){return typeof e==`object`||`displayName`in e||`props`in e||`__vccOpts`in e}function f(e){return e.__esModule||e[Symbol.toStringTag]===`Module`||e.default&&d(e.default)}const p=Object.assign;function m(e,t){let n={};for(let r in t){let i=t[r];n[r]=g(i)?i.map(e):e(i)}return n}const h=()=>{},g=Array.isArray;function _(e,t){let n={};for(let r in e)n[r]=r in t?t[r]:e[r];return n}function v(e){let t=Array.from(arguments).slice(1);console.warn.apply(console,[`[Vue Router warn]: `+e].concat(t))}const y=/#/g,ee=/&/g,te=/\//g,ne=/=/g,re=/\?/g,b=/\+/g,x=/%5B/g,ie=/%5D/g,S=/%5E/g,ae=/%60/g,oe=/%7B/g,C=/%7C/g,se=/%7D/g,w=/%20/g;function T(e){return e==null?``:encodeURI(``+e).replace(C,`|`).replace(x,`[`).replace(ie,`]`)}function ce(e){return T(e).replace(oe,`{`).replace(se,`}`).replace(S,`^`)}function E(e){return T(e).replace(b,`%2B`).replace(w,`+`).replace(y,`%23`).replace(ee,`%26`).replace(ae,"`").replace(oe,`{`).replace(se,`}`).replace(S,`^`)}function le(e){return E(e).replace(ne,`%3D`)}function D(e){return T(e).replace(y,`%23`).replace(re,`%3F`)}function ue(e){return D(e).replace(te,`%2F`)}function O(e){if(e==null)return null;try{return decodeURIComponent(``+e)}catch{process.env.NODE_ENV!==`production`&&v(`Error decoding "${e}". Using original value`)}return``+e}const de=/\/$/,k=e=>e.replace(de,``);function fe(e,t,n=`/`){let r,i={},a=``,o=``,s=t.indexOf(`#`),c=t.indexOf(`?`);return c=s>=0&&c>s?-1:c,c>=0&&(r=t.slice(0,c),a=t.slice(c,s>0?s:t.length),i=e(a.slice(1))),s>=0&&(r||=t.slice(0,s),o=t.slice(s,t.length)),r=ge(r??t,n),{fullPath:r+a+o,path:r,query:i,hash:O(o)}}function pe(e,t){let n=t.query?e(t.query):``;return t.path+(n&&`?`)+n+(t.hash||``)}function A(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||`/`}function me(e,t,n){let r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&j(t.matched[r],n.matched[i])&&M(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function j(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function M(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let n in e)if(!he(e[n],t[n]))return!1;return!0}function he(e,t){return g(e)?N(e,t):g(t)?N(t,e):e===t}function N(e,t){return g(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):e.length===1&&e[0]===t}function ge(e,t){if(e.startsWith(`/`))return e;if(process.env.NODE_ENV!==`production`&&!t.startsWith(`/`))return v(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;let n=t.split(`/`),r=e.split(`/`),i=r[r.length-1];(i===`..`||i===`.`)&&r.push(``);let a=n.length-1,o,s;for(o=0;o<r.length;o++)if(s=r[o],s!==`.`)if(s===`..`)a>1&&a--;else break;return n.slice(0,a).join(`/`)+`/`+r.slice(o).join(`/`)}const P={path:`/`,name:void 0,params:{},query:{},hash:``,fullPath:`/`,matched:[],meta:{},redirectedFrom:void 0};let F=function(e){return e.pop=`pop`,e.push=`push`,e}({}),I=function(e){return e.back=`back`,e.forward=`forward`,e.unknown=``,e}({});const _e=``;function L(e){if(!e)if(u){let t=document.querySelector(`base`);e=t&&t.getAttribute(`href`)||`/`,e=e.replace(/^\w+:\/\/[^\/]+/,``)}else e=`/`;return e[0]!==`/`&&e[0]!==`#`&&(e=`/`+e),k(e)}const R=/^[^#]+#/;function z(e,t){return e.replace(R,`#`)+t}function ve(e,t){let n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const ye=()=>({left:window.scrollX,top:window.scrollY});function be(e){let t;if(`el`in e){let n=e.el,r=typeof n==`string`&&n.startsWith(`#`);if(process.env.NODE_ENV!==`production`&&typeof e.el==`string`&&(!r||!document.getElementById(e.el.slice(1))))try{let t=document.querySelector(e.el);if(r&&t){v(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{v(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}let i=typeof n==`string`?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i){process.env.NODE_ENV!==`production`&&v(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=ve(i,e)}else t=e;`scrollBehavior`in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left==null?window.scrollX:t.left,t.top==null?window.scrollY:t.top)}function xe(e,t){return(history.state?history.state.position-t:-1)+e}const Se=new Map;function Ce(e,t){Se.set(e,t)}function we(e){let t=Se.get(e);return Se.delete(e),t}let Te=()=>location.protocol+`//`+location.host;function Ee(e,t){let{pathname:n,search:r,hash:i}=t,a=e.indexOf(`#`);if(a>-1){let t=i.includes(e.slice(a))?e.slice(a).length:1,n=i.slice(t);return n[0]!==`/`&&(n=`/`+n),A(n,``)}return A(n,e)+r+i}function De(e,t,n,r){let i=[],a=[],o=null,s=({state:a})=>{let s=Ee(e,location),c=n.value,l=t.value,u=0;if(a){if(n.value=s,t.value=a,o&&o===c){o=null;return}u=l?a.position-l.position:0}else r(s);i.forEach(e=>{e(n.value,c,{delta:u,type:F.pop,direction:u?u>0?I.forward:I.back:I.unknown})})};function c(){o=n.value}function l(e){i.push(e);let t=()=>{let t=i.indexOf(e);t>-1&&i.splice(t,1)};return a.push(t),t}function u(){if(document.visibilityState===`hidden`){let{history:e}=window;if(!e.state)return;e.replaceState(p({},e.state,{scroll:ye()}),``)}}function d(){for(let e of a)e();a=[],window.removeEventListener(`popstate`,s),window.removeEventListener(`pagehide`,u),document.removeEventListener(`visibilitychange`,u)}return window.addEventListener(`popstate`,s),window.addEventListener(`pagehide`,u),document.addEventListener(`visibilitychange`,u),{pauseListeners:c,listen:l,destroy:d}}function Oe(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?ye():null}}function ke(e){let{history:t,location:n}=window,r={value:Ee(e,n)},i={value:t.state};i.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(r,a,o){let s=e.indexOf(`#`),c=s>-1?(n.host&&document.querySelector(`base`)?e:e.slice(s))+r:Te()+e+r;try{t[o?`replaceState`:`pushState`](a,``,c),i.value=a}catch(e){process.env.NODE_ENV===`production`?console.error(e):v(`Error with push/replace State`,e),n[o?`replace`:`assign`](c)}}function o(e,n){a(e,p({},t.state,Oe(i.value.back,e,i.value.forward,!0),n,{position:i.value.position}),!0),r.value=e}function s(e,n){let o=p({},i.value,t.state,{forward:e,scroll:ye()});process.env.NODE_ENV!==`production`&&!t.state&&v(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(o.current,o,!0),a(e,p({},Oe(r.value,e,null),{position:o.position+1},n),!1),r.value=e}return{location:r,state:i,push:s,replace:o}}function Ae(e){e=L(e);let t=ke(e),n=De(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}let i=p({location:``,base:e,go:r,createHref:z.bind(null,e)},t,n);return Object.defineProperty(i,`location`,{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,`state`,{enumerable:!0,get:()=>t.state.value}),i}function je(e=``){let t=[],n=[[``,{}]],r=0;e=L(e);function i(e,t={}){r++,r!==n.length&&n.splice(r),n.push([e,t])}function a(e,n,{direction:r,delta:i}){let a={direction:r,delta:i,type:F.pop};for(let r of t)r(e,n,a)}let o={location:``,state:{},base:e,createHref:z.bind(null,e),replace(e,t){n.splice(r--,1),i(e,t)},push(e,t){i(e,t)},listen(e){return t.push(e),()=>{let n=t.indexOf(e);n>-1&&t.splice(n,1)}},destroy(){t=[],n=[[``,{}]],r=0},go(e,t=!0){let i=this.location,o=e<0?I.back:I.forward;r=Math.max(0,Math.min(r+e,n.length-1)),t&&a(this.location,i,{direction:o,delta:e})}};return Object.defineProperty(o,`location`,{enumerable:!0,get:()=>n[r][0]}),Object.defineProperty(o,`state`,{enumerable:!0,get:()=>n[r][1]}),o}function Me(e){return e=location.host?e||location.pathname+location.search:``,e.includes(`#`)||(e+=`#`),process.env.NODE_ENV!==`production`&&!e.endsWith(`#/`)&&!e.endsWith(`#`)&&v(`A hash base must end with a "#":\n"${e}" should be "${e.replace(/#.*$/,`#`)}".`),Ae(e)}function B(e){return typeof e==`string`||e&&typeof e==`object`}function Ne(e){return typeof e==`string`||typeof e==`symbol`}let V=function(e){return e[e.MATCHER_NOT_FOUND=1]=`MATCHER_NOT_FOUND`,e[e.NAVIGATION_GUARD_REDIRECT=2]=`NAVIGATION_GUARD_REDIRECT`,e[e.NAVIGATION_ABORTED=4]=`NAVIGATION_ABORTED`,e[e.NAVIGATION_CANCELLED=8]=`NAVIGATION_CANCELLED`,e[e.NAVIGATION_DUPLICATED=16]=`NAVIGATION_DUPLICATED`,e}({});const Pe=Symbol(process.env.NODE_ENV===`production`?``:`navigation failure`);let Fe=function(e){return e[e.aborted=4]=`aborted`,e[e.cancelled=8]=`cancelled`,e[e.duplicated=16]=`duplicated`,e}({});const Ie={[V.MATCHER_NOT_FOUND]({location:e,currentLocation:t}){return`No match for\n ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):``}`},[V.NAVIGATION_GUARD_REDIRECT]({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${Re(t)}" via a navigation guard.`},[V.NAVIGATION_ABORTED]({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},[V.NAVIGATION_CANCELLED]({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},[V.NAVIGATION_DUPLICATED]({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function H(e,t){if(process.env.NODE_ENV!==`production`||!0)return p(Error(Ie[e](t)),{type:e,[Pe]:!0},t)}function U(e,t){return e instanceof Error&&Pe in e&&(t==null||!!(e.type&t))}const Le=[`params`,`query`,`hash`];function Re(e){if(typeof e==`string`)return e;if(e.path!=null)return e.path;let t={};for(let n of Le)n in e&&(t[n]=e[n]);return JSON.stringify(t,null,2)}let W=function(e){return e[e.Static=0]=`Static`,e[e.Param=1]=`Param`,e[e.Group=2]=`Group`,e}({});var G=function(e){return e[e.Static=0]=`Static`,e[e.Param=1]=`Param`,e[e.ParamRegExp=2]=`ParamRegExp`,e[e.ParamRegExpEnd=3]=`ParamRegExpEnd`,e[e.EscapeNext=4]=`EscapeNext`,e}(G||{});const ze={type:W.Static,value:``},Be=/[a-zA-Z0-9_]/;function Ve(e){if(!e)return[[]];if(e===`/`)return[[ze]];if(!e.startsWith(`/`))throw Error(process.env.NODE_ENV===`production`?`Invalid path "${e}"`:`Route paths should start with a "/": "${e}" should be "/${e}".`);function t(e){throw Error(`ERR (${n})/"${l}": ${e}`)}let n=G.Static,r=n,i=[],a;function o(){a&&i.push(a),a=[]}let s=0,c,l=``,u=``;function d(){l&&=(n===G.Static?a.push({type:W.Static,value:l}):n===G.Param||n===G.ParamRegExp||n===G.ParamRegExpEnd?(a.length>1&&(c===`*`||c===`+`)&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),a.push({type:W.Param,value:l,regexp:u,repeatable:c===`*`||c===`+`,optional:c===`*`||c===`?`})):t(`Invalid state to consume buffer`),``)}function f(){l+=c}for(;s<e.length;){if(c=e[s++],c===`\\`&&n!==G.ParamRegExp){r=n,n=G.EscapeNext;continue}switch(n){case G.Static:c===`/`?(l&&d(),o()):c===`:`?(d(),n=G.Param):f();break;case G.EscapeNext:f(),n=r;break;case G.Param:c===`(`?n=G.ParamRegExp:Be.test(c)?f():(d(),n=G.Static,c!==`*`&&c!==`?`&&c!==`+`&&s--);break;case G.ParamRegExp:c===`)`?u[u.length-1]==`\\`?u=u.slice(0,-1)+c:n=G.ParamRegExpEnd:u+=c;break;case G.ParamRegExpEnd:d(),n=G.Static,c!==`*`&&c!==`?`&&c!==`+`&&s--,u=``;break;default:t(`Unknown state`);break}}return n===G.ParamRegExp&&t(`Unfinished custom RegExp for param "${l}"`),d(),o(),i}const He=`[^/]+?`,Ue={sensitive:!1,strict:!1,start:!0,end:!0};var K=function(e){return e[e._multiplier=10]=`_multiplier`,e[e.Root=90]=`Root`,e[e.Segment=40]=`Segment`,e[e.SubSegment=30]=`SubSegment`,e[e.Static=40]=`Static`,e[e.Dynamic=20]=`Dynamic`,e[e.BonusCustomRegExp=10]=`BonusCustomRegExp`,e[e.BonusWildcard=-50]=`BonusWildcard`,e[e.BonusRepeatable=-20]=`BonusRepeatable`,e[e.BonusOptional=-8]=`BonusOptional`,e[e.BonusStrict=.7000000000000001]=`BonusStrict`,e[e.BonusCaseSensitive=.25]=`BonusCaseSensitive`,e}(K||{});const We=/[.+*?^${}()[\]/\\]/g;function Ge(e,t){let n=p({},Ue,t),r=[],i=n.start?`^`:``,a=[];for(let t of e){let e=t.length?[]:[K.Root];n.strict&&!t.length&&(i+=`/`);for(let r=0;r<t.length;r++){let o=t[r],s=K.Segment+(n.sensitive?K.BonusCaseSensitive:0);if(o.type===W.Static)r||(i+=`/`),i+=o.value.replace(We,`\\$&`),s+=K.Static;else if(o.type===W.Param){let{value:e,repeatable:n,optional:c,regexp:l}=o;a.push({name:e,repeatable:n,optional:c});let u=l||He;if(u!==He){s+=K.BonusCustomRegExp;try{`${u}`}catch(t){throw Error(`Invalid custom RegExp for param "${e}" (${u}): `+t.message)}}let d=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;r||(d=c&&t.length<2?`(?:/${d})`:`/`+d),c&&(d+=`?`),i+=d,s+=K.Dynamic,c&&(s+=K.BonusOptional),n&&(s+=K.BonusRepeatable),u===`.*`&&(s+=K.BonusWildcard)}e.push(s)}r.push(e)}if(n.strict&&n.end){let e=r.length-1;r[e][r[e].length-1]+=K.BonusStrict}n.strict||(i+=`/?`),n.end?i+=`$`:n.strict&&!i.endsWith(`/`)&&(i+=`(?:/|$)`);let o=new RegExp(i,n.sensitive?``:`i`);function s(e){let t=e.match(o),n={};if(!t)return null;for(let e=1;e<t.length;e++){let r=t[e]||``,i=a[e-1];n[i.name]=r&&i.repeatable?r.split(`/`):r}return n}function c(t){let n=``,r=!1;for(let i of e){(!r||!n.endsWith(`/`))&&(n+=`/`),r=!1;for(let e of i)if(e.type===W.Static)n+=e.value;else if(e.type===W.Param){let{value:a,repeatable:o,optional:s}=e,c=a in t?t[a]:``;if(g(c)&&!o)throw Error(`Provided param "${a}" is an array but it is not repeatable (* or + modifiers)`);let l=g(c)?c.join(`/`):c;if(!l)if(s)i.length<2&&(n.endsWith(`/`)?n=n.slice(0,-1):r=!0);else throw Error(`Missing required param "${a}"`);n+=l}}return n||`/`}return{re:o,score:r,keys:a,parse:s,stringify:c}}function Ke(e,t){let n=0;for(;n<e.length&&n<t.length;){let r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===K.Static+K.Segment?-1:1:e.length>t.length?t.length===1&&t[0]===K.Static+K.Segment?1:-1:0}function qe(e,t){let n=0,r=e.score,i=t.score;for(;n<r.length&&n<i.length;){let e=Ke(r[n],i[n]);if(e)return e;n++}if(Math.abs(i.length-r.length)===1){if(Je(r))return 1;if(Je(i))return-1}return i.length-r.length}function Je(e){let t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ye={strict:!1,end:!0,sensitive:!1};function Xe(e,t,n){let r=Ge(Ve(e.path),n);if(process.env.NODE_ENV!==`production`){let t=new Set;for(let n of r.keys)t.has(n.name)&&v(`Found duplicated params with name "${n.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),t.add(n.name)}let i=p(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function Ze(e,t){let n=[],r=new Map;t=_(Ye,t);function i(e){return r.get(e)}function a(e,n,r){let i=!r,s=$e(e);process.env.NODE_ENV!==`production`&&at(s,n),s.aliasOf=r&&r.record;let l=_(t,e),u=[s];if(`alias`in e){let t=typeof e.alias==`string`?[e.alias]:e.alias;for(let e of t)u.push($e(p({},s,{components:r?r.record.components:s.components,path:e,aliasOf:r?r.record:s})))}let d,f;for(let t of u){let{path:u}=t;if(n&&u[0]!==`/`){let e=n.record.path,r=e[e.length-1]===`/`?``:`/`;t.path=n.record.path+(u&&r+u)}if(process.env.NODE_ENV!==`production`&&t.path===`*`)throw Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(d=Xe(t,n,l),process.env.NODE_ENV!==`production`&&n&&u[0]===`/`&&st(d,n),r?(r.alias.push(d),process.env.NODE_ENV!==`production`&&it(r,d)):(f||=d,f!==d&&f.alias.push(d),i&&e.name&&!tt(d)&&(process.env.NODE_ENV!==`production`&&ot(e,n),o(e.name))),ut(d)&&c(d),s.children){let e=s.children;for(let t=0;t<e.length;t++)a(e[t],d,r&&r.children[t])}r||=d}return f?()=>{o(f)}:h}function o(e){if(Ne(e)){let t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{let t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function s(){return n}function c(e){let t=ct(e,n);n.splice(t,0,e),e.record.name&&!tt(e)&&r.set(e.record.name,e)}function l(e,t){let i,a={},o,s;if(`name`in e&&e.name){if(i=r.get(e.name),!i)throw H(V.MATCHER_NOT_FOUND,{location:e});if(process.env.NODE_ENV!==`production`){let t=Object.keys(e.params||{}).filter(e=>!i.keys.find(t=>t.name===e));t.length&&v(`Discarded invalid param(s) "${t.join(`", "`)}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}s=i.record.name,a=p(Qe(t.params,i.keys.filter(e=>!e.optional).concat(i.parent?i.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Qe(e.params,i.keys.map(e=>e.name))),o=i.stringify(a)}else if(e.path!=null)o=e.path,process.env.NODE_ENV!==`production`&&!o.startsWith(`/`)&&v(`The Matcher cannot resolve relative paths but received "${o}". Unless you directly called \`matcher.resolve("${o}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),i=n.find(e=>e.re.test(o)),i&&(a=i.parse(o),s=i.record.name);else{if(i=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!i)throw H(V.MATCHER_NOT_FOUND,{location:e,currentLocation:t});s=i.record.name,a=p({},t.params,e.params),o=i.stringify(a)}let c=[],l=i;for(;l;)c.unshift(l.record),l=l.parent;return{name:s,path:o,params:a,matched:c,meta:nt(c)}}e.forEach(e=>a(e));function u(){n.length=0,r.clear()}return{addRoute:a,resolve:l,removeRoute:o,clearRoutes:u,getRoutes:s,getRecordMatcher:i}}function Qe(e,t){let n={};for(let r of t)r in e&&(n[r]=e[r]);return n}function $e(e){let t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:et(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:`components`in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,`mods`,{value:{}}),t}function et(e){let t={},n=e.props||!1;if(`component`in e)t.default=n;else for(let r in e.components)t[r]=typeof n==`object`?n[r]:n;return t}function tt(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function nt(e){return e.reduce((e,t)=>p(e,t.meta),{})}function rt(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function it(e,t){for(let n of e.keys)if(!n.optional&&!t.keys.find(rt.bind(null,n)))return v(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${n.name}"`);for(let n of t.keys)if(!n.optional&&!e.keys.find(rt.bind(null,n)))return v(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${n.name}"`)}function at(e,t){t&&t.record.name&&!e.name&&!e.path&&v(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function ot(e,t){for(let n=t;n;n=n.parent)if(n.record.name===e.name)throw Error(`A route named "${String(e.name)}" has been added as a ${t===n?`child`:`descendant`} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function st(e,t){for(let n of t.keys)if(!e.keys.find(rt.bind(null,n)))return v(`Absolute path "${e.record.path}" must have the exact same param named "${n.name}" as its parent "${t.record.path}".`)}function ct(e,t){let n=0,r=t.length;for(;n!==r;){let i=n+r>>1;qe(e,t[i])<0?r=i:n=i+1}let i=lt(e);return i&&(r=t.lastIndexOf(i,r-1),process.env.NODE_ENV!==`production`&&r<0&&v(`Finding ancestor route "${i.record.path}" failed for "${e.record.path}"`)),r}function lt(e){let t=e;for(;t=t.parent;)if(ut(t)&&qe(e,t)===0)return t}function ut({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function dt(e){let t={};if(e===``||e===`?`)return t;let n=(e[0]===`?`?e.slice(1):e).split(`&`);for(let e=0;e<n.length;++e){let r=n[e].replace(b,` `),i=r.indexOf(`=`),a=O(i<0?r:r.slice(0,i)),o=i<0?null:O(r.slice(i+1));if(a in t){let e=t[a];g(e)||(e=t[a]=[e]),e.push(o)}else t[a]=o}return t}function ft(e){let t=``;for(let n in e){let r=e[n];if(n=le(n),r==null){r!==void 0&&(t+=(t.length?`&`:``)+n);continue}(g(r)?r.map(e=>e&&E(e)):[r&&E(r)]).forEach(e=>{e!==void 0&&(t+=(t.length?`&`:``)+n,e!=null&&(t+=`=`+e))})}return t}function pt(e){let t={};for(let n in e){let r=e[n];r!==void 0&&(t[n]=g(r)?r.map(e=>e==null?null:``+e):r==null?r:``+r)}return t}const q=Symbol(process.env.NODE_ENV===`production`?``:`router view location matched`),mt=Symbol(process.env.NODE_ENV===`production`?``:`router view depth`),J=Symbol(process.env.NODE_ENV===`production`?``:`router`),Y=Symbol(process.env.NODE_ENV===`production`?``:`route location`),X=Symbol(process.env.NODE_ENV===`production`?``:`router view location`);function Z(){let e=[];function t(t){return e.push(t),()=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ht(e,t,n){let r=()=>{e[t].delete(n)};(0,c.onUnmounted)(r),(0,c.onDeactivated)(r),(0,c.onActivated)(()=>{e[t].add(n)}),e[t].add(n)}function gt(e){if(process.env.NODE_ENV!==`production`&&!(0,c.getCurrentInstance)()){v(`getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function`);return}let t=(0,c.inject)(q,{}).value;if(!t){process.env.NODE_ENV!==`production`&&v("No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?");return}ht(t,`leaveGuards`,e)}function _t(e){if(process.env.NODE_ENV!==`production`&&!(0,c.getCurrentInstance)()){v(`getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function`);return}let t=(0,c.inject)(q,{}).value;if(!t){process.env.NODE_ENV!==`production`&&v("No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?");return}ht(t,`updateGuards`,e)}function Q(e,t,n,r,i,a=e=>e()){let o=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((s,c)=>{let l=e=>{e===!1?c(H(V.NAVIGATION_ABORTED,{from:n,to:t})):e instanceof Error?c(e):B(e)?c(H(V.NAVIGATION_GUARD_REDIRECT,{from:t,to:e})):(o&&r.enterCallbacks[i]===o&&typeof e==`function`&&o.push(e),s())},u=a(()=>e.call(r&&r.instances[i],t,n,process.env.NODE_ENV===`production`?l:vt(l,t,n))),d=Promise.resolve(u);if(e.length<3&&(d=d.then(l)),process.env.NODE_ENV!==`production`&&e.length>2){let t=`The "next" callback was never called inside of ${e.name?`"`+e.name+`"`:``}:\n${e.toString()}\n. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof u==`object`&&`then`in u)d=d.then(e=>l._called?e:(v(t),Promise.reject(Error(`Invalid navigation guard`))));else if(u!==void 0&&!l._called){v(t),c(Error(`Invalid navigation guard`));return}}d.catch(e=>c(e))})}function vt(e,t,n){let r=0;return function(){r++===1&&v(`The "next" callback was called more than once in one navigation guard when going from "${n.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,r===1&&e.apply(null,arguments)}}function yt(e,t,n,r,i=e=>e()){let a=[];for(let o of e)for(let e in process.env.NODE_ENV!==`production`&&!o.components&&o.children&&!o.children.length&&v(`Record with path "${o.path}" is either missing a "component(s)" or "children" property.`),o.components){let s=o.components[e];if(process.env.NODE_ENV!==`production`){if(!s||typeof s!=`object`&&typeof s!=`function`)throw v(`Component "${e}" in record with path "${o.path}" is not a valid component. Received "${String(s)}".`),Error(`Invalid route component`);if(`then`in s){v(`Component "${e}" in record with path "${o.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);let t=s;s=()=>t}else s.__asyncLoader&&!s.__warnedDefineAsync&&(s.__warnedDefineAsync=!0,v(`Component "${e}" in record with path "${o.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!==`beforeRouteEnter`&&!o.instances[e]))if(d(s)){let c=(s.__vccOpts||s)[t];c&&a.push(Q(c,n,r,o,e,i))}else{let c=s();process.env.NODE_ENV!==`production`&&!(`catch`in c)&&(v(`Component "${e}" in record with path "${o.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),c=Promise.resolve(c)),a.push(()=>c.then(a=>{if(!a)throw Error(`Couldn't resolve component "${e}" at "${o.path}"`);let s=f(a)?a.default:a;o.mods[e]=a,o.components[e]=s;let c=(s.__vccOpts||s)[t];return c&&Q(c,n,r,o,e,i)()}))}}return a}function bt(e){return e.matched.every(e=>e.redirect)?Promise.reject(Error(`Cannot load a route that redirects.`)):Promise.all(e.matched.map(e=>e.components&&Promise.all(Object.keys(e.components).reduce((t,n)=>{let r=e.components[n];return typeof r==`function`&&!(`displayName`in r)&&t.push(r().then(t=>{if(!t)return Promise.reject(Error(`Couldn't resolve component "${n}" at "${e.path}". Ensure you passed a function that returns a promise.`));let r=f(t)?t.default:t;e.mods[n]=t,e.components[n]=r})),t},[])))).then(()=>e)}function xt(e,t){let n=[],r=[],i=[],a=Math.max(t.matched.length,e.matched.length);for(let o=0;o<a;o++){let a=t.matched[o];a&&(e.matched.find(e=>j(e,a))?r.push(a):n.push(a));let s=e.matched[o];s&&(t.matched.find(e=>j(e,s))||i.push(s))}return[n,r,i]}function St(e){let t=(0,c.inject)(J),n=(0,c.inject)(Y),r=!1,i=null,a=(0,c.computed)(()=>{let n=(0,c.unref)(e.to);return process.env.NODE_ENV!==`production`&&(!r||n!==i)&&(B(n)||(r?v(`Invalid value for prop "to" in useLink()
- to:`,n,`
- previous to:`,i,`
- props:`,e):v(`Invalid value for prop "to" in useLink()
- to:`,n,`
- props:`,e)),i=n,r=!0),t.resolve(n)}),o=(0,c.computed)(()=>{let{matched:e}=a.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;let o=i.findIndex(j.bind(null,r));if(o>-1)return o;let s=Ot(e[t-2]);return t>1&&Ot(r)===s&&i[i.length-1].path!==s?i.findIndex(j.bind(null,e[t-2])):o}),s=(0,c.computed)(()=>o.value>-1&&Dt(n.params,a.value.params)),l=(0,c.computed)(()=>o.value>-1&&o.value===n.matched.length-1&&M(n.params,a.value.params));function d(n={}){if(Et(n)){let n=t[(0,c.unref)(e.replace)?`replace`:`push`]((0,c.unref)(e.to)).catch(h);return e.viewTransition&&typeof document<`u`&&`startViewTransition`in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}if(process.env.NODE_ENV!==`production`&&u){let t=(0,c.getCurrentInstance)();if(t){let n={route:a.value,isActive:s.value,isExactActive:l.value,error:null};t.__vrl_devtools=t.__vrl_devtools||[],t.__vrl_devtools.push(n),(0,c.watchEffect)(()=>{n.route=a.value,n.isActive=s.value,n.isExactActive=l.value,n.error=B((0,c.unref)(e.to))?null:`Invalid "to" value`},{flush:`post`})}}return{route:a,href:(0,c.computed)(()=>a.value.href),isActive:s,isExactActive:l,navigate:d}}function Ct(e){return e.length===1?e[0]:e}const wt=(0,c.defineComponent)({name:`RouterLink`,compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:`page`},viewTransition:Boolean},useLink:St,setup(e,{slots:t}){let n=(0,c.reactive)(St(e)),{options:r}=(0,c.inject)(J),i=(0,c.computed)(()=>({[kt(e.activeClass,r.linkActiveClass,`router-link-active`)]:n.isActive,[kt(e.exactActiveClass,r.linkExactActiveClass,`router-link-exact-active`)]:n.isExactActive}));return()=>{let r=t.default&&Ct(t.default(n));return e.custom?r:(0,c.h)(`a`,{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},r)}}}),Tt=wt;function Et(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){let t=e.currentTarget.getAttribute(`target`);if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Dt(e,t){for(let n in t){let r=t[n],i=e[n];if(typeof r==`string`){if(r!==i)return!1}else if(!g(i)||i.length!==r.length||r.some((e,t)=>e!==i[t]))return!1}return!0}function Ot(e){return e?e.aliasOf?e.aliasOf.path:e.path:``}const kt=(e,t,n)=>e??t??n,At=(0,c.defineComponent)({name:`RouterView`,inheritAttrs:!1,props:{name:{type:String,default:`default`},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){process.env.NODE_ENV!==`production`&&Nt();let r=(0,c.inject)(X),i=(0,c.computed)(()=>e.route||r.value),a=(0,c.inject)(mt,0),o=(0,c.computed)(()=>{let e=(0,c.unref)(a),{matched:t}=i.value,n;for(;(n=t[e])&&!n.components;)e++;return e}),s=(0,c.computed)(()=>i.value.matched[o.value]);(0,c.provide)(mt,(0,c.computed)(()=>o.value+1)),(0,c.provide)(q,s),(0,c.provide)(X,i);let l=(0,c.ref)();return(0,c.watch)(()=>[l.value,s.value,e.name],([e,t,n],[r,i,a])=>{t&&(t.instances[n]=e,i&&i!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=i.leaveGuards),t.updateGuards.size||(t.updateGuards=i.updateGuards))),e&&t&&(!i||!j(t,i)||!r)&&(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:`post`}),()=>{let r=i.value,a=e.name,d=s.value,f=d&&d.components[a];if(!f)return jt(n.default,{Component:f,route:r});let m=d.props[a],h=m?m===!0?r.params:typeof m==`function`?m(r):m:null,_=(0,c.h)(f,p({},h,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(d.instances[a]=null)},ref:l}));if(process.env.NODE_ENV!==`production`&&u&&_.ref){let e={depth:o.value,name:d.name,path:d.path,meta:d.meta};(g(_.ref)?_.ref.map(e=>e.i):[_.ref.i]).forEach(t=>{t.__vrv_devtools=e})}return jt(n.default,{Component:_,route:r})||_}}});function jt(e,t){if(!e)return null;let n=e(t);return n.length===1?n[0]:n}const Mt=At;function Nt(){let e=(0,c.getCurrentInstance)(),t=e.parent&&e.parent.type.name,n=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t===`KeepAlive`||t.includes(`Transition`))&&typeof n==`object`&&n.name===`RouterView`){let e=t===`KeepAlive`?`keep-alive`:`transition`;v(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${e}>\n    <component :is="Component" />\n  </${e}>\n</router-view>`)}}function $(e,t){let n=p({},e,{matched:e.matched.map(e=>$t(e,[`instances`,`children`,`aliasOf`]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:n}}}function Pt(e){return{_custom:{display:e}}}let Ft=0;function It(e,t,n){if(t.__hasDevtools)return;t.__hasDevtools=!0;let r=Ft++;(0,l.setupDevtoolsPlugin)({id:`org.vuejs.router`+(r?`.`+r:``),label:`Vue Router`,packageName:`vue-router`,homepage:`https://router.vuejs.org`,logo:`https://router.vuejs.org/logo.png`,componentStateTypes:[`Routing`],app:e},i=>{typeof i.now!=`function`&&v(`[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.`),i.on.inspectComponent((e,n)=>{e.instanceData&&e.instanceData.state.push({type:`Routing`,key:`$route`,editable:!1,value:$(t.currentRoute.value,`Current Route`)})}),i.on.visitComponentTree(({treeNode:e,componentInstance:t})=>{if(t.__vrv_devtools){let n=t.__vrv_devtools;e.tags.push({label:(n.name?`${n.name.toString()}: `:``)+n.path,textColor:0,tooltip:`This component is rendered by &lt;router-view&gt;`,backgroundColor:zt})}g(t.__vrl_devtools)&&(t.__devtoolsApi=i,t.__vrl_devtools.forEach(t=>{let n=t.route.path,r=Ut,i=``,a=0;t.error?(n=t.error,r=Gt,a=Kt):t.isExactActive?(r=Vt,i=`This is exactly active`):t.isActive&&(r=Bt,i=`This link is active`),e.tags.push({label:n,textColor:a,tooltip:i,backgroundColor:r})}))}),(0,c.watch)(t.currentRoute,()=>{l(),i.notifyComponentUpdate(),i.sendInspectorTree(s),i.sendInspectorState(s)});let a=`router:navigations:`+r;i.addTimelineLayer({id:a,label:`Router${r?` `+r:``} Navigations`,color:4237508}),t.onError((e,t)=>{i.addTimelineEvent({layerId:a,event:{title:`Error during Navigation`,subtitle:t.fullPath,logType:`error`,time:i.now(),data:{error:e},groupId:t.meta.__navigationId}})});let o=0;t.beforeEach((e,t)=>{let n={guard:Pt(`beforeEach`),from:$(t,`Current Location during this navigation`),to:$(e,`Target location`)};Object.defineProperty(e.meta,`__navigationId`,{value:o++}),i.addTimelineEvent({layerId:a,event:{time:i.now(),title:`Start of navigation`,subtitle:e.fullPath,data:n,groupId:e.meta.__navigationId}})}),t.afterEach((e,t,n)=>{let r={guard:Pt(`afterEach`)};n?(r.failure={_custom:{type:Error,readOnly:!0,display:n?n.message:``,tooltip:`Navigation Failure`,value:n}},r.status=Pt(`❌`)):r.status=Pt(`✅`),r.from=$(t,`Current Location during this navigation`),r.to=$(e,`Target location`),i.addTimelineEvent({layerId:a,event:{title:`End of navigation`,subtitle:e.fullPath,time:i.now(),data:r,logType:n?`warning`:`default`,groupId:e.meta.__navigationId}})});let s=`router-inspector:`+r;i.addInspector({id:s,label:`Routes`+(r?` `+r:``),icon:`book`,treeFilterPlaceholder:`Search routes`});function l(){if(!u)return;let e=u,r=n.getRoutes().filter(e=>!e.parent||!e.parent.record.components);r.forEach(Zt),e.filter&&(r=r.filter(t=>Qt(t,e.filter.toLowerCase()))),r.forEach(e=>Xt(e,t.currentRoute.value)),e.rootNodes=r.map(qt)}let u;i.on.getInspectorTree(t=>{u=t,t.app===e&&t.inspectorId===s&&l()}),i.on.getInspectorState(t=>{if(t.app===e&&t.inspectorId===s){let e=n.getRoutes().find(e=>e.record.__vd_id===t.nodeId);e&&(t.state={options:Rt(e)})}}),i.sendInspectorTree(s),i.sendInspectorState(s)})}function Lt(e){return e.optional?e.repeatable?`*`:`?`:e.repeatable?`+`:``}function Rt(e){let{record:t}=e,n=[{editable:!1,key:`path`,value:t.path}];return t.name!=null&&n.push({editable:!1,key:`name`,value:t.name}),n.push({editable:!1,key:`regexp`,value:e.re}),e.keys.length&&n.push({editable:!1,key:`keys`,value:{_custom:{type:null,readOnly:!0,display:e.keys.map(e=>`${e.name}${Lt(e)}`).join(` `),tooltip:`Param keys`,value:e.keys}}}),t.redirect!=null&&n.push({editable:!1,key:`redirect`,value:t.redirect}),e.alias.length&&n.push({editable:!1,key:`aliases`,value:e.alias.map(e=>e.record.path)}),Object.keys(e.record.meta).length&&n.push({editable:!1,key:`meta`,value:e.record.meta}),n.push({key:`score`,editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(e=>e.join(`, `)).join(` | `),tooltip:`Score used to sort routes`,value:e.score}}}),n}const zt=15485081,Bt=2450411,Vt=8702998,Ht=2282478,Ut=16486972,Wt=6710886,Gt=16704226,Kt=12131356;function qt(e){let t=[],{record:n}=e;n.name!=null&&t.push({label:String(n.name),textColor:0,backgroundColor:2282478}),n.aliasOf&&t.push({label:`alias`,textColor:0,backgroundColor:Ut}),e.__vd_match&&t.push({label:`matches`,textColor:0,backgroundColor:zt}),e.__vd_exactActive&&t.push({label:`exact`,textColor:0,backgroundColor:Vt}),e.__vd_active&&t.push({label:`active`,textColor:0,backgroundColor:Bt}),n.redirect&&t.push({label:typeof n.redirect==`string`?`redirect: ${n.redirect}`:`redirects`,textColor:16777215,backgroundColor:6710886});let r=n.__vd_id;return r??(r=String(Jt++),n.__vd_id=r),{id:r,label:n.path,tags:t,children:e.children.map(qt)}}let Jt=0;const Yt=/^\/(.*)\/([a-z]*)$/;function Xt(e,t){let n=t.matched.length&&j(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=n,n||(e.__vd_active=t.matched.some(t=>j(t,e.record))),e.children.forEach(e=>Xt(e,t))}function Zt(e){e.__vd_match=!1,e.children.forEach(Zt)}function Qt(e,t){let n=String(e.re).match(Yt);if(e.__vd_match=!1,!n||n.length<3)return!1;if(new RegExp(n[1].replace(/\$$/,``),n[2]).test(t))return e.children.forEach(e=>Qt(e,t)),e.record.path!==`/`||t===`/`?(e.__vd_match=e.re.test(t),!0):!1;let r=e.record.path.toLowerCase(),i=O(r);return!t.startsWith(`/`)&&(i.includes(t)||r.includes(t))||i.startsWith(t)||r.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(e=>Qt(e,t))}function $t(e,t){let n={};for(let r in e)t.includes(r)||(n[r]=e[r]);return n}function en(e){let t=Ze(e.routes,e),n=e.parseQuery||dt,r=e.stringifyQuery||ft,i=e.history;if(process.env.NODE_ENV!==`production`&&!i)throw Error(`Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history`);let a=Z(),o=Z(),s=Z(),l=(0,c.shallowRef)(P),d=P;u&&e.scrollBehavior&&`scrollRestoration`in history&&(history.scrollRestoration=`manual`);let f=m.bind(null,e=>``+e),_=m.bind(null,ue),y=m.bind(null,O);function ee(e,n){let r,i;return Ne(e)?(r=t.getRecordMatcher(e),process.env.NODE_ENV!==`production`&&!r&&v(`Parent route "${String(e)}" not found when adding child route`,n),i=n):i=e,t.addRoute(i,r)}function te(e){let n=t.getRecordMatcher(e);n?t.removeRoute(n):process.env.NODE_ENV!==`production`&&v(`Cannot remove non-existent route "${String(e)}"`)}function ne(){return t.getRoutes().map(e=>e.record)}function re(e){return!!t.getRecordMatcher(e)}function b(e,a){if(a=p({},a||l.value),typeof e==`string`){let r=fe(n,e,a.path),o=t.resolve({path:r.path},a),s=i.createHref(r.fullPath);return process.env.NODE_ENV!==`production`&&(s.startsWith(`//`)?v(`Location "${e}" resolved to "${s}". A resolved location cannot start with multiple slashes.`):o.matched.length||v(`No match found for location with path "${e}"`)),p(r,o,{params:y(o.params),hash:O(r.hash),redirectedFrom:void 0,href:s})}if(process.env.NODE_ENV!==`production`&&!B(e))return v(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,e),b({});let o;if(e.path!=null)process.env.NODE_ENV!==`production`&&`params`in e&&!(`name`in e)&&Object.keys(e.params).length&&v(`Path "${e.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),o=p({},e,{path:fe(n,e.path,a.path).path});else{let t=p({},e.params);for(let e in t)t[e]??delete t[e];o=p({},e,{params:_(t)}),a.params=_(a.params)}let s=t.resolve(o,a),c=e.hash||``;process.env.NODE_ENV!==`production`&&c&&!c.startsWith(`#`)&&v(`A \`hash\` should always start with the character "#". Replace "${c}" with "#${c}".`),s.params=f(y(s.params));let u=pe(r,p({},e,{hash:ce(c),path:s.path})),d=i.createHref(u);return process.env.NODE_ENV!==`production`&&(d.startsWith(`//`)?v(`Location "${e}" resolved to "${d}". A resolved location cannot start with multiple slashes.`):s.matched.length||v(`No match found for location with path "${e.path==null?e:e.path}"`)),p({fullPath:u,hash:c,query:r===ft?pt(e.query):e.query||{}},s,{redirectedFrom:void 0,href:d})}function x(e){return typeof e==`string`?fe(n,e,l.value.path):p({},e)}function ie(e,t){if(d!==e)return H(V.NAVIGATION_CANCELLED,{from:t,to:e})}function S(e){return C(e)}function ae(e){return S(p(x(e),{replace:!0}))}function oe(e,t){let n=e.matched[e.matched.length-1];if(n&&n.redirect){let{redirect:r}=n,i=typeof r==`function`?r(e,t):r;if(typeof i==`string`&&(i=i.includes(`?`)||i.includes(`#`)?i=x(i):{path:i},i.params={}),process.env.NODE_ENV!==`production`&&i.path==null&&!(`name`in i))throw v(`Invalid redirect found:\n${JSON.stringify(i,null,2)}\n when navigating to "${e.fullPath}". A redirect must contain a name or path. This will break in production.`),Error(`Invalid redirect`);return p({query:e.query,hash:e.hash,params:i.path==null?e.params:{}},i)}}function C(e,t){let n=d=b(e),i=l.value,a=e.state,o=e.force,s=e.replace===!0,c=oe(n,i);if(c)return C(p(x(c),{state:typeof c==`object`?p({},a,c.state):a,force:o,replace:s}),t||n);let u=n;u.redirectedFrom=t;let f;return!o&&me(r,i,n)&&(f=H(V.NAVIGATION_DUPLICATED,{to:u,from:i}),ge(i,i,!0,!1)),(f?Promise.resolve(f):T(u,i)).catch(e=>U(e)?U(e,V.NAVIGATION_GUARD_REDIRECT)?e:N(e):M(e,u,i)).then(e=>{if(e){if(U(e,V.NAVIGATION_GUARD_REDIRECT))return process.env.NODE_ENV!==`production`&&me(r,b(e.to),u)&&t&&(t._count=t._count?t._count+1:1)>30?(v(`Detected a possibly infinite redirection in a navigation guard when going from "${i.fullPath}" to "${u.fullPath}". Aborting to avoid a Stack Overflow.\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(Error(`Infinite redirect in navigation guard`))):C(p({replace:s},x(e.to),{state:typeof e.to==`object`?p({},a,e.to.state):a,force:o}),t||u)}else e=le(u,i,!0,s,a);return E(u,i,e),e})}function se(e,t){let n=ie(e,t);return n?Promise.reject(n):Promise.resolve()}function w(e){let t=L.values().next().value;return t&&typeof t.runWithContext==`function`?t.runWithContext(e):e()}function T(e,t){let n,[r,i,s]=xt(e,t);n=yt(r.reverse(),`beforeRouteLeave`,e,t);for(let i of r)i.leaveGuards.forEach(r=>{n.push(Q(r,e,t))});let c=se.bind(null,e,t);return n.push(c),z(n).then(()=>{n=[];for(let r of a.list())n.push(Q(r,e,t));return n.push(c),z(n)}).then(()=>{n=yt(i,`beforeRouteUpdate`,e,t);for(let r of i)r.updateGuards.forEach(r=>{n.push(Q(r,e,t))});return n.push(c),z(n)}).then(()=>{n=[];for(let r of s)if(r.beforeEnter)if(g(r.beforeEnter))for(let i of r.beforeEnter)n.push(Q(i,e,t));else n.push(Q(r.beforeEnter,e,t));return n.push(c),z(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=yt(s,`beforeRouteEnter`,e,t,w),n.push(c),z(n))).then(()=>{n=[];for(let r of o.list())n.push(Q(r,e,t));return n.push(c),z(n)}).catch(e=>U(e,V.NAVIGATION_CANCELLED)?e:Promise.reject(e))}function E(e,t,n){s.list().forEach(r=>w(()=>r(e,t,n)))}function le(e,t,n,r,a){let o=ie(e,t);if(o)return o;let s=t===P,c=u?history.state:{};n&&(r||s?i.replace(e.fullPath,p({scroll:s&&c&&c.scroll},a)):i.push(e.fullPath,a)),l.value=e,ge(e,t,n,s),N()}let D;function de(){D||=i.listen((e,t,n)=>{if(!R.listening)return;let r=b(e),a=oe(r,R.currentRoute.value);if(a){C(p(a,{replace:!0,force:!0}),r).catch(h);return}d=r;let o=l.value;u&&Ce(xe(o.fullPath,n.delta),ye()),T(r,o).catch(e=>U(e,V.NAVIGATION_ABORTED|V.NAVIGATION_CANCELLED)?e:U(e,V.NAVIGATION_GUARD_REDIRECT)?(C(p(x(e.to),{force:!0}),r).then(e=>{U(e,V.NAVIGATION_ABORTED|V.NAVIGATION_DUPLICATED)&&!n.delta&&n.type===F.pop&&i.go(-1,!1)}).catch(h),Promise.reject()):(n.delta&&i.go(-n.delta,!1),M(e,r,o))).then(e=>{e||=le(r,o,!1),e&&(n.delta&&!U(e,V.NAVIGATION_CANCELLED)?i.go(-n.delta,!1):n.type===F.pop&&U(e,V.NAVIGATION_ABORTED|V.NAVIGATION_DUPLICATED)&&i.go(-1,!1)),E(r,o,e)}).catch(h)})}let k=Z(),A=Z(),j;function M(e,t,n){N(e);let r=A.list();return r.length?r.forEach(r=>r(e,t,n)):(process.env.NODE_ENV!==`production`&&v(`uncaught error during route navigation:`),console.error(e)),Promise.reject(e)}function he(){return j&&l.value!==P?Promise.resolve():new Promise((e,t)=>{k.add([e,t])})}function N(e){return j||(j=!e,de(),k.list().forEach(([t,n])=>e?n(e):t()),k.reset()),e}function ge(t,n,r,i){let{scrollBehavior:a}=e;if(!u||!a)return Promise.resolve();let o=!r&&we(xe(t.fullPath,0))||(i||!r)&&history.state&&history.state.scroll||null;return(0,c.nextTick)().then(()=>a(t,n,o)).then(e=>e&&be(e)).catch(e=>M(e,t,n))}let I=e=>i.go(e),_e,L=new Set,R={currentRoute:l,listening:!0,addRoute:ee,removeRoute:te,clearRoutes:t.clearRoutes,hasRoute:re,getRoutes:ne,resolve:b,options:e,push:S,replace:ae,go:I,back:()=>I(-1),forward:()=>I(1),beforeEach:a.add,beforeResolve:o.add,afterEach:s.add,onError:A.add,isReady:he,install(e){e.component(`RouterLink`,Tt),e.component(`RouterView`,Mt),e.config.globalProperties.$router=R,Object.defineProperty(e.config.globalProperties,`$route`,{enumerable:!0,get:()=>(0,c.unref)(l)}),u&&!_e&&l.value===P&&(_e=!0,S(i.location).catch(e=>{process.env.NODE_ENV!==`production`&&v(`Unexpected error when starting the router:`,e)}));let n={};for(let e in P)Object.defineProperty(n,e,{get:()=>l.value[e],enumerable:!0});e.provide(J,R),e.provide(Y,(0,c.shallowReactive)(n)),e.provide(X,l);let r=e.unmount;L.add(e),e.unmount=function(){L.delete(e),L.size<1&&(d=P,D&&D(),D=null,l.value=P,_e=!1,j=!1),r()},process.env.NODE_ENV!==`production`&&u&&It(e,R,t)}};function z(e){return e.reduce((e,t)=>e.then(()=>w(t)),Promise.resolve())}return R}function tn(){return(0,c.inject)(J)}function nn(e){return(0,c.inject)(Y)}exports.NavigationFailureType=Fe,exports.RouterLink=Tt,exports.RouterView=Mt,exports.START_LOCATION=P,exports.createMemoryHistory=je,exports.createRouter=en,exports.createRouterMatcher=Ze,exports.createWebHashHistory=Me,exports.createWebHistory=Ae,exports.isNavigationFailure=U,exports.loadRouteLocation=bt,exports.matchedRouteKey=q,exports.onBeforeRouteLeave=gt,exports.onBeforeRouteUpdate=_t,exports.parseQuery=dt,exports.routeLocationKey=Y,exports.routerKey=J,exports.routerViewLocationKey=X,exports.stringifyQuery=ft,exports.useLink=St,exports.useRoute=nn,exports.useRouter=tn,exports.viewDepthKey=mt;