/*!
* vue-router v4.6.3
* (c) 2025 <PERSON>
* @license MIT
*/
var VueRouter=(function(e,t){var n=Object.create,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,c=(e,t,n,o)=>{if(t&&typeof t==`object`||typeof t==`function`)for(var c=a(t),l=0,u=c.length,d;l<u;l++)d=c[l],!s.call(e,d)&&d!==n&&r(e,d,{get:(e=>t[e]).bind(null,d),enumerable:!(o=i(t,d))||o.enumerable});return e};t=((e,t,i)=>(i=e==null?{}:n(o(e)),c(t||!e||!e.__esModule?r(i,`default`,{value:e,enumerable:!0}):i,e)))(t);let l=typeof document<`u`;function u(e){return typeof e==`object`||`displayName`in e||`props`in e||`__vccOpts`in e}function d(e){return e.__esModule||e[Symbol.toStringTag]===`Module`||e.default&&u(e.default)}let f=Object.assign;function p(e,t){let n={};for(let r in t){let i=t[r];n[r]=h(i)?i.map(e):e(i)}return n}let m=()=>{},h=Array.isArray;function g(e,t){let n={};for(let r in e)n[r]=r in t?t[r]:e[r];return n}let _=/#/g,ee=/&/g,te=/\//g,ne=/=/g,re=/\?/g,ie=/\+/g,v=/%5B/g,y=/%5D/g,b=/%5E/g,x=/%60/g,ae=/%7B/g,oe=/%7C/g,S=/%7D/g,se=/%20/g;function C(e){return e==null?``:encodeURI(``+e).replace(oe,`|`).replace(v,`[`).replace(y,`]`)}function ce(e){return C(e).replace(ae,`{`).replace(S,`}`).replace(b,`^`)}function w(e){return C(e).replace(ie,`%2B`).replace(se,`+`).replace(_,`%23`).replace(ee,`%26`).replace(x,"`").replace(ae,`{`).replace(S,`}`).replace(b,`^`)}function le(e){return w(e).replace(ne,`%3D`)}function ue(e){return C(e).replace(_,`%23`).replace(re,`%3F`)}function de(e){return ue(e).replace(te,`%2F`)}function T(e){if(e==null)return null;try{return decodeURIComponent(``+e)}catch{}return``+e}let E=/\/$/,fe=e=>e.replace(E,``);function pe(e,t,n=`/`){let r,i={},a=``,o=``,s=t.indexOf(`#`),c=t.indexOf(`?`);return c=s>=0&&c>s?-1:c,c>=0&&(r=t.slice(0,c),a=t.slice(c,s>0?s:t.length),i=e(a.slice(1))),s>=0&&(r||=t.slice(0,s),o=t.slice(s,t.length)),r=j(r??t,n),{fullPath:r+a+o,path:r,query:i,hash:T(o)}}function me(e,t){let n=t.query?e(t.query):``;return t.path+(n&&`?`)+n+(t.hash||``)}function D(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||`/`}function he(e,t,n){let r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&O(t.matched[r],n.matched[i])&&k(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function O(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function k(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let n in e)if(!A(e[n],t[n]))return!1;return!0}function A(e,t){return h(e)?ge(e,t):h(t)?ge(t,e):e===t}function ge(e,t){return h(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):e.length===1&&e[0]===t}function j(e,t){if(e.startsWith(`/`))return e;if(!e)return t;let n=t.split(`/`),r=e.split(`/`),i=r[r.length-1];(i===`..`||i===`.`)&&r.push(``);let a=n.length-1,o,s;for(o=0;o<r.length;o++)if(s=r[o],s!==`.`)if(s===`..`)a>1&&a--;else break;return n.slice(0,a).join(`/`)+`/`+r.slice(o).join(`/`)}let M={path:`/`,name:void 0,params:{},query:{},hash:``,fullPath:`/`,matched:[],meta:{},redirectedFrom:void 0},N=function(e){return e.pop=`pop`,e.push=`push`,e}({}),P=function(e){return e.back=`back`,e.forward=`forward`,e.unknown=``,e}({});function F(e){if(!e)if(l){let t=document.querySelector(`base`);e=t&&t.getAttribute(`href`)||`/`,e=e.replace(/^\w+:\/\/[^\/]+/,``)}else e=`/`;return e[0]!==`/`&&e[0]!==`#`&&(e=`/`+e),fe(e)}let I=/^[^#]+#/;function L(e,t){return e.replace(I,`#`)+t}function R(e,t){let n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}let z=()=>({left:window.scrollX,top:window.scrollY});function _e(e){let t;if(`el`in e){let n=e.el,r=typeof n==`string`&&n.startsWith(`#`),i=typeof n==`string`?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=R(i,e)}else t=e;`scrollBehavior`in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left==null?window.scrollX:t.left,t.top==null?window.scrollY:t.top)}function ve(e,t){return(history.state?history.state.position-t:-1)+e}let B=new Map;function ye(e,t){B.set(e,t)}function be(e){let t=B.get(e);return B.delete(e),t}let xe=()=>location.protocol+`//`+location.host;function Se(e,t){let{pathname:n,search:r,hash:i}=t,a=e.indexOf(`#`);if(a>-1){let t=i.includes(e.slice(a))?e.slice(a).length:1,n=i.slice(t);return n[0]!==`/`&&(n=`/`+n),D(n,``)}return D(n,e)+r+i}function Ce(e,t,n,r){let i=[],a=[],o=null,s=({state:a})=>{let s=Se(e,location),c=n.value,l=t.value,u=0;if(a){if(n.value=s,t.value=a,o&&o===c){o=null;return}u=l?a.position-l.position:0}else r(s);i.forEach(e=>{e(n.value,c,{delta:u,type:N.pop,direction:u?u>0?P.forward:P.back:P.unknown})})};function c(){o=n.value}function l(e){i.push(e);let t=()=>{let t=i.indexOf(e);t>-1&&i.splice(t,1)};return a.push(t),t}function u(){if(document.visibilityState===`hidden`){let{history:e}=window;if(!e.state)return;e.replaceState(f({},e.state,{scroll:z()}),``)}}function d(){for(let e of a)e();a=[],window.removeEventListener(`popstate`,s),window.removeEventListener(`pagehide`,u),document.removeEventListener(`visibilitychange`,u)}return window.addEventListener(`popstate`,s),window.addEventListener(`pagehide`,u),document.addEventListener(`visibilitychange`,u),{pauseListeners:c,listen:l,destroy:d}}function we(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?z():null}}function Te(e){let{history:t,location:n}=window,r={value:Se(e,n)},i={value:t.state};i.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(r,a,o){let s=e.indexOf(`#`),c=s>-1?(n.host&&document.querySelector(`base`)?e:e.slice(s))+r:xe()+e+r;try{t[o?`replaceState`:`pushState`](a,``,c),i.value=a}catch(e){console.error(e),n[o?`replace`:`assign`](c)}}function o(e,n){a(e,f({},t.state,we(i.value.back,e,i.value.forward,!0),n,{position:i.value.position}),!0),r.value=e}function s(e,n){let o=f({},i.value,t.state,{forward:e,scroll:z()});a(o.current,o,!0),a(e,f({},we(r.value,e,null),{position:o.position+1},n),!1),r.value=e}return{location:r,state:i,push:s,replace:o}}function Ee(e){e=F(e);let t=Te(e),n=Ce(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}let i=f({location:``,base:e,go:r,createHref:L.bind(null,e)},t,n);return Object.defineProperty(i,`location`,{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,`state`,{enumerable:!0,get:()=>t.state.value}),i}function De(e=``){let t=[],n=[[``,{}]],r=0;e=F(e);function i(e,t={}){r++,r!==n.length&&n.splice(r),n.push([e,t])}function a(e,n,{direction:r,delta:i}){let a={direction:r,delta:i,type:N.pop};for(let r of t)r(e,n,a)}let o={location:``,state:{},base:e,createHref:L.bind(null,e),replace(e,t){n.splice(r--,1),i(e,t)},push(e,t){i(e,t)},listen(e){return t.push(e),()=>{let n=t.indexOf(e);n>-1&&t.splice(n,1)}},destroy(){t=[],n=[[``,{}]],r=0},go(e,t=!0){let i=this.location,o=e<0?P.back:P.forward;r=Math.max(0,Math.min(r+e,n.length-1)),t&&a(this.location,i,{direction:o,delta:e})}};return Object.defineProperty(o,`location`,{enumerable:!0,get:()=>n[r][0]}),Object.defineProperty(o,`state`,{enumerable:!0,get:()=>n[r][1]}),o}function Oe(e){return e=location.host?e||location.pathname+location.search:``,e.includes(`#`)||(e+=`#`),Ee(e)}function ke(e){return typeof e==`string`||e&&typeof e==`object`}function Ae(e){return typeof e==`string`||typeof e==`symbol`}let V=function(e){return e[e.MATCHER_NOT_FOUND=1]=`MATCHER_NOT_FOUND`,e[e.NAVIGATION_GUARD_REDIRECT=2]=`NAVIGATION_GUARD_REDIRECT`,e[e.NAVIGATION_ABORTED=4]=`NAVIGATION_ABORTED`,e[e.NAVIGATION_CANCELLED=8]=`NAVIGATION_CANCELLED`,e[e.NAVIGATION_DUPLICATED=16]=`NAVIGATION_DUPLICATED`,e}({}),je=Symbol(``),Me=function(e){return e[e.aborted=4]=`aborted`,e[e.cancelled=8]=`cancelled`,e[e.duplicated=16]=`duplicated`,e}({});V.MATCHER_NOT_FOUND,V.NAVIGATION_GUARD_REDIRECT,V.NAVIGATION_ABORTED,V.NAVIGATION_CANCELLED,V.NAVIGATION_DUPLICATED;function H(e,t){return f(Error(),{type:e,[je]:!0},t)}function U(e,t){return e instanceof Error&&je in e&&(t==null||!!(e.type&t))}let W=function(e){return e[e.Static=0]=`Static`,e[e.Param=1]=`Param`,e[e.Group=2]=`Group`,e}({});var G=function(e){return e[e.Static=0]=`Static`,e[e.Param=1]=`Param`,e[e.ParamRegExp=2]=`ParamRegExp`,e[e.ParamRegExpEnd=3]=`ParamRegExpEnd`,e[e.EscapeNext=4]=`EscapeNext`,e}(G||{});let Ne={type:W.Static,value:``},Pe=/[a-zA-Z0-9_]/;function Fe(e){if(!e)return[[]];if(e===`/`)return[[Ne]];if(!e.startsWith(`/`))throw Error(`Invalid path "${e}"`);function t(e){throw Error(`ERR (${n})/"${l}": ${e}`)}let n=G.Static,r=n,i=[],a;function o(){a&&i.push(a),a=[]}let s=0,c,l=``,u=``;function d(){l&&=(n===G.Static?a.push({type:W.Static,value:l}):n===G.Param||n===G.ParamRegExp||n===G.ParamRegExpEnd?(a.length>1&&(c===`*`||c===`+`)&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),a.push({type:W.Param,value:l,regexp:u,repeatable:c===`*`||c===`+`,optional:c===`*`||c===`?`})):t(`Invalid state to consume buffer`),``)}function f(){l+=c}for(;s<e.length;){if(c=e[s++],c===`\\`&&n!==G.ParamRegExp){r=n,n=G.EscapeNext;continue}switch(n){case G.Static:c===`/`?(l&&d(),o()):c===`:`?(d(),n=G.Param):f();break;case G.EscapeNext:f(),n=r;break;case G.Param:c===`(`?n=G.ParamRegExp:Pe.test(c)?f():(d(),n=G.Static,c!==`*`&&c!==`?`&&c!==`+`&&s--);break;case G.ParamRegExp:c===`)`?u[u.length-1]==`\\`?u=u.slice(0,-1)+c:n=G.ParamRegExpEnd:u+=c;break;case G.ParamRegExpEnd:d(),n=G.Static,c!==`*`&&c!==`?`&&c!==`+`&&s--,u=``;break;default:t(`Unknown state`);break}}return n===G.ParamRegExp&&t(`Unfinished custom RegExp for param "${l}"`),d(),o(),i}let Ie=`[^/]+?`,Le={sensitive:!1,strict:!1,start:!0,end:!0};var K=function(e){return e[e._multiplier=10]=`_multiplier`,e[e.Root=90]=`Root`,e[e.Segment=40]=`Segment`,e[e.SubSegment=30]=`SubSegment`,e[e.Static=40]=`Static`,e[e.Dynamic=20]=`Dynamic`,e[e.BonusCustomRegExp=10]=`BonusCustomRegExp`,e[e.BonusWildcard=-50]=`BonusWildcard`,e[e.BonusRepeatable=-20]=`BonusRepeatable`,e[e.BonusOptional=-8]=`BonusOptional`,e[e.BonusStrict=.7000000000000001]=`BonusStrict`,e[e.BonusCaseSensitive=.25]=`BonusCaseSensitive`,e}(K||{});let Re=/[.+*?^${}()[\]/\\]/g;function ze(e,t){let n=f({},Le,t),r=[],i=n.start?`^`:``,a=[];for(let t of e){let e=t.length?[]:[K.Root];n.strict&&!t.length&&(i+=`/`);for(let r=0;r<t.length;r++){let o=t[r],s=K.Segment+(n.sensitive?K.BonusCaseSensitive:0);if(o.type===W.Static)r||(i+=`/`),i+=o.value.replace(Re,`\\$&`),s+=K.Static;else if(o.type===W.Param){let{value:e,repeatable:n,optional:c,regexp:l}=o;a.push({name:e,repeatable:n,optional:c});let u=l||Ie;if(u!==Ie){s+=K.BonusCustomRegExp;try{`${u}`}catch(t){throw Error(`Invalid custom RegExp for param "${e}" (${u}): `+t.message)}}let d=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;r||(d=c&&t.length<2?`(?:/${d})`:`/`+d),c&&(d+=`?`),i+=d,s+=K.Dynamic,c&&(s+=K.BonusOptional),n&&(s+=K.BonusRepeatable),u===`.*`&&(s+=K.BonusWildcard)}e.push(s)}r.push(e)}if(n.strict&&n.end){let e=r.length-1;r[e][r[e].length-1]+=K.BonusStrict}n.strict||(i+=`/?`),n.end?i+=`$`:n.strict&&!i.endsWith(`/`)&&(i+=`(?:/|$)`);let o=new RegExp(i,n.sensitive?``:`i`);function s(e){let t=e.match(o),n={};if(!t)return null;for(let e=1;e<t.length;e++){let r=t[e]||``,i=a[e-1];n[i.name]=r&&i.repeatable?r.split(`/`):r}return n}function c(t){let n=``,r=!1;for(let i of e){(!r||!n.endsWith(`/`))&&(n+=`/`),r=!1;for(let e of i)if(e.type===W.Static)n+=e.value;else if(e.type===W.Param){let{value:a,repeatable:o,optional:s}=e,c=a in t?t[a]:``;if(h(c)&&!o)throw Error(`Provided param "${a}" is an array but it is not repeatable (* or + modifiers)`);let l=h(c)?c.join(`/`):c;if(!l)if(s)i.length<2&&(n.endsWith(`/`)?n=n.slice(0,-1):r=!0);else throw Error(`Missing required param "${a}"`);n+=l}}return n||`/`}return{re:o,score:r,keys:a,parse:s,stringify:c}}function Be(e,t){let n=0;for(;n<e.length&&n<t.length;){let r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===K.Static+K.Segment?-1:1:e.length>t.length?t.length===1&&t[0]===K.Static+K.Segment?1:-1:0}function Ve(e,t){let n=0,r=e.score,i=t.score;for(;n<r.length&&n<i.length;){let e=Be(r[n],i[n]);if(e)return e;n++}if(Math.abs(i.length-r.length)===1){if(He(r))return 1;if(He(i))return-1}return i.length-r.length}function He(e){let t=e[e.length-1];return e.length>0&&t[t.length-1]<0}let Ue={strict:!1,end:!0,sensitive:!1};function We(e,t,n){let r=f(ze(Fe(e.path),n),{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Ge(e,t){let n=[],r=new Map;t=g(Ue,t);function i(e){return r.get(e)}function a(e,n,r){let i=!r,s=qe(e);s.aliasOf=r&&r.record;let l=g(t,e),u=[s];if(`alias`in e){let t=typeof e.alias==`string`?[e.alias]:e.alias;for(let e of t)u.push(qe(f({},s,{components:r?r.record.components:s.components,path:e,aliasOf:r?r.record:s})))}let d,p;for(let t of u){let{path:u}=t;if(n&&u[0]!==`/`){let e=n.record.path,r=e[e.length-1]===`/`?``:`/`;t.path=n.record.path+(u&&r+u)}if(d=We(t,n,l),r?r.alias.push(d):(p||=d,p!==d&&p.alias.push(d),i&&e.name&&!Ye(d)&&o(e.name)),$e(d)&&c(d),s.children){let e=s.children;for(let t=0;t<e.length;t++)a(e[t],d,r&&r.children[t])}r||=d}return p?()=>{o(p)}:m}function o(e){if(Ae(e)){let t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{let t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function s(){return n}function c(e){let t=Ze(e,n);n.splice(t,0,e),e.record.name&&!Ye(e)&&r.set(e.record.name,e)}function l(e,t){let i,a={},o,s;if(`name`in e&&e.name){if(i=r.get(e.name),!i)throw H(V.MATCHER_NOT_FOUND,{location:e});s=i.record.name,a=f(Ke(t.params,i.keys.filter(e=>!e.optional).concat(i.parent?i.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Ke(e.params,i.keys.map(e=>e.name))),o=i.stringify(a)}else if(e.path!=null)o=e.path,i=n.find(e=>e.re.test(o)),i&&(a=i.parse(o),s=i.record.name);else{if(i=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!i)throw H(V.MATCHER_NOT_FOUND,{location:e,currentLocation:t});s=i.record.name,a=f({},t.params,e.params),o=i.stringify(a)}let c=[],l=i;for(;l;)c.unshift(l.record),l=l.parent;return{name:s,path:o,params:a,matched:c,meta:Xe(c)}}e.forEach(e=>a(e));function u(){n.length=0,r.clear()}return{addRoute:a,resolve:l,removeRoute:o,clearRoutes:u,getRoutes:s,getRecordMatcher:i}}function Ke(e,t){let n={};for(let r of t)r in e&&(n[r]=e[r]);return n}function qe(e){let t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Je(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:`components`in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,`mods`,{value:{}}),t}function Je(e){let t={},n=e.props||!1;if(`component`in e)t.default=n;else for(let r in e.components)t[r]=typeof n==`object`?n[r]:n;return t}function Ye(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Xe(e){return e.reduce((e,t)=>f(e,t.meta),{})}function Ze(e,t){let n=0,r=t.length;for(;n!==r;){let i=n+r>>1;Ve(e,t[i])<0?r=i:n=i+1}let i=Qe(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function Qe(e){let t=e;for(;t=t.parent;)if($e(t)&&Ve(e,t)===0)return t}function $e({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function et(e){let t={};if(e===``||e===`?`)return t;let n=(e[0]===`?`?e.slice(1):e).split(`&`);for(let e=0;e<n.length;++e){let r=n[e].replace(ie,` `),i=r.indexOf(`=`),a=T(i<0?r:r.slice(0,i)),o=i<0?null:T(r.slice(i+1));if(a in t){let e=t[a];h(e)||(e=t[a]=[e]),e.push(o)}else t[a]=o}return t}function tt(e){let t=``;for(let n in e){let r=e[n];if(n=le(n),r==null){r!==void 0&&(t+=(t.length?`&`:``)+n);continue}(h(r)?r.map(e=>e&&w(e)):[r&&w(r)]).forEach(e=>{e!==void 0&&(t+=(t.length?`&`:``)+n,e!=null&&(t+=`=`+e))})}return t}function nt(e){let t={};for(let n in e){let r=e[n];r!==void 0&&(t[n]=h(r)?r.map(e=>e==null?null:``+e):r==null?r:``+r)}return t}let q=Symbol(``),rt=Symbol(``),J=Symbol(``),Y=Symbol(``),X=Symbol(``);function Z(){let e=[];function t(t){return e.push(t),()=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function it(e,n,r){let i=()=>{e[n].delete(r)};(0,t.onUnmounted)(i),(0,t.onDeactivated)(i),(0,t.onActivated)(()=>{e[n].add(r)}),e[n].add(r)}function at(e){let n=(0,t.inject)(q,{}).value;n&&it(n,`leaveGuards`,e)}function ot(e){let n=(0,t.inject)(q,{}).value;n&&it(n,`updateGuards`,e)}function Q(e,t,n,r,i,a=e=>e()){let o=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((s,c)=>{let l=e=>{e===!1?c(H(V.NAVIGATION_ABORTED,{from:n,to:t})):e instanceof Error?c(e):ke(e)?c(H(V.NAVIGATION_GUARD_REDIRECT,{from:t,to:e})):(o&&r.enterCallbacks[i]===o&&typeof e==`function`&&o.push(e),s())},u=a(()=>e.call(r&&r.instances[i],t,n,l)),d=Promise.resolve(u);e.length<3&&(d=d.then(l)),d.catch(e=>c(e))})}function st(e,t,n,r,i=e=>e()){let a=[];for(let o of e)for(let e in o.components){let s=o.components[e];if(!(t!==`beforeRouteEnter`&&!o.instances[e]))if(u(s)){let c=(s.__vccOpts||s)[t];c&&a.push(Q(c,n,r,o,e,i))}else{let c=s();a.push(()=>c.then(a=>{if(!a)throw Error(`Couldn't resolve component "${e}" at "${o.path}"`);let s=d(a)?a.default:a;o.mods[e]=a,o.components[e]=s;let c=(s.__vccOpts||s)[t];return c&&Q(c,n,r,o,e,i)()}))}}return a}function ct(e){return e.matched.every(e=>e.redirect)?Promise.reject(Error(`Cannot load a route that redirects.`)):Promise.all(e.matched.map(e=>e.components&&Promise.all(Object.keys(e.components).reduce((t,n)=>{let r=e.components[n];return typeof r==`function`&&!(`displayName`in r)&&t.push(r().then(t=>{if(!t)return Promise.reject(Error(`Couldn't resolve component "${n}" at "${e.path}". Ensure you passed a function that returns a promise.`));let r=d(t)?t.default:t;e.mods[n]=t,e.components[n]=r})),t},[])))).then(()=>e)}function lt(e,t){let n=[],r=[],i=[],a=Math.max(t.matched.length,e.matched.length);for(let o=0;o<a;o++){let a=t.matched[o];a&&(e.matched.find(e=>O(e,a))?r.push(a):n.push(a));let s=e.matched[o];s&&(t.matched.find(e=>O(e,s))||i.push(s))}return[n,r,i]}function $(e){let n=(0,t.inject)(J),r=(0,t.inject)(Y),i=(0,t.computed)(()=>{let r=(0,t.unref)(e.to);return n.resolve(r)}),a=(0,t.computed)(()=>{let{matched:e}=i.value,{length:t}=e,n=e[t-1],a=r.matched;if(!n||!a.length)return-1;let o=a.findIndex(O.bind(null,n));if(o>-1)return o;let s=mt(e[t-2]);return t>1&&mt(n)===s&&a[a.length-1].path!==s?a.findIndex(O.bind(null,e[t-2])):o}),o=(0,t.computed)(()=>a.value>-1&&pt(r.params,i.value.params)),s=(0,t.computed)(()=>a.value>-1&&a.value===r.matched.length-1&&k(r.params,i.value.params));function c(r={}){if(ft(r)){let r=n[(0,t.unref)(e.replace)?`replace`:`push`]((0,t.unref)(e.to)).catch(m);return e.viewTransition&&typeof document<`u`&&`startViewTransition`in document&&document.startViewTransition(()=>r),r}return Promise.resolve()}return{route:i,href:(0,t.computed)(()=>i.value.href),isActive:o,isExactActive:s,navigate:c}}function ut(e){return e.length===1?e[0]:e}let dt=(0,t.defineComponent)({name:`RouterLink`,compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:`page`},viewTransition:Boolean},useLink:$,setup(e,{slots:n}){let r=(0,t.reactive)($(e)),{options:i}=(0,t.inject)(J),a=(0,t.computed)(()=>({[ht(e.activeClass,i.linkActiveClass,`router-link-active`)]:r.isActive,[ht(e.exactActiveClass,i.linkExactActiveClass,`router-link-exact-active`)]:r.isExactActive}));return()=>{let i=n.default&&ut(n.default(r));return e.custom?i:(0,t.h)(`a`,{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:a.value},i)}}});function ft(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){let t=e.currentTarget.getAttribute(`target`);if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function pt(e,t){for(let n in t){let r=t[n],i=e[n];if(typeof r==`string`){if(r!==i)return!1}else if(!h(i)||i.length!==r.length||r.some((e,t)=>e!==i[t]))return!1}return!0}function mt(e){return e?e.aliasOf?e.aliasOf.path:e.path:``}let ht=(e,t,n)=>e??t??n,gt=(0,t.defineComponent)({name:`RouterView`,inheritAttrs:!1,props:{name:{type:String,default:`default`},route:Object},compatConfig:{MODE:3},setup(e,{attrs:n,slots:r}){let i=(0,t.inject)(X),a=(0,t.computed)(()=>e.route||i.value),o=(0,t.inject)(rt,0),s=(0,t.computed)(()=>{let e=(0,t.unref)(o),{matched:n}=a.value,r;for(;(r=n[e])&&!r.components;)e++;return e}),c=(0,t.computed)(()=>a.value.matched[s.value]);(0,t.provide)(rt,(0,t.computed)(()=>s.value+1)),(0,t.provide)(q,c),(0,t.provide)(X,a);let l=(0,t.ref)();return(0,t.watch)(()=>[l.value,c.value,e.name],([e,t,n],[r,i,a])=>{t&&(t.instances[n]=e,i&&i!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=i.leaveGuards),t.updateGuards.size||(t.updateGuards=i.updateGuards))),e&&t&&(!i||!O(t,i)||!r)&&(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:`post`}),()=>{let i=a.value,o=e.name,s=c.value,u=s&&s.components[o];if(!u)return _t(r.default,{Component:u,route:i});let d=s.props[o],p=d?d===!0?i.params:typeof d==`function`?d(i):d:null,m=(0,t.h)(u,f({},p,n,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[o]=null)},ref:l}));return _t(r.default,{Component:m,route:i})||m}}});function _t(e,t){if(!e)return null;let n=e(t);return n.length===1?n[0]:n}let vt=gt;function yt(e){let n=Ge(e.routes,e),r=e.parseQuery||et,i=e.stringifyQuery||tt,a=e.history,o=Z(),s=Z(),c=Z(),u=(0,t.shallowRef)(M),d=M;l&&e.scrollBehavior&&`scrollRestoration`in history&&(history.scrollRestoration=`manual`);let g=p.bind(null,e=>``+e),_=p.bind(null,de),ee=p.bind(null,T);function te(e,t){let r,i;return Ae(e)?(r=n.getRecordMatcher(e),i=t):i=e,n.addRoute(i,r)}function ne(e){let t=n.getRecordMatcher(e);t&&n.removeRoute(t)}function re(){return n.getRoutes().map(e=>e.record)}function ie(e){return!!n.getRecordMatcher(e)}function v(e,t){if(t=f({},t||u.value),typeof e==`string`){let i=pe(r,e,t.path),o=n.resolve({path:i.path},t),s=a.createHref(i.fullPath);return f(i,o,{params:ee(o.params),hash:T(i.hash),redirectedFrom:void 0,href:s})}let o;if(e.path!=null)o=f({},e,{path:pe(r,e.path,t.path).path});else{let n=f({},e.params);for(let e in n)n[e]??delete n[e];o=f({},e,{params:_(n)}),t.params=_(t.params)}let s=n.resolve(o,t),c=e.hash||``;s.params=g(ee(s.params));let l=me(i,f({},e,{hash:ce(c),path:s.path})),d=a.createHref(l);return f({fullPath:l,hash:c,query:i===tt?nt(e.query):e.query||{}},s,{redirectedFrom:void 0,href:d})}function y(e){return typeof e==`string`?pe(r,e,u.value.path):f({},e)}function b(e,t){if(d!==e)return H(V.NAVIGATION_CANCELLED,{from:t,to:e})}function x(e){return S(e)}function ae(e){return x(f(y(e),{replace:!0}))}function oe(e,t){let n=e.matched[e.matched.length-1];if(n&&n.redirect){let{redirect:r}=n,i=typeof r==`function`?r(e,t):r;return typeof i==`string`&&(i=i.includes(`?`)||i.includes(`#`)?i=y(i):{path:i},i.params={}),f({query:e.query,hash:e.hash,params:i.path==null?e.params:{}},i)}}function S(e,t){let n=d=v(e),r=u.value,a=e.state,o=e.force,s=e.replace===!0,c=oe(n,r);if(c)return S(f(y(c),{state:typeof c==`object`?f({},a,c.state):a,force:o,replace:s}),t||n);let l=n;l.redirectedFrom=t;let p;return!o&&he(i,r,n)&&(p=H(V.NAVIGATION_DUPLICATED,{to:l,from:r}),P(r,r,!0,!1)),(p?Promise.resolve(p):w(l,r)).catch(e=>U(e)?U(e,V.NAVIGATION_GUARD_REDIRECT)?e:j(e):A(e,l,r)).then(e=>{if(e){if(U(e,V.NAVIGATION_GUARD_REDIRECT))return S(f({replace:s},y(e.to),{state:typeof e.to==`object`?f({},a,e.to.state):a,force:o}),t||l)}else e=ue(l,r,!0,s,a);return le(l,r,e),e})}function se(e,t){let n=b(e,t);return n?Promise.reject(n):Promise.resolve()}function C(e){let t=L.values().next().value;return t&&typeof t.runWithContext==`function`?t.runWithContext(e):e()}function w(e,t){let n,[r,i,a]=lt(e,t);n=st(r.reverse(),`beforeRouteLeave`,e,t);for(let i of r)i.leaveGuards.forEach(r=>{n.push(Q(r,e,t))});let c=se.bind(null,e,t);return n.push(c),B(n).then(()=>{n=[];for(let r of o.list())n.push(Q(r,e,t));return n.push(c),B(n)}).then(()=>{n=st(i,`beforeRouteUpdate`,e,t);for(let r of i)r.updateGuards.forEach(r=>{n.push(Q(r,e,t))});return n.push(c),B(n)}).then(()=>{n=[];for(let r of a)if(r.beforeEnter)if(h(r.beforeEnter))for(let i of r.beforeEnter)n.push(Q(i,e,t));else n.push(Q(r.beforeEnter,e,t));return n.push(c),B(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=st(a,`beforeRouteEnter`,e,t,C),n.push(c),B(n))).then(()=>{n=[];for(let r of s.list())n.push(Q(r,e,t));return n.push(c),B(n)}).catch(e=>U(e,V.NAVIGATION_CANCELLED)?e:Promise.reject(e))}function le(e,t,n){c.list().forEach(r=>C(()=>r(e,t,n)))}function ue(e,t,n,r,i){let o=b(e,t);if(o)return o;let s=t===M,c=l?history.state:{};n&&(r||s?a.replace(e.fullPath,f({scroll:s&&c&&c.scroll},i)):a.push(e.fullPath,i)),u.value=e,P(e,t,n,s),j()}let E;function fe(){E||=a.listen((e,t,n)=>{if(!R.listening)return;let r=v(e),i=oe(r,R.currentRoute.value);if(i){S(f(i,{replace:!0,force:!0}),r).catch(m);return}d=r;let o=u.value;l&&ye(ve(o.fullPath,n.delta),z()),w(r,o).catch(e=>U(e,V.NAVIGATION_ABORTED|V.NAVIGATION_CANCELLED)?e:U(e,V.NAVIGATION_GUARD_REDIRECT)?(S(f(y(e.to),{force:!0}),r).then(e=>{U(e,V.NAVIGATION_ABORTED|V.NAVIGATION_DUPLICATED)&&!n.delta&&n.type===N.pop&&a.go(-1,!1)}).catch(m),Promise.reject()):(n.delta&&a.go(-n.delta,!1),A(e,r,o))).then(e=>{e||=ue(r,o,!1),e&&(n.delta&&!U(e,V.NAVIGATION_CANCELLED)?a.go(-n.delta,!1):n.type===N.pop&&U(e,V.NAVIGATION_ABORTED|V.NAVIGATION_DUPLICATED)&&a.go(-1,!1)),le(r,o,e)}).catch(m)})}let D=Z(),O=Z(),k;function A(e,t,n){j(e);let r=O.list();return r.length?r.forEach(r=>r(e,t,n)):console.error(e),Promise.reject(e)}function ge(){return k&&u.value!==M?Promise.resolve():new Promise((e,t)=>{D.add([e,t])})}function j(e){return k||(k=!e,fe(),D.list().forEach(([t,n])=>e?n(e):t()),D.reset()),e}function P(n,r,i,a){let{scrollBehavior:o}=e;if(!l||!o)return Promise.resolve();let s=!i&&be(ve(n.fullPath,0))||(a||!i)&&history.state&&history.state.scroll||null;return(0,t.nextTick)().then(()=>o(n,r,s)).then(e=>e&&_e(e)).catch(e=>A(e,n,r))}let F=e=>a.go(e),I,L=new Set,R={currentRoute:u,listening:!0,addRoute:te,removeRoute:ne,clearRoutes:n.clearRoutes,hasRoute:ie,getRoutes:re,resolve:v,options:e,push:x,replace:ae,go:F,back:()=>F(-1),forward:()=>F(1),beforeEach:o.add,beforeResolve:s.add,afterEach:c.add,onError:O.add,isReady:ge,install(e){e.component(`RouterLink`,dt),e.component(`RouterView`,vt),e.config.globalProperties.$router=R,Object.defineProperty(e.config.globalProperties,`$route`,{enumerable:!0,get:()=>(0,t.unref)(u)}),l&&!I&&u.value===M&&(I=!0,x(a.location).catch(e=>{}));let n={};for(let e in M)Object.defineProperty(n,e,{get:()=>u.value[e],enumerable:!0});e.provide(J,R),e.provide(Y,(0,t.shallowReactive)(n)),e.provide(X,u);let r=e.unmount;L.add(e),e.unmount=function(){L.delete(e),L.size<1&&(d=M,E&&E(),E=null,u.value=M,I=!1,k=!1),r()}}};function B(e){return e.reduce((e,t)=>e.then(()=>C(t)),Promise.resolve())}return R}function bt(){return(0,t.inject)(J)}function xt(e){return(0,t.inject)(Y)}return e.NavigationFailureType=Me,e.RouterLink=dt,e.RouterView=vt,e.START_LOCATION=M,e.createMemoryHistory=De,e.createRouter=yt,e.createRouterMatcher=Ge,e.createWebHashHistory=Oe,e.createWebHistory=Ee,e.isNavigationFailure=U,e.loadRouteLocation=ct,e.matchedRouteKey=q,e.onBeforeRouteLeave=at,e.onBeforeRouteUpdate=ot,e.parseQuery=et,e.routeLocationKey=Y,e.routerKey=J,e.routerViewLocationKey=X,e.stringifyQuery=tt,e.useLink=$,e.useRoute=xt,e.useRouter=bt,e.viewDepthKey=rt,e})({},Vue);