/**
 * TODO: remove the declaration if possible when merging unplugin-vue-router into this monorepo
 */

declare module 'vue-router/auto-resolver' {
  import type {
    EXPERIMENTAL_Router,
    EXPERIMENTAL_RouterOptions,
  } from 'vue-router/experimental'

  /**
   * Handles HMR.
   * Generated by unplugin-vue-router
   */
  export function handleHotUpdate(router: EXPERIMENTAL_Router): void

  /**
   * Custom resolver automatically generated at build time
   * Generated by unplugin-vue-router
   */
  export const resolver: EXPERIMENTAL_RouterOptions['resolver']
}
