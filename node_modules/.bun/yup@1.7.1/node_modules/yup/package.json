{"name": "yup", "version": "1.7.1", "description": "Dead simple Object schema validation", "main": "index.js", "module": "index.esm.js", "runkitExampleFilename": "./runkit-example.js", "repository": {"type": "git", "url": "git+https://github.com/jquense/yup.git"}, "author": {"name": "@monasticpanic Jason <PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jquense/yup/issues"}, "homepage": "https://github.com/jquense/yup", "release": {"conventionalCommits": true, "publishDir": "lib"}, "prettier": {"singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "dependencies": {"property-expr": "^2.0.5", "tiny-case": "^1.0.3", "toposort": "^2.0.2", "type-fest": "^2.19.0"}, "packageManager": "yarn@4.5.3+sha512.3003a14012e2987072d244c720506549c1aab73ee728208f1b2580a9fd67b92d61ba6b08fe93f6dce68fd771e3af1e59a0afa28dd242dd0940d73b95fedd4e90", "resolutions": {"@4c/rollout@npm:^4.0.2": "patch:@4c/rollout@npm%3A4.0.2#~/.yarn/patches/@4c-rollout-npm-4.0.2-ab2b6d0bab.patch"}}