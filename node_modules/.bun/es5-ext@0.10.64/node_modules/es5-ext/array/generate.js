"use strict";

var toPosInt = require("../number/to-pos-integer")
  , value    = require("../object/valid-value")
  , slice    = Array.prototype.slice;

module.exports = function (length /*, …fill*/) {
	var arr, currentLength;
	length = toPosInt(value(length));
	if (length === 0) return [];

	arr = arguments.length < 2 ? [undefined] : slice.call(arguments, 1, 1 + length);

	while ((currentLength = arr.length) < length) {
		arr = arr.concat(arr.slice(0, length - currentLength));
	}
	return arr;
};
