import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureSafeInteger(value: any, options?: EnsureBaseOptions): number;
declare function ensureSafeInteger(value: any, options?: EnsureBaseOptions & EnsureIsOptional): number | null;
declare function ensureSafeInteger(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<number>): number;

export default ensureSafeInteger;
