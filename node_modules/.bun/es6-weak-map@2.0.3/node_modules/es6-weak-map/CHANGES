v2.0.2  --  2017.03.15
* Update dependencies

v2.0.1  --  2015.10.02
* Update to use es6-symbol at v3

v2.0.0  --  2015.09.04
* Relax native implementation detection, stringification of instance should returm
  expected result (not necesarily prototype)

v1.0.2  --  2015.05.07
* Add "ponyfill" keyword to meta description. Fixes #7

v1.0.1  --  2015.04.14
* Fix isNativeImplemented, so it's not affected by #3619 V8 bug
* Fix internal prototype resolution, in case where isNativeImplemented was true, and
  native implementation was shadowed it got into stack overflow

v1.0.0  --  2015.04.13
* It's v0.1.3 republished as v1.0.0

v0.1.4  --  2015.04.13
* Republish v0.1.2 as v0.1.4 due to breaking changes
  (v0.1.3 should have been published as next major)

v0.1.3  --  2015.04.12
* Update up to changes in specification (require new, remove clear method)
* Improve native implementation validation
* Configure lint scripts
* Rename LICENCE to LICENSE

v0.1.2  --  2014.09.01
* Use internal random and unique id generator instead of external (time-uuid based).
  Global uniqueness is not needed in scope of this module. Fixes #1

v0.1.1  --  2014.05.15
* Improve valid WeakMap detection

v0.1.0  --  2014.04.29
* Assure to depend only npm hosted dependencies
* Update to use latest versions of dependencies
* Use ES6 symbols internally

v0.0.0  --  2013.10.24
Initial (dev version)
