{"version": 3, "sources": ["../../../../node_modules/.bun/vue-router@4.6.3+c930ee13906f8ce0/node_modules/vue-router/dist/devtools-BLCumUwL.mjs", "../../../../node_modules/.bun/vue-router@4.6.3+c930ee13906f8ce0/node_modules/vue-router/dist/vue-router.mjs"], "sourcesContent": ["/*!\n * vue-router v4.6.3\n * (c) 2025 <PERSON>\n * @license MIT\n */\nimport { getCurrentInstance, inject, onActivated, onDeactivated, onUnmounted, watch } from \"vue\";\nimport { setupDevtoolsPlugin } from \"@vue/devtools-api\";\n\n//#region src/utils/env.ts\nconst isBrowser = typeof document !== \"undefined\";\n\n//#endregion\n//#region src/utils/index.ts\n/**\n* Identity function that returns the value as is.\n*\n* @param v - the value to return\n*\n* @internal\n*/\nconst identityFn = (v) => v;\n/**\n* Allows differentiating lazy components from functional components and vue-class-component\n* @internal\n*\n* @param component\n*/\nfunction isRouteComponent(component) {\n\treturn typeof component === \"object\" || \"displayName\" in component || \"props\" in component || \"__vccOpts\" in component;\n}\nfunction isESModule(obj) {\n\treturn obj.__esModule || obj[Symbol.toStringTag] === \"Module\" || obj.default && isRouteComponent(obj.default);\n}\nconst assign = Object.assign;\nfunction applyToParams(fn, params) {\n\tconst newParams = {};\n\tfor (const key in params) {\n\t\tconst value = params[key];\n\t\tnewParams[key] = isArray(value) ? value.map(fn) : fn(value);\n\t}\n\treturn newParams;\n}\nconst noop = () => {};\n/**\n* Typesafe alternative to Array.isArray\n* https://github.com/microsoft/TypeScript/pull/48228\n*/\nconst isArray = Array.isArray;\nfunction mergeOptions(defaults, partialOptions) {\n\tconst options = {};\n\tfor (const key in defaults) options[key] = key in partialOptions ? partialOptions[key] : defaults[key];\n\treturn options;\n}\n\n//#endregion\n//#region src/warning.ts\nfunction warn$1(msg) {\n\tconst args = Array.from(arguments).slice(1);\n\tconsole.warn.apply(console, [\"[Vue Router warn]: \" + msg].concat(args));\n}\n\n//#endregion\n//#region src/encoding.ts\n/**\n* Encoding Rules (␣ = Space)\n* - Path: ␣ \" < > # ? { }\n* - Query: ␣ \" < > # & =\n* - Hash: ␣ \" < > `\n*\n* On top of that, the RFC3986 (https://tools.ietf.org/html/rfc3986#section-2.2)\n* defines some extra characters to be encoded. Most browsers do not encode them\n* in encodeURI https://github.com/whatwg/url/issues/369, so it may be safer to\n* also encode `!'()*`. Leaving un-encoded only ASCII alphanumeric(`a-zA-Z0-9`)\n* plus `-._~`. This extra safety should be applied to query by patching the\n* string returned by encodeURIComponent encodeURI also encodes `[\\]^`. `\\`\n* should be encoded to avoid ambiguity. Browsers (IE, FF, C) transform a `\\`\n* into a `/` if directly typed in. The _backtick_ (`````) should also be\n* encoded everywhere because some browsers like FF encode it when directly\n* written while others don't. Safari and IE don't encode ``\"<>{}``` in hash.\n*/\nconst HASH_RE = /#/g;\nconst AMPERSAND_RE = /&/g;\nconst SLASH_RE = /\\//g;\nconst EQUAL_RE = /=/g;\nconst IM_RE = /\\?/g;\nconst PLUS_RE = /\\+/g;\n/**\n* NOTE: It's not clear to me if we should encode the + symbol in queries, it\n* seems to be less flexible than not doing so and I can't find out the legacy\n* systems requiring this for regular requests like text/html. In the standard,\n* the encoding of the plus character is only mentioned for\n* application/x-www-form-urlencoded\n* (https://url.spec.whatwg.org/#urlencoded-parsing) and most browsers seems lo\n* leave the plus character as is in queries. To be more flexible, we allow the\n* plus character on the query, but it can also be manually encoded by the user.\n*\n* Resources:\n* - https://url.spec.whatwg.org/#urlencoded-parsing\n* - https://stackoverflow.com/questions/1634271/url-encoding-the-space-character-or-20\n*/\nconst ENC_BRACKET_OPEN_RE = /%5B/g;\nconst ENC_BRACKET_CLOSE_RE = /%5D/g;\nconst ENC_CARET_RE = /%5E/g;\nconst ENC_BACKTICK_RE = /%60/g;\nconst ENC_CURLY_OPEN_RE = /%7B/g;\nconst ENC_PIPE_RE = /%7C/g;\nconst ENC_CURLY_CLOSE_RE = /%7D/g;\nconst ENC_SPACE_RE = /%20/g;\n/**\n* Encode characters that need to be encoded on the path, search and hash\n* sections of the URL.\n*\n* @internal\n* @param text - string to encode\n* @returns encoded string\n*/\nfunction commonEncode(text) {\n\treturn text == null ? \"\" : encodeURI(\"\" + text).replace(ENC_PIPE_RE, \"|\").replace(ENC_BRACKET_OPEN_RE, \"[\").replace(ENC_BRACKET_CLOSE_RE, \"]\");\n}\n/**\n* Encode characters that need to be encoded on the hash section of the URL.\n*\n* @param text - string to encode\n* @returns encoded string\n*/\nfunction encodeHash(text) {\n\treturn commonEncode(text).replace(ENC_CURLY_OPEN_RE, \"{\").replace(ENC_CURLY_CLOSE_RE, \"}\").replace(ENC_CARET_RE, \"^\");\n}\n/**\n* Encode characters that need to be encoded query values on the query\n* section of the URL.\n*\n* @param text - string to encode\n* @returns encoded string\n*/\nfunction encodeQueryValue(text) {\n\treturn commonEncode(text).replace(PLUS_RE, \"%2B\").replace(ENC_SPACE_RE, \"+\").replace(HASH_RE, \"%23\").replace(AMPERSAND_RE, \"%26\").replace(ENC_BACKTICK_RE, \"`\").replace(ENC_CURLY_OPEN_RE, \"{\").replace(ENC_CURLY_CLOSE_RE, \"}\").replace(ENC_CARET_RE, \"^\");\n}\n/**\n* Like `encodeQueryValue` but also encodes the `=` character.\n*\n* @param text - string to encode\n*/\nfunction encodeQueryKey(text) {\n\treturn encodeQueryValue(text).replace(EQUAL_RE, \"%3D\");\n}\n/**\n* Encode characters that need to be encoded on the path section of the URL.\n*\n* @param text - string to encode\n* @returns encoded string\n*/\nfunction encodePath(text) {\n\treturn commonEncode(text).replace(HASH_RE, \"%23\").replace(IM_RE, \"%3F\");\n}\n/**\n* Encode characters that need to be encoded on the path section of the URL as a\n* param. This function encodes everything {@link encodePath} does plus the\n* slash (`/`) character. If `text` is `null` or `undefined`, returns an empty\n* string instead.\n*\n* @param text - string to encode\n* @returns encoded string\n*/\nfunction encodeParam(text) {\n\treturn encodePath(text).replace(SLASH_RE, \"%2F\");\n}\nfunction decode(text) {\n\tif (text == null) return null;\n\ttry {\n\t\treturn decodeURIComponent(\"\" + text);\n\t} catch (err) {\n\t\tprocess.env.NODE_ENV !== \"production\" && warn$1(`Error decoding \"${text}\". Using original value`);\n\t}\n\treturn \"\" + text;\n}\n\n//#endregion\n//#region src/location.ts\nconst TRAILING_SLASH_RE = /\\/$/;\nconst removeTrailingSlash = (path) => path.replace(TRAILING_SLASH_RE, \"\");\n/**\n* Transforms a URI into a normalized history location\n*\n* @param parseQuery\n* @param location - URI to normalize\n* @param currentLocation - current absolute location. Allows resolving relative\n* paths. Must start with `/`. Defaults to `/`\n* @returns a normalized history location\n*/\nfunction parseURL(parseQuery$1, location, currentLocation = \"/\") {\n\tlet path, query = {}, searchString = \"\", hash = \"\";\n\tconst hashPos = location.indexOf(\"#\");\n\tlet searchPos = location.indexOf(\"?\");\n\tsearchPos = hashPos >= 0 && searchPos > hashPos ? -1 : searchPos;\n\tif (searchPos >= 0) {\n\t\tpath = location.slice(0, searchPos);\n\t\tsearchString = location.slice(searchPos, hashPos > 0 ? hashPos : location.length);\n\t\tquery = parseQuery$1(searchString.slice(1));\n\t}\n\tif (hashPos >= 0) {\n\t\tpath = path || location.slice(0, hashPos);\n\t\thash = location.slice(hashPos, location.length);\n\t}\n\tpath = resolveRelativePath(path != null ? path : location, currentLocation);\n\treturn {\n\t\tfullPath: path + searchString + hash,\n\t\tpath,\n\t\tquery,\n\t\thash: decode(hash)\n\t};\n}\nfunction NEW_stringifyURL(stringifyQuery$1, path, query, hash = \"\") {\n\tconst searchText = stringifyQuery$1(query);\n\treturn path + (searchText && \"?\") + searchText + encodeHash(hash);\n}\n/**\n* Stringifies a URL object\n*\n* @param stringifyQuery\n* @param location\n*/\nfunction stringifyURL(stringifyQuery$1, location) {\n\tconst query = location.query ? stringifyQuery$1(location.query) : \"\";\n\treturn location.path + (query && \"?\") + query + (location.hash || \"\");\n}\n/**\n* Strips off the base from the beginning of a location.pathname in a non-case-sensitive way.\n*\n* @param pathname - location.pathname\n* @param base - base to strip off\n*/\nfunction stripBase(pathname, base) {\n\tif (!base || !pathname.toLowerCase().startsWith(base.toLowerCase())) return pathname;\n\treturn pathname.slice(base.length) || \"/\";\n}\n/**\n* Checks if two RouteLocation are equal. This means that both locations are\n* pointing towards the same {@link RouteRecord} and that all `params`, `query`\n* parameters and `hash` are the same\n*\n* @param stringifyQuery - A function that takes a query object of type LocationQueryRaw and returns a string representation of it.\n* @param a - first {@link RouteLocation}\n* @param b - second {@link RouteLocation}\n*/\nfunction isSameRouteLocation(stringifyQuery$1, a, b) {\n\tconst aLastIndex = a.matched.length - 1;\n\tconst bLastIndex = b.matched.length - 1;\n\treturn aLastIndex > -1 && aLastIndex === bLastIndex && isSameRouteRecord(a.matched[aLastIndex], b.matched[bLastIndex]) && isSameRouteLocationParams(a.params, b.params) && stringifyQuery$1(a.query) === stringifyQuery$1(b.query) && a.hash === b.hash;\n}\n/**\n* Check if two `RouteRecords` are equal. Takes into account aliases: they are\n* considered equal to the `RouteRecord` they are aliasing.\n*\n* @param a - first {@link RouteRecord}\n* @param b - second {@link RouteRecord}\n*/\nfunction isSameRouteRecord(a, b) {\n\treturn (a.aliasOf || a) === (b.aliasOf || b);\n}\nfunction isSameRouteLocationParams(a, b) {\n\tif (Object.keys(a).length !== Object.keys(b).length) return false;\n\tfor (const key in a) if (!isSameRouteLocationParamsValue(a[key], b[key])) return false;\n\treturn true;\n}\nfunction isSameRouteLocationParamsValue(a, b) {\n\treturn isArray(a) ? isEquivalentArray(a, b) : isArray(b) ? isEquivalentArray(b, a) : a === b;\n}\n/**\n* Check if two arrays are the same or if an array with one single entry is the\n* same as another primitive value. Used to check query and parameters\n*\n* @param a - array of values\n* @param b - array of values or a single value\n*/\nfunction isEquivalentArray(a, b) {\n\treturn isArray(b) ? a.length === b.length && a.every((value, i) => value === b[i]) : a.length === 1 && a[0] === b;\n}\n/**\n* Resolves a relative path that starts with `.`.\n*\n* @param to - path location we are resolving\n* @param from - currentLocation.path, should start with `/`\n*/\nfunction resolveRelativePath(to, from) {\n\tif (to.startsWith(\"/\")) return to;\n\tif (process.env.NODE_ENV !== \"production\" && !from.startsWith(\"/\")) {\n\t\twarn$1(`Cannot resolve a relative location without an absolute path. Trying to resolve \"${to}\" from \"${from}\". It should look like \"/${from}\".`);\n\t\treturn to;\n\t}\n\tif (!to) return from;\n\tconst fromSegments = from.split(\"/\");\n\tconst toSegments = to.split(\"/\");\n\tconst lastToSegment = toSegments[toSegments.length - 1];\n\tif (lastToSegment === \"..\" || lastToSegment === \".\") toSegments.push(\"\");\n\tlet position = fromSegments.length - 1;\n\tlet toPosition;\n\tlet segment;\n\tfor (toPosition = 0; toPosition < toSegments.length; toPosition++) {\n\t\tsegment = toSegments[toPosition];\n\t\tif (segment === \".\") continue;\n\t\tif (segment === \"..\") {\n\t\t\tif (position > 1) position--;\n\t\t} else break;\n\t}\n\treturn fromSegments.slice(0, position).join(\"/\") + \"/\" + toSegments.slice(toPosition).join(\"/\");\n}\n/**\n* Initial route location where the router is. Can be used in navigation guards\n* to differentiate the initial navigation.\n*\n* @example\n* ```js\n* import { START_LOCATION } from 'vue-router'\n*\n* router.beforeEach((to, from) => {\n*   if (from === START_LOCATION) {\n*     // initial navigation\n*   }\n* })\n* ```\n*/\nconst START_LOCATION_NORMALIZED = {\n\tpath: \"/\",\n\tname: void 0,\n\tparams: {},\n\tquery: {},\n\thash: \"\",\n\tfullPath: \"/\",\n\tmatched: [],\n\tmeta: {},\n\tredirectedFrom: void 0\n};\n\n//#endregion\n//#region src/history/common.ts\nlet NavigationType = /* @__PURE__ */ function(NavigationType$1) {\n\tNavigationType$1[\"pop\"] = \"pop\";\n\tNavigationType$1[\"push\"] = \"push\";\n\treturn NavigationType$1;\n}({});\nlet NavigationDirection = /* @__PURE__ */ function(NavigationDirection$1) {\n\tNavigationDirection$1[\"back\"] = \"back\";\n\tNavigationDirection$1[\"forward\"] = \"forward\";\n\tNavigationDirection$1[\"unknown\"] = \"\";\n\treturn NavigationDirection$1;\n}({});\n/**\n* Starting location for Histories\n*/\nconst START = \"\";\n/**\n* Normalizes a base by removing any trailing slash and reading the base tag if\n* present.\n*\n* @param base - base to normalize\n*/\nfunction normalizeBase(base) {\n\tif (!base) if (isBrowser) {\n\t\tconst baseEl = document.querySelector(\"base\");\n\t\tbase = baseEl && baseEl.getAttribute(\"href\") || \"/\";\n\t\tbase = base.replace(/^\\w+:\\/\\/[^\\/]+/, \"\");\n\t} else base = \"/\";\n\tif (base[0] !== \"/\" && base[0] !== \"#\") base = \"/\" + base;\n\treturn removeTrailingSlash(base);\n}\nconst BEFORE_HASH_RE = /^[^#]+#/;\nfunction createHref(base, location) {\n\treturn base.replace(BEFORE_HASH_RE, \"#\") + location;\n}\n\n//#endregion\n//#region src/scrollBehavior.ts\nfunction getElementPosition(el, offset) {\n\tconst docRect = document.documentElement.getBoundingClientRect();\n\tconst elRect = el.getBoundingClientRect();\n\treturn {\n\t\tbehavior: offset.behavior,\n\t\tleft: elRect.left - docRect.left - (offset.left || 0),\n\t\ttop: elRect.top - docRect.top - (offset.top || 0)\n\t};\n}\nconst computeScrollPosition = () => ({\n\tleft: window.scrollX,\n\ttop: window.scrollY\n});\nfunction scrollToPosition(position) {\n\tlet scrollToOptions;\n\tif (\"el\" in position) {\n\t\tconst positionEl = position.el;\n\t\tconst isIdSelector = typeof positionEl === \"string\" && positionEl.startsWith(\"#\");\n\t\t/**\n\t\t* `id`s can accept pretty much any characters, including CSS combinators\n\t\t* like `>` or `~`. It's still possible to retrieve elements using\n\t\t* `document.getElementById('~')` but it needs to be escaped when using\n\t\t* `document.querySelector('#\\\\~')` for it to be valid. The only\n\t\t* requirements for `id`s are them to be unique on the page and to not be\n\t\t* empty (`id=\"\"`). Because of that, when passing an id selector, it should\n\t\t* be properly escaped for it to work with `querySelector`. We could check\n\t\t* for the id selector to be simple (no CSS combinators `+ >~`) but that\n\t\t* would make things inconsistent since they are valid characters for an\n\t\t* `id` but would need to be escaped when using `querySelector`, breaking\n\t\t* their usage and ending up in no selector returned. Selectors need to be\n\t\t* escaped:\n\t\t*\n\t\t* - `#1-thing` becomes `#\\31 -thing`\n\t\t* - `#with~symbols` becomes `#with\\\\~symbols`\n\t\t*\n\t\t* - More information about  the topic can be found at\n\t\t*   https://mathiasbynens.be/notes/html5-id-class.\n\t\t* - Practical example: https://mathiasbynens.be/demo/html5-id\n\t\t*/\n\t\tif (process.env.NODE_ENV !== \"production\" && typeof position.el === \"string\") {\n\t\t\tif (!isIdSelector || !document.getElementById(position.el.slice(1))) try {\n\t\t\t\tconst foundEl = document.querySelector(position.el);\n\t\t\t\tif (isIdSelector && foundEl) {\n\t\t\t\t\twarn$1(`The selector \"${position.el}\" should be passed as \"el: document.querySelector('${position.el}')\" because it starts with \"#\".`);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\twarn$1(`The selector \"${position.el}\" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tconst el = typeof positionEl === \"string\" ? isIdSelector ? document.getElementById(positionEl.slice(1)) : document.querySelector(positionEl) : positionEl;\n\t\tif (!el) {\n\t\t\tprocess.env.NODE_ENV !== \"production\" && warn$1(`Couldn't find element using selector \"${position.el}\" returned by scrollBehavior.`);\n\t\t\treturn;\n\t\t}\n\t\tscrollToOptions = getElementPosition(el, position);\n\t} else scrollToOptions = position;\n\tif (\"scrollBehavior\" in document.documentElement.style) window.scrollTo(scrollToOptions);\n\telse window.scrollTo(scrollToOptions.left != null ? scrollToOptions.left : window.scrollX, scrollToOptions.top != null ? scrollToOptions.top : window.scrollY);\n}\nfunction getScrollKey(path, delta) {\n\treturn (history.state ? history.state.position - delta : -1) + path;\n}\nconst scrollPositions = /* @__PURE__ */ new Map();\nfunction saveScrollPosition(key, scrollPosition) {\n\tscrollPositions.set(key, scrollPosition);\n}\nfunction getSavedScrollPosition(key) {\n\tconst scroll = scrollPositions.get(key);\n\tscrollPositions.delete(key);\n\treturn scroll;\n}\n/**\n* ScrollBehavior instance used by the router to compute and restore the scroll\n* position when navigating.\n*/\n\n//#endregion\n//#region src/types/typeGuards.ts\nfunction isRouteLocation(route) {\n\treturn typeof route === \"string\" || route && typeof route === \"object\";\n}\nfunction isRouteName(name) {\n\treturn typeof name === \"string\" || typeof name === \"symbol\";\n}\n\n//#endregion\n//#region src/errors.ts\n/**\n* Flags so we can combine them when checking for multiple errors. This is the internal version of\n* {@link NavigationFailureType}.\n*\n* @internal\n*/\nlet ErrorTypes = /* @__PURE__ */ function(ErrorTypes$1) {\n\tErrorTypes$1[ErrorTypes$1[\"MATCHER_NOT_FOUND\"] = 1] = \"MATCHER_NOT_FOUND\";\n\tErrorTypes$1[ErrorTypes$1[\"NAVIGATION_GUARD_REDIRECT\"] = 2] = \"NAVIGATION_GUARD_REDIRECT\";\n\tErrorTypes$1[ErrorTypes$1[\"NAVIGATION_ABORTED\"] = 4] = \"NAVIGATION_ABORTED\";\n\tErrorTypes$1[ErrorTypes$1[\"NAVIGATION_CANCELLED\"] = 8] = \"NAVIGATION_CANCELLED\";\n\tErrorTypes$1[ErrorTypes$1[\"NAVIGATION_DUPLICATED\"] = 16] = \"NAVIGATION_DUPLICATED\";\n\treturn ErrorTypes$1;\n}({});\nconst NavigationFailureSymbol = Symbol(process.env.NODE_ENV !== \"production\" ? \"navigation failure\" : \"\");\n/**\n* Enumeration with all possible types for navigation failures. Can be passed to\n* {@link isNavigationFailure} to check for specific failures.\n*/\nlet NavigationFailureType = /* @__PURE__ */ function(NavigationFailureType$1) {\n\t/**\n\t* An aborted navigation is a navigation that failed because a navigation\n\t* guard returned `false` or called `next(false)`\n\t*/\n\tNavigationFailureType$1[NavigationFailureType$1[\"aborted\"] = 4] = \"aborted\";\n\t/**\n\t* A cancelled navigation is a navigation that failed because a more recent\n\t* navigation finished started (not necessarily finished).\n\t*/\n\tNavigationFailureType$1[NavigationFailureType$1[\"cancelled\"] = 8] = \"cancelled\";\n\t/**\n\t* A duplicated navigation is a navigation that failed because it was\n\t* initiated while already being at the exact same location.\n\t*/\n\tNavigationFailureType$1[NavigationFailureType$1[\"duplicated\"] = 16] = \"duplicated\";\n\treturn NavigationFailureType$1;\n}({});\nconst ErrorTypeMessages = {\n\t[ErrorTypes.MATCHER_NOT_FOUND]({ location, currentLocation }) {\n\t\treturn `No match for\\n ${JSON.stringify(location)}${currentLocation ? \"\\nwhile being at\\n\" + JSON.stringify(currentLocation) : \"\"}`;\n\t},\n\t[ErrorTypes.NAVIGATION_GUARD_REDIRECT]({ from, to }) {\n\t\treturn `Redirected from \"${from.fullPath}\" to \"${stringifyRoute(to)}\" via a navigation guard.`;\n\t},\n\t[ErrorTypes.NAVIGATION_ABORTED]({ from, to }) {\n\t\treturn `Navigation aborted from \"${from.fullPath}\" to \"${to.fullPath}\" via a navigation guard.`;\n\t},\n\t[ErrorTypes.NAVIGATION_CANCELLED]({ from, to }) {\n\t\treturn `Navigation cancelled from \"${from.fullPath}\" to \"${to.fullPath}\" with a new navigation.`;\n\t},\n\t[ErrorTypes.NAVIGATION_DUPLICATED]({ from, to }) {\n\t\treturn `Avoided redundant navigation to current location: \"${from.fullPath}\".`;\n\t}\n};\n/**\n* Creates a typed NavigationFailure object.\n* @internal\n* @param type - NavigationFailureType\n* @param params - { from, to }\n*/\nfunction createRouterError(type, params) {\n\tif (process.env.NODE_ENV !== \"production\" || false) return assign(new Error(ErrorTypeMessages[type](params)), {\n\t\ttype,\n\t\t[NavigationFailureSymbol]: true\n\t}, params);\n\telse return assign(/* @__PURE__ */ new Error(), {\n\t\ttype,\n\t\t[NavigationFailureSymbol]: true\n\t}, params);\n}\nfunction isNavigationFailure(error, type) {\n\treturn error instanceof Error && NavigationFailureSymbol in error && (type == null || !!(error.type & type));\n}\nconst propertiesToLog = [\n\t\"params\",\n\t\"query\",\n\t\"hash\"\n];\nfunction stringifyRoute(to) {\n\tif (typeof to === \"string\") return to;\n\tif (to.path != null) return to.path;\n\tconst location = {};\n\tfor (const key of propertiesToLog) if (key in to) location[key] = to[key];\n\treturn JSON.stringify(location, null, 2);\n}\n\n//#endregion\n//#region src/query.ts\n/**\n* Transforms a queryString into a {@link LocationQuery} object. Accept both, a\n* version with the leading `?` and without Should work as URLSearchParams\n\n* @internal\n*\n* @param search - search string to parse\n* @returns a query object\n*/\nfunction parseQuery(search) {\n\tconst query = {};\n\tif (search === \"\" || search === \"?\") return query;\n\tconst searchParams = (search[0] === \"?\" ? search.slice(1) : search).split(\"&\");\n\tfor (let i = 0; i < searchParams.length; ++i) {\n\t\tconst searchParam = searchParams[i].replace(PLUS_RE, \" \");\n\t\tconst eqPos = searchParam.indexOf(\"=\");\n\t\tconst key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));\n\t\tconst value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));\n\t\tif (key in query) {\n\t\t\tlet currentValue = query[key];\n\t\t\tif (!isArray(currentValue)) currentValue = query[key] = [currentValue];\n\t\t\tcurrentValue.push(value);\n\t\t} else query[key] = value;\n\t}\n\treturn query;\n}\n/**\n* Stringifies a {@link LocationQueryRaw} object. Like `URLSearchParams`, it\n* doesn't prepend a `?`\n*\n* @internal\n*\n* @param query - query object to stringify\n* @returns string version of the query without the leading `?`\n*/\nfunction stringifyQuery(query) {\n\tlet search = \"\";\n\tfor (let key in query) {\n\t\tconst value = query[key];\n\t\tkey = encodeQueryKey(key);\n\t\tif (value == null) {\n\t\t\tif (value !== void 0) search += (search.length ? \"&\" : \"\") + key;\n\t\t\tcontinue;\n\t\t}\n\t\t(isArray(value) ? value.map((v) => v && encodeQueryValue(v)) : [value && encodeQueryValue(value)]).forEach((value$1) => {\n\t\t\tif (value$1 !== void 0) {\n\t\t\t\tsearch += (search.length ? \"&\" : \"\") + key;\n\t\t\t\tif (value$1 != null) search += \"=\" + value$1;\n\t\t\t}\n\t\t});\n\t}\n\treturn search;\n}\n/**\n* Transforms a {@link LocationQueryRaw} into a {@link LocationQuery} by casting\n* numbers into strings, removing keys with an undefined value and replacing\n* undefined with null in arrays\n*\n* @param query - query object to normalize\n* @returns a normalized query object\n*/\nfunction normalizeQuery(query) {\n\tconst normalizedQuery = {};\n\tfor (const key in query) {\n\t\tconst value = query[key];\n\t\tif (value !== void 0) normalizedQuery[key] = isArray(value) ? value.map((v) => v == null ? null : \"\" + v) : value == null ? value : \"\" + value;\n\t}\n\treturn normalizedQuery;\n}\n\n//#endregion\n//#region src/injectionSymbols.ts\n/**\n* RouteRecord being rendered by the closest ancestor Router View. Used for\n* `onBeforeRouteUpdate` and `onBeforeRouteLeave`. rvlm stands for Router View\n* Location Matched\n*\n* @internal\n*/\nconst matchedRouteKey = Symbol(process.env.NODE_ENV !== \"production\" ? \"router view location matched\" : \"\");\n/**\n* Allows overriding the router view depth to control which component in\n* `matched` is rendered. rvd stands for Router View Depth\n*\n* @internal\n*/\nconst viewDepthKey = Symbol(process.env.NODE_ENV !== \"production\" ? \"router view depth\" : \"\");\n/**\n* Allows overriding the router instance returned by `useRouter` in tests. r\n* stands for router\n*\n* @internal\n*/\nconst routerKey = Symbol(process.env.NODE_ENV !== \"production\" ? \"router\" : \"\");\n/**\n* Allows overriding the current route returned by `useRoute` in tests. rl\n* stands for route location\n*\n* @internal\n*/\nconst routeLocationKey = Symbol(process.env.NODE_ENV !== \"production\" ? \"route location\" : \"\");\n/**\n* Allows overriding the current route used by router-view. Internally this is\n* used when the `route` prop is passed.\n*\n* @internal\n*/\nconst routerViewLocationKey = Symbol(process.env.NODE_ENV !== \"production\" ? \"router view location\" : \"\");\n\n//#endregion\n//#region src/utils/callbacks.ts\n/**\n* Create a list of callbacks that can be reset. Used to create before and after navigation guards list\n*/\nfunction useCallbacks() {\n\tlet handlers = [];\n\tfunction add(handler) {\n\t\thandlers.push(handler);\n\t\treturn () => {\n\t\t\tconst i = handlers.indexOf(handler);\n\t\t\tif (i > -1) handlers.splice(i, 1);\n\t\t};\n\t}\n\tfunction reset() {\n\t\thandlers = [];\n\t}\n\treturn {\n\t\tadd,\n\t\tlist: () => handlers.slice(),\n\t\treset\n\t};\n}\n\n//#endregion\n//#region src/navigationGuards.ts\nfunction registerGuard(record, name, guard) {\n\tconst removeFromList = () => {\n\t\trecord[name].delete(guard);\n\t};\n\tonUnmounted(removeFromList);\n\tonDeactivated(removeFromList);\n\tonActivated(() => {\n\t\trecord[name].add(guard);\n\t});\n\trecord[name].add(guard);\n}\n/**\n* Add a navigation guard that triggers whenever the component for the current\n* location is about to be left. Similar to {@link beforeRouteLeave} but can be\n* used in any component. The guard is removed when the component is unmounted.\n*\n* @param leaveGuard - {@link NavigationGuard}\n*/\nfunction onBeforeRouteLeave(leaveGuard) {\n\tif (process.env.NODE_ENV !== \"production\" && !getCurrentInstance()) {\n\t\twarn$1(\"getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function\");\n\t\treturn;\n\t}\n\tconst activeRecord = inject(matchedRouteKey, {}).value;\n\tif (!activeRecord) {\n\t\tprocess.env.NODE_ENV !== \"production\" && warn$1(\"No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?\");\n\t\treturn;\n\t}\n\tregisterGuard(activeRecord, \"leaveGuards\", leaveGuard);\n}\n/**\n* Add a navigation guard that triggers whenever the current location is about\n* to be updated. Similar to {@link beforeRouteUpdate} but can be used in any\n* component. The guard is removed when the component is unmounted.\n*\n* @param updateGuard - {@link NavigationGuard}\n*/\nfunction onBeforeRouteUpdate(updateGuard) {\n\tif (process.env.NODE_ENV !== \"production\" && !getCurrentInstance()) {\n\t\twarn$1(\"getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function\");\n\t\treturn;\n\t}\n\tconst activeRecord = inject(matchedRouteKey, {}).value;\n\tif (!activeRecord) {\n\t\tprocess.env.NODE_ENV !== \"production\" && warn$1(\"No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?\");\n\t\treturn;\n\t}\n\tregisterGuard(activeRecord, \"updateGuards\", updateGuard);\n}\nfunction guardToPromiseFn(guard, to, from, record, name, runWithContext = (fn) => fn()) {\n\tconst enterCallbackArray = record && (record.enterCallbacks[name] = record.enterCallbacks[name] || []);\n\treturn () => new Promise((resolve, reject) => {\n\t\tconst next = (valid) => {\n\t\t\tif (valid === false) reject(createRouterError(ErrorTypes.NAVIGATION_ABORTED, {\n\t\t\t\tfrom,\n\t\t\t\tto\n\t\t\t}));\n\t\t\telse if (valid instanceof Error) reject(valid);\n\t\t\telse if (isRouteLocation(valid)) reject(createRouterError(ErrorTypes.NAVIGATION_GUARD_REDIRECT, {\n\t\t\t\tfrom: to,\n\t\t\t\tto: valid\n\t\t\t}));\n\t\t\telse {\n\t\t\t\tif (enterCallbackArray && record.enterCallbacks[name] === enterCallbackArray && typeof valid === \"function\") enterCallbackArray.push(valid);\n\t\t\t\tresolve();\n\t\t\t}\n\t\t};\n\t\tconst guardReturn = runWithContext(() => guard.call(record && record.instances[name], to, from, process.env.NODE_ENV !== \"production\" ? canOnlyBeCalledOnce(next, to, from) : next));\n\t\tlet guardCall = Promise.resolve(guardReturn);\n\t\tif (guard.length < 3) guardCall = guardCall.then(next);\n\t\tif (process.env.NODE_ENV !== \"production\" && guard.length > 2) {\n\t\t\tconst message = `The \"next\" callback was never called inside of ${guard.name ? \"\\\"\" + guard.name + \"\\\"\" : \"\"}:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`;\n\t\t\tif (typeof guardReturn === \"object\" && \"then\" in guardReturn) guardCall = guardCall.then((resolvedValue) => {\n\t\t\t\tif (!next._called) {\n\t\t\t\t\twarn$1(message);\n\t\t\t\t\treturn Promise.reject(/* @__PURE__ */ new Error(\"Invalid navigation guard\"));\n\t\t\t\t}\n\t\t\t\treturn resolvedValue;\n\t\t\t});\n\t\t\telse if (guardReturn !== void 0) {\n\t\t\t\tif (!next._called) {\n\t\t\t\t\twarn$1(message);\n\t\t\t\t\treject(/* @__PURE__ */ new Error(\"Invalid navigation guard\"));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tguardCall.catch((err) => reject(err));\n\t});\n}\nfunction canOnlyBeCalledOnce(next, to, from) {\n\tlet called = 0;\n\treturn function() {\n\t\tif (called++ === 1) warn$1(`The \"next\" callback was called more than once in one navigation guard when going from \"${from.fullPath}\" to \"${to.fullPath}\". It should be called exactly one time in each navigation guard. This will fail in production.`);\n\t\tnext._called = true;\n\t\tif (called === 1) next.apply(null, arguments);\n\t};\n}\nfunction extractComponentsGuards(matched, guardType, to, from, runWithContext = (fn) => fn()) {\n\tconst guards = [];\n\tfor (const record of matched) {\n\t\tif (process.env.NODE_ENV !== \"production\" && !record.components && record.children && !record.children.length) warn$1(`Record with path \"${record.path}\" is either missing a \"component(s)\" or \"children\" property.`);\n\t\tfor (const name in record.components) {\n\t\t\tlet rawComponent = record.components[name];\n\t\t\tif (process.env.NODE_ENV !== \"production\") {\n\t\t\t\tif (!rawComponent || typeof rawComponent !== \"object\" && typeof rawComponent !== \"function\") {\n\t\t\t\t\twarn$1(`Component \"${name}\" in record with path \"${record.path}\" is not a valid component. Received \"${String(rawComponent)}\".`);\n\t\t\t\t\tthrow new Error(\"Invalid route component\");\n\t\t\t\t} else if (\"then\" in rawComponent) {\n\t\t\t\t\twarn$1(`Component \"${name}\" in record with path \"${record.path}\" is a Promise instead of a function that returns a Promise. Did you write \"import('./MyPage.vue')\" instead of \"() => import('./MyPage.vue')\" ? This will break in production if not fixed.`);\n\t\t\t\t\tconst promise = rawComponent;\n\t\t\t\t\trawComponent = () => promise;\n\t\t\t\t} else if (rawComponent.__asyncLoader && !rawComponent.__warnedDefineAsync) {\n\t\t\t\t\trawComponent.__warnedDefineAsync = true;\n\t\t\t\t\twarn$1(`Component \"${name}\" in record with path \"${record.path}\" is defined using \"defineAsyncComponent()\". Write \"() => import('./MyPage.vue')\" instead of \"defineAsyncComponent(() => import('./MyPage.vue'))\".`);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (guardType !== \"beforeRouteEnter\" && !record.instances[name]) continue;\n\t\t\tif (isRouteComponent(rawComponent)) {\n\t\t\t\tconst guard = (rawComponent.__vccOpts || rawComponent)[guardType];\n\t\t\t\tguard && guards.push(guardToPromiseFn(guard, to, from, record, name, runWithContext));\n\t\t\t} else {\n\t\t\t\tlet componentPromise = rawComponent();\n\t\t\t\tif (process.env.NODE_ENV !== \"production\" && !(\"catch\" in componentPromise)) {\n\t\t\t\t\twarn$1(`Component \"${name}\" in record with path \"${record.path}\" is a function that does not return a Promise. If you were passing a functional component, make sure to add a \"displayName\" to the component. This will break in production if not fixed.`);\n\t\t\t\t\tcomponentPromise = Promise.resolve(componentPromise);\n\t\t\t\t}\n\t\t\t\tguards.push(() => componentPromise.then((resolved) => {\n\t\t\t\t\tif (!resolved) throw new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\"`);\n\t\t\t\t\tconst resolvedComponent = isESModule(resolved) ? resolved.default : resolved;\n\t\t\t\t\trecord.mods[name] = resolved;\n\t\t\t\t\trecord.components[name] = resolvedComponent;\n\t\t\t\t\tconst guard = (resolvedComponent.__vccOpts || resolvedComponent)[guardType];\n\t\t\t\t\treturn guard && guardToPromiseFn(guard, to, from, record, name, runWithContext)();\n\t\t\t\t}));\n\t\t\t}\n\t\t}\n\t}\n\treturn guards;\n}\n/**\n* Ensures a route is loaded, so it can be passed as o prop to `<RouterView>`.\n*\n* @param route - resolved route to load\n*/\nfunction loadRouteLocation(route) {\n\treturn route.matched.every((record) => record.redirect) ? Promise.reject(/* @__PURE__ */ new Error(\"Cannot load a route that redirects.\")) : Promise.all(route.matched.map((record) => record.components && Promise.all(Object.keys(record.components).reduce((promises, name) => {\n\t\tconst rawComponent = record.components[name];\n\t\tif (typeof rawComponent === \"function\" && !(\"displayName\" in rawComponent)) promises.push(rawComponent().then((resolved) => {\n\t\t\tif (!resolved) return Promise.reject(/* @__PURE__ */ new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\". Ensure you passed a function that returns a promise.`));\n\t\t\tconst resolvedComponent = isESModule(resolved) ? resolved.default : resolved;\n\t\t\trecord.mods[name] = resolved;\n\t\t\trecord.components[name] = resolvedComponent;\n\t\t}));\n\t\treturn promises;\n\t}, [])))).then(() => route);\n}\n/**\n* Split the leaving, updating, and entering records.\n* @internal\n*\n* @param  to - Location we are navigating to\n* @param from - Location we are navigating from\n*/\nfunction extractChangingRecords(to, from) {\n\tconst leavingRecords = [];\n\tconst updatingRecords = [];\n\tconst enteringRecords = [];\n\tconst len = Math.max(from.matched.length, to.matched.length);\n\tfor (let i = 0; i < len; i++) {\n\t\tconst recordFrom = from.matched[i];\n\t\tif (recordFrom) if (to.matched.find((record) => isSameRouteRecord(record, recordFrom))) updatingRecords.push(recordFrom);\n\t\telse leavingRecords.push(recordFrom);\n\t\tconst recordTo = to.matched[i];\n\t\tif (recordTo) {\n\t\t\tif (!from.matched.find((record) => isSameRouteRecord(record, recordTo))) enteringRecords.push(recordTo);\n\t\t}\n\t}\n\treturn [\n\t\tleavingRecords,\n\t\tupdatingRecords,\n\t\tenteringRecords\n\t];\n}\n\n//#endregion\n//#region src/devtools.ts\n/**\n* Copies a route location and removes any problematic properties that cannot be shown in devtools (e.g. Vue instances).\n*\n* @param routeLocation - routeLocation to format\n* @param tooltip - optional tooltip\n* @returns a copy of the routeLocation\n*/\nfunction formatRouteLocation(routeLocation, tooltip) {\n\tconst copy = assign({}, routeLocation, { matched: routeLocation.matched.map((matched) => omit(matched, [\n\t\t\"instances\",\n\t\t\"children\",\n\t\t\"aliasOf\"\n\t])) });\n\treturn { _custom: {\n\t\ttype: null,\n\t\treadOnly: true,\n\t\tdisplay: routeLocation.fullPath,\n\t\ttooltip,\n\t\tvalue: copy\n\t} };\n}\nfunction formatDisplay(display) {\n\treturn { _custom: { display } };\n}\nlet routerId = 0;\nfunction addDevtools(app, router, matcher) {\n\tif (router.__hasDevtools) return;\n\trouter.__hasDevtools = true;\n\tconst id = routerId++;\n\tsetupDevtoolsPlugin({\n\t\tid: \"org.vuejs.router\" + (id ? \".\" + id : \"\"),\n\t\tlabel: \"Vue Router\",\n\t\tpackageName: \"vue-router\",\n\t\thomepage: \"https://router.vuejs.org\",\n\t\tlogo: \"https://router.vuejs.org/logo.png\",\n\t\tcomponentStateTypes: [\"Routing\"],\n\t\tapp\n\t}, (api) => {\n\t\tif (typeof api.now !== \"function\") warn$1(\"[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.\");\n\t\tapi.on.inspectComponent((payload, ctx) => {\n\t\t\tif (payload.instanceData) payload.instanceData.state.push({\n\t\t\t\ttype: \"Routing\",\n\t\t\t\tkey: \"$route\",\n\t\t\t\teditable: false,\n\t\t\t\tvalue: formatRouteLocation(router.currentRoute.value, \"Current Route\")\n\t\t\t});\n\t\t});\n\t\tapi.on.visitComponentTree(({ treeNode: node, componentInstance }) => {\n\t\t\tif (componentInstance.__vrv_devtools) {\n\t\t\t\tconst info = componentInstance.__vrv_devtools;\n\t\t\t\tnode.tags.push({\n\t\t\t\t\tlabel: (info.name ? `${info.name.toString()}: ` : \"\") + info.path,\n\t\t\t\t\ttextColor: 0,\n\t\t\t\t\ttooltip: \"This component is rendered by &lt;router-view&gt;\",\n\t\t\t\t\tbackgroundColor: PINK_500\n\t\t\t\t});\n\t\t\t}\n\t\t\tif (isArray(componentInstance.__vrl_devtools)) {\n\t\t\t\tcomponentInstance.__devtoolsApi = api;\n\t\t\t\tcomponentInstance.__vrl_devtools.forEach((devtoolsData) => {\n\t\t\t\t\tlet label = devtoolsData.route.path;\n\t\t\t\t\tlet backgroundColor = ORANGE_400;\n\t\t\t\t\tlet tooltip = \"\";\n\t\t\t\t\tlet textColor = 0;\n\t\t\t\t\tif (devtoolsData.error) {\n\t\t\t\t\t\tlabel = devtoolsData.error;\n\t\t\t\t\t\tbackgroundColor = RED_100;\n\t\t\t\t\t\ttextColor = RED_700;\n\t\t\t\t\t} else if (devtoolsData.isExactActive) {\n\t\t\t\t\t\tbackgroundColor = LIME_500;\n\t\t\t\t\t\ttooltip = \"This is exactly active\";\n\t\t\t\t\t} else if (devtoolsData.isActive) {\n\t\t\t\t\t\tbackgroundColor = BLUE_600;\n\t\t\t\t\t\ttooltip = \"This link is active\";\n\t\t\t\t\t}\n\t\t\t\t\tnode.tags.push({\n\t\t\t\t\t\tlabel,\n\t\t\t\t\t\ttextColor,\n\t\t\t\t\t\ttooltip,\n\t\t\t\t\t\tbackgroundColor\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\twatch(router.currentRoute, () => {\n\t\t\trefreshRoutesView();\n\t\t\tapi.notifyComponentUpdate();\n\t\t\tapi.sendInspectorTree(routerInspectorId);\n\t\t\tapi.sendInspectorState(routerInspectorId);\n\t\t});\n\t\tconst navigationsLayerId = \"router:navigations:\" + id;\n\t\tapi.addTimelineLayer({\n\t\t\tid: navigationsLayerId,\n\t\t\tlabel: `Router${id ? \" \" + id : \"\"} Navigations`,\n\t\t\tcolor: 4237508\n\t\t});\n\t\trouter.onError((error, to) => {\n\t\t\tapi.addTimelineEvent({\n\t\t\t\tlayerId: navigationsLayerId,\n\t\t\t\tevent: {\n\t\t\t\t\ttitle: \"Error during Navigation\",\n\t\t\t\t\tsubtitle: to.fullPath,\n\t\t\t\t\tlogType: \"error\",\n\t\t\t\t\ttime: api.now(),\n\t\t\t\t\tdata: { error },\n\t\t\t\t\tgroupId: to.meta.__navigationId\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t\tlet navigationId = 0;\n\t\trouter.beforeEach((to, from) => {\n\t\t\tconst data = {\n\t\t\t\tguard: formatDisplay(\"beforeEach\"),\n\t\t\t\tfrom: formatRouteLocation(from, \"Current Location during this navigation\"),\n\t\t\t\tto: formatRouteLocation(to, \"Target location\")\n\t\t\t};\n\t\t\tObject.defineProperty(to.meta, \"__navigationId\", { value: navigationId++ });\n\t\t\tapi.addTimelineEvent({\n\t\t\t\tlayerId: navigationsLayerId,\n\t\t\t\tevent: {\n\t\t\t\t\ttime: api.now(),\n\t\t\t\t\ttitle: \"Start of navigation\",\n\t\t\t\t\tsubtitle: to.fullPath,\n\t\t\t\t\tdata,\n\t\t\t\t\tgroupId: to.meta.__navigationId\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t\trouter.afterEach((to, from, failure) => {\n\t\t\tconst data = { guard: formatDisplay(\"afterEach\") };\n\t\t\tif (failure) {\n\t\t\t\tdata.failure = { _custom: {\n\t\t\t\t\ttype: Error,\n\t\t\t\t\treadOnly: true,\n\t\t\t\t\tdisplay: failure ? failure.message : \"\",\n\t\t\t\t\ttooltip: \"Navigation Failure\",\n\t\t\t\t\tvalue: failure\n\t\t\t\t} };\n\t\t\t\tdata.status = formatDisplay(\"❌\");\n\t\t\t} else data.status = formatDisplay(\"✅\");\n\t\t\tdata.from = formatRouteLocation(from, \"Current Location during this navigation\");\n\t\t\tdata.to = formatRouteLocation(to, \"Target location\");\n\t\t\tapi.addTimelineEvent({\n\t\t\t\tlayerId: navigationsLayerId,\n\t\t\t\tevent: {\n\t\t\t\t\ttitle: \"End of navigation\",\n\t\t\t\t\tsubtitle: to.fullPath,\n\t\t\t\t\ttime: api.now(),\n\t\t\t\t\tdata,\n\t\t\t\t\tlogType: failure ? \"warning\" : \"default\",\n\t\t\t\t\tgroupId: to.meta.__navigationId\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t\t/**\n\t\t* Inspector of Existing routes\n\t\t*/\n\t\tconst routerInspectorId = \"router-inspector:\" + id;\n\t\tapi.addInspector({\n\t\t\tid: routerInspectorId,\n\t\t\tlabel: \"Routes\" + (id ? \" \" + id : \"\"),\n\t\t\ticon: \"book\",\n\t\t\ttreeFilterPlaceholder: \"Search routes\"\n\t\t});\n\t\tfunction refreshRoutesView() {\n\t\t\tif (!activeRoutesPayload) return;\n\t\t\tconst payload = activeRoutesPayload;\n\t\t\tlet routes = matcher.getRoutes().filter((route) => !route.parent || !route.parent.record.components);\n\t\t\troutes.forEach(resetMatchStateOnRouteRecord);\n\t\t\tif (payload.filter) routes = routes.filter((route) => isRouteMatching(route, payload.filter.toLowerCase()));\n\t\t\troutes.forEach((route) => markRouteRecordActive(route, router.currentRoute.value));\n\t\t\tpayload.rootNodes = routes.map(formatRouteRecordForInspector);\n\t\t}\n\t\tlet activeRoutesPayload;\n\t\tapi.on.getInspectorTree((payload) => {\n\t\t\tactiveRoutesPayload = payload;\n\t\t\tif (payload.app === app && payload.inspectorId === routerInspectorId) refreshRoutesView();\n\t\t});\n\t\t/**\n\t\t* Display information about the currently selected route record\n\t\t*/\n\t\tapi.on.getInspectorState((payload) => {\n\t\t\tif (payload.app === app && payload.inspectorId === routerInspectorId) {\n\t\t\t\tconst route = matcher.getRoutes().find((route$1) => route$1.record.__vd_id === payload.nodeId);\n\t\t\t\tif (route) payload.state = { options: formatRouteRecordMatcherForStateInspector(route) };\n\t\t\t}\n\t\t});\n\t\tapi.sendInspectorTree(routerInspectorId);\n\t\tapi.sendInspectorState(routerInspectorId);\n\t});\n}\nfunction modifierForKey(key) {\n\tif (key.optional) return key.repeatable ? \"*\" : \"?\";\n\telse return key.repeatable ? \"+\" : \"\";\n}\nfunction formatRouteRecordMatcherForStateInspector(route) {\n\tconst { record } = route;\n\tconst fields = [{\n\t\teditable: false,\n\t\tkey: \"path\",\n\t\tvalue: record.path\n\t}];\n\tif (record.name != null) fields.push({\n\t\teditable: false,\n\t\tkey: \"name\",\n\t\tvalue: record.name\n\t});\n\tfields.push({\n\t\teditable: false,\n\t\tkey: \"regexp\",\n\t\tvalue: route.re\n\t});\n\tif (route.keys.length) fields.push({\n\t\teditable: false,\n\t\tkey: \"keys\",\n\t\tvalue: { _custom: {\n\t\t\ttype: null,\n\t\t\treadOnly: true,\n\t\t\tdisplay: route.keys.map((key) => `${key.name}${modifierForKey(key)}`).join(\" \"),\n\t\t\ttooltip: \"Param keys\",\n\t\t\tvalue: route.keys\n\t\t} }\n\t});\n\tif (record.redirect != null) fields.push({\n\t\teditable: false,\n\t\tkey: \"redirect\",\n\t\tvalue: record.redirect\n\t});\n\tif (route.alias.length) fields.push({\n\t\teditable: false,\n\t\tkey: \"aliases\",\n\t\tvalue: route.alias.map((alias) => alias.record.path)\n\t});\n\tif (Object.keys(route.record.meta).length) fields.push({\n\t\teditable: false,\n\t\tkey: \"meta\",\n\t\tvalue: route.record.meta\n\t});\n\tfields.push({\n\t\tkey: \"score\",\n\t\teditable: false,\n\t\tvalue: { _custom: {\n\t\t\ttype: null,\n\t\t\treadOnly: true,\n\t\t\tdisplay: route.score.map((score) => score.join(\", \")).join(\" | \"),\n\t\t\ttooltip: \"Score used to sort routes\",\n\t\t\tvalue: route.score\n\t\t} }\n\t});\n\treturn fields;\n}\n/**\n* Extracted from tailwind palette\n*/\nconst PINK_500 = 15485081;\nconst BLUE_600 = 2450411;\nconst LIME_500 = 8702998;\nconst CYAN_400 = 2282478;\nconst ORANGE_400 = 16486972;\nconst DARK = 6710886;\nconst RED_100 = 16704226;\nconst RED_700 = 12131356;\nfunction formatRouteRecordForInspector(route) {\n\tconst tags = [];\n\tconst { record } = route;\n\tif (record.name != null) tags.push({\n\t\tlabel: String(record.name),\n\t\ttextColor: 0,\n\t\tbackgroundColor: CYAN_400\n\t});\n\tif (record.aliasOf) tags.push({\n\t\tlabel: \"alias\",\n\t\ttextColor: 0,\n\t\tbackgroundColor: ORANGE_400\n\t});\n\tif (route.__vd_match) tags.push({\n\t\tlabel: \"matches\",\n\t\ttextColor: 0,\n\t\tbackgroundColor: PINK_500\n\t});\n\tif (route.__vd_exactActive) tags.push({\n\t\tlabel: \"exact\",\n\t\ttextColor: 0,\n\t\tbackgroundColor: LIME_500\n\t});\n\tif (route.__vd_active) tags.push({\n\t\tlabel: \"active\",\n\t\ttextColor: 0,\n\t\tbackgroundColor: BLUE_600\n\t});\n\tif (record.redirect) tags.push({\n\t\tlabel: typeof record.redirect === \"string\" ? `redirect: ${record.redirect}` : \"redirects\",\n\t\ttextColor: 16777215,\n\t\tbackgroundColor: DARK\n\t});\n\tlet id = record.__vd_id;\n\tif (id == null) {\n\t\tid = String(routeRecordId++);\n\t\trecord.__vd_id = id;\n\t}\n\treturn {\n\t\tid,\n\t\tlabel: record.path,\n\t\ttags,\n\t\tchildren: route.children.map(formatRouteRecordForInspector)\n\t};\n}\nlet routeRecordId = 0;\nconst EXTRACT_REGEXP_RE = /^\\/(.*)\\/([a-z]*)$/;\nfunction markRouteRecordActive(route, currentRoute) {\n\tconst isExactActive = currentRoute.matched.length && isSameRouteRecord(currentRoute.matched[currentRoute.matched.length - 1], route.record);\n\troute.__vd_exactActive = route.__vd_active = isExactActive;\n\tif (!isExactActive) route.__vd_active = currentRoute.matched.some((match) => isSameRouteRecord(match, route.record));\n\troute.children.forEach((childRoute) => markRouteRecordActive(childRoute, currentRoute));\n}\nfunction resetMatchStateOnRouteRecord(route) {\n\troute.__vd_match = false;\n\troute.children.forEach(resetMatchStateOnRouteRecord);\n}\nfunction isRouteMatching(route, filter) {\n\tconst found = String(route.re).match(EXTRACT_REGEXP_RE);\n\troute.__vd_match = false;\n\tif (!found || found.length < 3) return false;\n\tif (new RegExp(found[1].replace(/\\$$/, \"\"), found[2]).test(filter)) {\n\t\troute.children.forEach((child) => isRouteMatching(child, filter));\n\t\tif (route.record.path !== \"/\" || filter === \"/\") {\n\t\t\troute.__vd_match = route.re.test(filter);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\tconst path = route.record.path.toLowerCase();\n\tconst decodedPath = decode(path);\n\tif (!filter.startsWith(\"/\") && (decodedPath.includes(filter) || path.includes(filter))) return true;\n\tif (decodedPath.startsWith(filter) || path.startsWith(filter)) return true;\n\tif (route.record.name && String(route.record.name).includes(filter)) return true;\n\treturn route.children.some((child) => isRouteMatching(child, filter));\n}\nfunction omit(obj, keys) {\n\tconst ret = {};\n\tfor (const key in obj) if (!keys.includes(key)) ret[key] = obj[key];\n\treturn ret;\n}\n\n//#endregion\nexport { ErrorTypes, NEW_stringifyURL, NavigationDirection, NavigationFailureType, NavigationType, START, START_LOCATION_NORMALIZED, addDevtools, applyToParams, assign, computeScrollPosition, createHref, createRouterError, decode, encodeHash, encodeParam, encodePath, extractChangingRecords, extractComponentsGuards, getSavedScrollPosition, getScrollKey, guardToPromiseFn, identityFn, isArray, isBrowser, isNavigationFailure, isRouteLocation, isRouteName, isSameRouteLocation, isSameRouteLocationParams, isSameRouteRecord, loadRouteLocation, matchedRouteKey, mergeOptions, noop, normalizeBase, normalizeQuery, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, parseURL, resolveRelativePath, routeLocationKey, routerKey, routerViewLocationKey, saveScrollPosition, scrollToPosition, stringifyQuery, stringifyURL, stripBase, useCallbacks, viewDepthKey, warn$1 as warn };", "/*!\n * vue-router v4.6.3\n * (c) 2025 <PERSON>\n * @license MIT\n */\nimport { ErrorTypes, NavigationDirection, NavigationFailureType, NavigationType, START, START_LOCATION_NORMALIZED, addDevtools, applyToParams, assign, computeScrollPosition, createHref, createRouterError, decode, encodeHash, encodeParam, extractChangingRecords, extractComponentsGuards, getSavedScrollPosition, getScrollKey, guardToPromiseFn, isArray, isBrowser, isNavigationFailure, isRouteLocation, isRouteName, isSameRouteLocation, isSameRouteLocationParams, isSameRouteRecord, loadRouteLocation, matchedRouteKey, mergeOptions, noop, normalizeBase, normalizeQuery, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, parseURL, routeLocationKey, router<PERSON><PERSON>, routerViewLocationKey, saveScrollPosition, scrollToPosition, stringifyQuery, stringifyURL, stripBase, useCallbacks, viewDepthKey, warn as warn$1 } from \"./devtools-BLCumUwL.mjs\";\nimport { computed, defineComponent, getCurrentInstance, h, inject, nextTick, provide, reactive, ref, shallowReactive, shallowRef, unref, watch, watchEffect } from \"vue\";\n\n//#region src/history/html5.ts\nlet createBaseLocation = () => location.protocol + \"//\" + location.host;\n/**\n* Creates a normalized history location from a window.location object\n* @param base - The base path\n* @param location - The window.location object\n*/\nfunction createCurrentLocation(base, location$1) {\n\tconst { pathname, search, hash } = location$1;\n\tconst hashPos = base.indexOf(\"#\");\n\tif (hashPos > -1) {\n\t\tlet slicePos = hash.includes(base.slice(hashPos)) ? base.slice(hashPos).length : 1;\n\t\tlet pathFromHash = hash.slice(slicePos);\n\t\tif (pathFromHash[0] !== \"/\") pathFromHash = \"/\" + pathFromHash;\n\t\treturn stripBase(pathFromHash, \"\");\n\t}\n\treturn stripBase(pathname, base) + search + hash;\n}\nfunction useHistoryListeners(base, historyState, currentLocation, replace) {\n\tlet listeners = [];\n\tlet teardowns = [];\n\tlet pauseState = null;\n\tconst popStateHandler = ({ state }) => {\n\t\tconst to = createCurrentLocation(base, location);\n\t\tconst from = currentLocation.value;\n\t\tconst fromState = historyState.value;\n\t\tlet delta = 0;\n\t\tif (state) {\n\t\t\tcurrentLocation.value = to;\n\t\t\thistoryState.value = state;\n\t\t\tif (pauseState && pauseState === from) {\n\t\t\t\tpauseState = null;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tdelta = fromState ? state.position - fromState.position : 0;\n\t\t} else replace(to);\n\t\tlisteners.forEach((listener) => {\n\t\t\tlistener(currentLocation.value, from, {\n\t\t\t\tdelta,\n\t\t\t\ttype: NavigationType.pop,\n\t\t\t\tdirection: delta ? delta > 0 ? NavigationDirection.forward : NavigationDirection.back : NavigationDirection.unknown\n\t\t\t});\n\t\t});\n\t};\n\tfunction pauseListeners() {\n\t\tpauseState = currentLocation.value;\n\t}\n\tfunction listen(callback) {\n\t\tlisteners.push(callback);\n\t\tconst teardown = () => {\n\t\t\tconst index = listeners.indexOf(callback);\n\t\t\tif (index > -1) listeners.splice(index, 1);\n\t\t};\n\t\tteardowns.push(teardown);\n\t\treturn teardown;\n\t}\n\tfunction beforeUnloadListener() {\n\t\tif (document.visibilityState === \"hidden\") {\n\t\t\tconst { history: history$1 } = window;\n\t\t\tif (!history$1.state) return;\n\t\t\thistory$1.replaceState(assign({}, history$1.state, { scroll: computeScrollPosition() }), \"\");\n\t\t}\n\t}\n\tfunction destroy() {\n\t\tfor (const teardown of teardowns) teardown();\n\t\tteardowns = [];\n\t\twindow.removeEventListener(\"popstate\", popStateHandler);\n\t\twindow.removeEventListener(\"pagehide\", beforeUnloadListener);\n\t\tdocument.removeEventListener(\"visibilitychange\", beforeUnloadListener);\n\t}\n\twindow.addEventListener(\"popstate\", popStateHandler);\n\twindow.addEventListener(\"pagehide\", beforeUnloadListener);\n\tdocument.addEventListener(\"visibilitychange\", beforeUnloadListener);\n\treturn {\n\t\tpauseListeners,\n\t\tlisten,\n\t\tdestroy\n\t};\n}\n/**\n* Creates a state object\n*/\nfunction buildState(back, current, forward, replaced = false, computeScroll = false) {\n\treturn {\n\t\tback,\n\t\tcurrent,\n\t\tforward,\n\t\treplaced,\n\t\tposition: window.history.length,\n\t\tscroll: computeScroll ? computeScrollPosition() : null\n\t};\n}\nfunction useHistoryStateNavigation(base) {\n\tconst { history: history$1, location: location$1 } = window;\n\tconst currentLocation = { value: createCurrentLocation(base, location$1) };\n\tconst historyState = { value: history$1.state };\n\tif (!historyState.value) changeLocation(currentLocation.value, {\n\t\tback: null,\n\t\tcurrent: currentLocation.value,\n\t\tforward: null,\n\t\tposition: history$1.length - 1,\n\t\treplaced: true,\n\t\tscroll: null\n\t}, true);\n\tfunction changeLocation(to, state, replace$1) {\n\t\t/**\n\t\t* if a base tag is provided, and we are on a normal domain, we have to\n\t\t* respect the provided `base` attribute because pushState() will use it and\n\t\t* potentially erase anything before the `#` like at\n\t\t* https://github.com/vuejs/router/issues/685 where a base of\n\t\t* `/folder/#` but a base of `/` would erase the `/folder/` section. If\n\t\t* there is no host, the `<base>` tag makes no sense and if there isn't a\n\t\t* base tag we can just use everything after the `#`.\n\t\t*/\n\t\tconst hashIndex = base.indexOf(\"#\");\n\t\tconst url = hashIndex > -1 ? (location$1.host && document.querySelector(\"base\") ? base : base.slice(hashIndex)) + to : createBaseLocation() + base + to;\n\t\ttry {\n\t\t\thistory$1[replace$1 ? \"replaceState\" : \"pushState\"](state, \"\", url);\n\t\t\thistoryState.value = state;\n\t\t} catch (err) {\n\t\t\tif (process.env.NODE_ENV !== \"production\") warn$1(\"Error with push/replace State\", err);\n\t\t\telse console.error(err);\n\t\t\tlocation$1[replace$1 ? \"replace\" : \"assign\"](url);\n\t\t}\n\t}\n\tfunction replace(to, data) {\n\t\tchangeLocation(to, assign({}, history$1.state, buildState(historyState.value.back, to, historyState.value.forward, true), data, { position: historyState.value.position }), true);\n\t\tcurrentLocation.value = to;\n\t}\n\tfunction push(to, data) {\n\t\tconst currentState = assign({}, historyState.value, history$1.state, {\n\t\t\tforward: to,\n\t\t\tscroll: computeScrollPosition()\n\t\t});\n\t\tif (process.env.NODE_ENV !== \"production\" && !history$1.state) warn$1(\"history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\\n\\nhistory.replaceState(history.state, '', url)\\n\\nYou can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state\");\n\t\tchangeLocation(currentState.current, currentState, true);\n\t\tchangeLocation(to, assign({}, buildState(currentLocation.value, to, null), { position: currentState.position + 1 }, data), false);\n\t\tcurrentLocation.value = to;\n\t}\n\treturn {\n\t\tlocation: currentLocation,\n\t\tstate: historyState,\n\t\tpush,\n\t\treplace\n\t};\n}\n/**\n* Creates an HTML5 history. Most common history for single page applications.\n*\n* @param base -\n*/\nfunction createWebHistory(base) {\n\tbase = normalizeBase(base);\n\tconst historyNavigation = useHistoryStateNavigation(base);\n\tconst historyListeners = useHistoryListeners(base, historyNavigation.state, historyNavigation.location, historyNavigation.replace);\n\tfunction go(delta, triggerListeners = true) {\n\t\tif (!triggerListeners) historyListeners.pauseListeners();\n\t\thistory.go(delta);\n\t}\n\tconst routerHistory = assign({\n\t\tlocation: \"\",\n\t\tbase,\n\t\tgo,\n\t\tcreateHref: createHref.bind(null, base)\n\t}, historyNavigation, historyListeners);\n\tObject.defineProperty(routerHistory, \"location\", {\n\t\tenumerable: true,\n\t\tget: () => historyNavigation.location.value\n\t});\n\tObject.defineProperty(routerHistory, \"state\", {\n\t\tenumerable: true,\n\t\tget: () => historyNavigation.state.value\n\t});\n\treturn routerHistory;\n}\n\n//#endregion\n//#region src/history/memory.ts\n/**\n* Creates an in-memory based history. The main purpose of this history is to handle SSR. It starts in a special location that is nowhere.\n* It's up to the user to replace that location with the starter location by either calling `router.push` or `router.replace`.\n*\n* @param base - Base applied to all urls, defaults to '/'\n* @returns a history object that can be passed to the router constructor\n*/\nfunction createMemoryHistory(base = \"\") {\n\tlet listeners = [];\n\tlet queue = [[START, {}]];\n\tlet position = 0;\n\tbase = normalizeBase(base);\n\tfunction setLocation(location$1, state = {}) {\n\t\tposition++;\n\t\tif (position !== queue.length) queue.splice(position);\n\t\tqueue.push([location$1, state]);\n\t}\n\tfunction triggerListeners(to, from, { direction, delta }) {\n\t\tconst info = {\n\t\t\tdirection,\n\t\t\tdelta,\n\t\t\ttype: NavigationType.pop\n\t\t};\n\t\tfor (const callback of listeners) callback(to, from, info);\n\t}\n\tconst routerHistory = {\n\t\tlocation: START,\n\t\tstate: {},\n\t\tbase,\n\t\tcreateHref: createHref.bind(null, base),\n\t\treplace(to, state) {\n\t\t\tqueue.splice(position--, 1);\n\t\t\tsetLocation(to, state);\n\t\t},\n\t\tpush(to, state) {\n\t\t\tsetLocation(to, state);\n\t\t},\n\t\tlisten(callback) {\n\t\t\tlisteners.push(callback);\n\t\t\treturn () => {\n\t\t\t\tconst index = listeners.indexOf(callback);\n\t\t\t\tif (index > -1) listeners.splice(index, 1);\n\t\t\t};\n\t\t},\n\t\tdestroy() {\n\t\t\tlisteners = [];\n\t\t\tqueue = [[START, {}]];\n\t\t\tposition = 0;\n\t\t},\n\t\tgo(delta, shouldTrigger = true) {\n\t\t\tconst from = this.location;\n\t\t\tconst direction = delta < 0 ? NavigationDirection.back : NavigationDirection.forward;\n\t\t\tposition = Math.max(0, Math.min(position + delta, queue.length - 1));\n\t\t\tif (shouldTrigger) triggerListeners(this.location, from, {\n\t\t\t\tdirection,\n\t\t\t\tdelta\n\t\t\t});\n\t\t}\n\t};\n\tObject.defineProperty(routerHistory, \"location\", {\n\t\tenumerable: true,\n\t\tget: () => queue[position][0]\n\t});\n\tObject.defineProperty(routerHistory, \"state\", {\n\t\tenumerable: true,\n\t\tget: () => queue[position][1]\n\t});\n\treturn routerHistory;\n}\n\n//#endregion\n//#region src/history/hash.ts\n/**\n* Creates a hash history. Useful for web applications with no host (e.g. `file://`) or when configuring a server to\n* handle any URL is not possible.\n*\n* @param base - optional base to provide. Defaults to `location.pathname + location.search` If there is a `<base>` tag\n* in the `head`, its value will be ignored in favor of this parameter **but note it affects all the history.pushState()\n* calls**, meaning that if you use a `<base>` tag, it's `href` value **has to match this parameter** (ignoring anything\n* after the `#`).\n*\n* @example\n* ```js\n* // at https://example.com/folder\n* createWebHashHistory() // gives a url of `https://example.com/folder#`\n* createWebHashHistory('/folder/') // gives a url of `https://example.com/folder/#`\n* // if the `#` is provided in the base, it won't be added by `createWebHashHistory`\n* createWebHashHistory('/folder/#/app/') // gives a url of `https://example.com/folder/#/app/`\n* // you should avoid doing this because it changes the original url and breaks copying urls\n* createWebHashHistory('/other-folder/') // gives a url of `https://example.com/other-folder/#`\n*\n* // at file:///usr/etc/folder/index.html\n* // for locations with no `host`, the base is ignored\n* createWebHashHistory('/iAmIgnored') // gives a url of `file:///usr/etc/folder/index.html#`\n* ```\n*/\nfunction createWebHashHistory(base) {\n\tbase = location.host ? base || location.pathname + location.search : \"\";\n\tif (!base.includes(\"#\")) base += \"#\";\n\tif (process.env.NODE_ENV !== \"production\" && !base.endsWith(\"#/\") && !base.endsWith(\"#\")) warn$1(`A hash base must end with a \"#\":\\n\"${base}\" should be \"${base.replace(/#.*$/, \"#\")}\".`);\n\treturn createWebHistory(base);\n}\n\n//#endregion\n//#region src/matcher/pathTokenizer.ts\nlet TokenType = /* @__PURE__ */ function(TokenType$1) {\n\tTokenType$1[TokenType$1[\"Static\"] = 0] = \"Static\";\n\tTokenType$1[TokenType$1[\"Param\"] = 1] = \"Param\";\n\tTokenType$1[TokenType$1[\"Group\"] = 2] = \"Group\";\n\treturn TokenType$1;\n}({});\nvar TokenizerState = /* @__PURE__ */ function(TokenizerState$1) {\n\tTokenizerState$1[TokenizerState$1[\"Static\"] = 0] = \"Static\";\n\tTokenizerState$1[TokenizerState$1[\"Param\"] = 1] = \"Param\";\n\tTokenizerState$1[TokenizerState$1[\"ParamRegExp\"] = 2] = \"ParamRegExp\";\n\tTokenizerState$1[TokenizerState$1[\"ParamRegExpEnd\"] = 3] = \"ParamRegExpEnd\";\n\tTokenizerState$1[TokenizerState$1[\"EscapeNext\"] = 4] = \"EscapeNext\";\n\treturn TokenizerState$1;\n}(TokenizerState || {});\nconst ROOT_TOKEN = {\n\ttype: TokenType.Static,\n\tvalue: \"\"\n};\nconst VALID_PARAM_RE = /[a-zA-Z0-9_]/;\nfunction tokenizePath(path) {\n\tif (!path) return [[]];\n\tif (path === \"/\") return [[ROOT_TOKEN]];\n\tif (!path.startsWith(\"/\")) throw new Error(process.env.NODE_ENV !== \"production\" ? `Route paths should start with a \"/\": \"${path}\" should be \"/${path}\".` : `Invalid path \"${path}\"`);\n\tfunction crash(message) {\n\t\tthrow new Error(`ERR (${state})/\"${buffer}\": ${message}`);\n\t}\n\tlet state = TokenizerState.Static;\n\tlet previousState = state;\n\tconst tokens = [];\n\tlet segment;\n\tfunction finalizeSegment() {\n\t\tif (segment) tokens.push(segment);\n\t\tsegment = [];\n\t}\n\tlet i = 0;\n\tlet char;\n\tlet buffer = \"\";\n\tlet customRe = \"\";\n\tfunction consumeBuffer() {\n\t\tif (!buffer) return;\n\t\tif (state === TokenizerState.Static) segment.push({\n\t\t\ttype: TokenType.Static,\n\t\t\tvalue: buffer\n\t\t});\n\t\telse if (state === TokenizerState.Param || state === TokenizerState.ParamRegExp || state === TokenizerState.ParamRegExpEnd) {\n\t\t\tif (segment.length > 1 && (char === \"*\" || char === \"+\")) crash(`A repeatable param (${buffer}) must be alone in its segment. eg: '/:ids+.`);\n\t\t\tsegment.push({\n\t\t\t\ttype: TokenType.Param,\n\t\t\t\tvalue: buffer,\n\t\t\t\tregexp: customRe,\n\t\t\t\trepeatable: char === \"*\" || char === \"+\",\n\t\t\t\toptional: char === \"*\" || char === \"?\"\n\t\t\t});\n\t\t} else crash(\"Invalid state to consume buffer\");\n\t\tbuffer = \"\";\n\t}\n\tfunction addCharToBuffer() {\n\t\tbuffer += char;\n\t}\n\twhile (i < path.length) {\n\t\tchar = path[i++];\n\t\tif (char === \"\\\\\" && state !== TokenizerState.ParamRegExp) {\n\t\t\tpreviousState = state;\n\t\t\tstate = TokenizerState.EscapeNext;\n\t\t\tcontinue;\n\t\t}\n\t\tswitch (state) {\n\t\t\tcase TokenizerState.Static:\n\t\t\t\tif (char === \"/\") {\n\t\t\t\t\tif (buffer) consumeBuffer();\n\t\t\t\t\tfinalizeSegment();\n\t\t\t\t} else if (char === \":\") {\n\t\t\t\t\tconsumeBuffer();\n\t\t\t\t\tstate = TokenizerState.Param;\n\t\t\t\t} else addCharToBuffer();\n\t\t\t\tbreak;\n\t\t\tcase TokenizerState.EscapeNext:\n\t\t\t\taddCharToBuffer();\n\t\t\t\tstate = previousState;\n\t\t\t\tbreak;\n\t\t\tcase TokenizerState.Param:\n\t\t\t\tif (char === \"(\") state = TokenizerState.ParamRegExp;\n\t\t\t\telse if (VALID_PARAM_RE.test(char)) addCharToBuffer();\n\t\t\t\telse {\n\t\t\t\t\tconsumeBuffer();\n\t\t\t\t\tstate = TokenizerState.Static;\n\t\t\t\t\tif (char !== \"*\" && char !== \"?\" && char !== \"+\") i--;\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase TokenizerState.ParamRegExp:\n\t\t\t\tif (char === \")\") if (customRe[customRe.length - 1] == \"\\\\\") customRe = customRe.slice(0, -1) + char;\n\t\t\t\telse state = TokenizerState.ParamRegExpEnd;\n\t\t\t\telse customRe += char;\n\t\t\t\tbreak;\n\t\t\tcase TokenizerState.ParamRegExpEnd:\n\t\t\t\tconsumeBuffer();\n\t\t\t\tstate = TokenizerState.Static;\n\t\t\t\tif (char !== \"*\" && char !== \"?\" && char !== \"+\") i--;\n\t\t\t\tcustomRe = \"\";\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcrash(\"Unknown state\");\n\t\t\t\tbreak;\n\t\t}\n\t}\n\tif (state === TokenizerState.ParamRegExp) crash(`Unfinished custom RegExp for param \"${buffer}\"`);\n\tconsumeBuffer();\n\tfinalizeSegment();\n\treturn tokens;\n}\n\n//#endregion\n//#region src/matcher/pathParserRanker.ts\nconst BASE_PARAM_PATTERN = \"[^/]+?\";\nconst BASE_PATH_PARSER_OPTIONS = {\n\tsensitive: false,\n\tstrict: false,\n\tstart: true,\n\tend: true\n};\nvar PathScore = /* @__PURE__ */ function(PathScore$1) {\n\tPathScore$1[PathScore$1[\"_multiplier\"] = 10] = \"_multiplier\";\n\tPathScore$1[PathScore$1[\"Root\"] = 90] = \"Root\";\n\tPathScore$1[PathScore$1[\"Segment\"] = 40] = \"Segment\";\n\tPathScore$1[PathScore$1[\"SubSegment\"] = 30] = \"SubSegment\";\n\tPathScore$1[PathScore$1[\"Static\"] = 40] = \"Static\";\n\tPathScore$1[PathScore$1[\"Dynamic\"] = 20] = \"Dynamic\";\n\tPathScore$1[PathScore$1[\"BonusCustomRegExp\"] = 10] = \"BonusCustomRegExp\";\n\tPathScore$1[PathScore$1[\"BonusWildcard\"] = -50] = \"BonusWildcard\";\n\tPathScore$1[PathScore$1[\"BonusRepeatable\"] = -20] = \"BonusRepeatable\";\n\tPathScore$1[PathScore$1[\"BonusOptional\"] = -8] = \"BonusOptional\";\n\tPathScore$1[PathScore$1[\"BonusStrict\"] = .7000000000000001] = \"BonusStrict\";\n\tPathScore$1[PathScore$1[\"BonusCaseSensitive\"] = .25] = \"BonusCaseSensitive\";\n\treturn PathScore$1;\n}(PathScore || {});\nconst REGEX_CHARS_RE = /[.+*?^${}()[\\]/\\\\]/g;\n/**\n* Creates a path parser from an array of Segments (a segment is an array of Tokens)\n*\n* @param segments - array of segments returned by tokenizePath\n* @param extraOptions - optional options for the regexp\n* @returns a PathParser\n*/\nfunction tokensToParser(segments, extraOptions) {\n\tconst options = assign({}, BASE_PATH_PARSER_OPTIONS, extraOptions);\n\tconst score = [];\n\tlet pattern = options.start ? \"^\" : \"\";\n\tconst keys = [];\n\tfor (const segment of segments) {\n\t\tconst segmentScores = segment.length ? [] : [PathScore.Root];\n\t\tif (options.strict && !segment.length) pattern += \"/\";\n\t\tfor (let tokenIndex = 0; tokenIndex < segment.length; tokenIndex++) {\n\t\t\tconst token = segment[tokenIndex];\n\t\t\tlet subSegmentScore = PathScore.Segment + (options.sensitive ? PathScore.BonusCaseSensitive : 0);\n\t\t\tif (token.type === TokenType.Static) {\n\t\t\t\tif (!tokenIndex) pattern += \"/\";\n\t\t\t\tpattern += token.value.replace(REGEX_CHARS_RE, \"\\\\$&\");\n\t\t\t\tsubSegmentScore += PathScore.Static;\n\t\t\t} else if (token.type === TokenType.Param) {\n\t\t\t\tconst { value, repeatable, optional, regexp } = token;\n\t\t\t\tkeys.push({\n\t\t\t\t\tname: value,\n\t\t\t\t\trepeatable,\n\t\t\t\t\toptional\n\t\t\t\t});\n\t\t\t\tconst re$1 = regexp ? regexp : BASE_PARAM_PATTERN;\n\t\t\t\tif (re$1 !== BASE_PARAM_PATTERN) {\n\t\t\t\t\tsubSegmentScore += PathScore.BonusCustomRegExp;\n\t\t\t\t\ttry {\n\t\t\t\t\t\t`${re$1}`;\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tthrow new Error(`Invalid custom RegExp for param \"${value}\" (${re$1}): ` + err.message);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet subPattern = repeatable ? `((?:${re$1})(?:/(?:${re$1}))*)` : `(${re$1})`;\n\t\t\t\tif (!tokenIndex) subPattern = optional && segment.length < 2 ? `(?:/${subPattern})` : \"/\" + subPattern;\n\t\t\t\tif (optional) subPattern += \"?\";\n\t\t\t\tpattern += subPattern;\n\t\t\t\tsubSegmentScore += PathScore.Dynamic;\n\t\t\t\tif (optional) subSegmentScore += PathScore.BonusOptional;\n\t\t\t\tif (repeatable) subSegmentScore += PathScore.BonusRepeatable;\n\t\t\t\tif (re$1 === \".*\") subSegmentScore += PathScore.BonusWildcard;\n\t\t\t}\n\t\t\tsegmentScores.push(subSegmentScore);\n\t\t}\n\t\tscore.push(segmentScores);\n\t}\n\tif (options.strict && options.end) {\n\t\tconst i = score.length - 1;\n\t\tscore[i][score[i].length - 1] += PathScore.BonusStrict;\n\t}\n\tif (!options.strict) pattern += \"/?\";\n\tif (options.end) pattern += \"$\";\n\telse if (options.strict && !pattern.endsWith(\"/\")) pattern += \"(?:/|$)\";\n\tconst re = new RegExp(pattern, options.sensitive ? \"\" : \"i\");\n\tfunction parse(path) {\n\t\tconst match = path.match(re);\n\t\tconst params = {};\n\t\tif (!match) return null;\n\t\tfor (let i = 1; i < match.length; i++) {\n\t\t\tconst value = match[i] || \"\";\n\t\t\tconst key = keys[i - 1];\n\t\t\tparams[key.name] = value && key.repeatable ? value.split(\"/\") : value;\n\t\t}\n\t\treturn params;\n\t}\n\tfunction stringify(params) {\n\t\tlet path = \"\";\n\t\tlet avoidDuplicatedSlash = false;\n\t\tfor (const segment of segments) {\n\t\t\tif (!avoidDuplicatedSlash || !path.endsWith(\"/\")) path += \"/\";\n\t\t\tavoidDuplicatedSlash = false;\n\t\t\tfor (const token of segment) if (token.type === TokenType.Static) path += token.value;\n\t\t\telse if (token.type === TokenType.Param) {\n\t\t\t\tconst { value, repeatable, optional } = token;\n\t\t\t\tconst param = value in params ? params[value] : \"\";\n\t\t\t\tif (isArray(param) && !repeatable) throw new Error(`Provided param \"${value}\" is an array but it is not repeatable (* or + modifiers)`);\n\t\t\t\tconst text = isArray(param) ? param.join(\"/\") : param;\n\t\t\t\tif (!text) if (optional) {\n\t\t\t\t\tif (segment.length < 2) if (path.endsWith(\"/\")) path = path.slice(0, -1);\n\t\t\t\t\telse avoidDuplicatedSlash = true;\n\t\t\t\t} else throw new Error(`Missing required param \"${value}\"`);\n\t\t\t\tpath += text;\n\t\t\t}\n\t\t}\n\t\treturn path || \"/\";\n\t}\n\treturn {\n\t\tre,\n\t\tscore,\n\t\tkeys,\n\t\tparse,\n\t\tstringify\n\t};\n}\n/**\n* Compares an array of numbers as used in PathParser.score and returns a\n* number. This function can be used to `sort` an array\n*\n* @param a - first array of numbers\n* @param b - second array of numbers\n* @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n* should be sorted first\n*/\nfunction compareScoreArray(a, b) {\n\tlet i = 0;\n\twhile (i < a.length && i < b.length) {\n\t\tconst diff = b[i] - a[i];\n\t\tif (diff) return diff;\n\t\ti++;\n\t}\n\tif (a.length < b.length) return a.length === 1 && a[0] === PathScore.Static + PathScore.Segment ? -1 : 1;\n\telse if (a.length > b.length) return b.length === 1 && b[0] === PathScore.Static + PathScore.Segment ? 1 : -1;\n\treturn 0;\n}\n/**\n* Compare function that can be used with `sort` to sort an array of PathParser\n*\n* @param a - first PathParser\n* @param b - second PathParser\n* @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n*/\nfunction comparePathParserScore(a, b) {\n\tlet i = 0;\n\tconst aScore = a.score;\n\tconst bScore = b.score;\n\twhile (i < aScore.length && i < bScore.length) {\n\t\tconst comp = compareScoreArray(aScore[i], bScore[i]);\n\t\tif (comp) return comp;\n\t\ti++;\n\t}\n\tif (Math.abs(bScore.length - aScore.length) === 1) {\n\t\tif (isLastScoreNegative(aScore)) return 1;\n\t\tif (isLastScoreNegative(bScore)) return -1;\n\t}\n\treturn bScore.length - aScore.length;\n}\n/**\n* This allows detecting splats at the end of a path: /home/<USER>\n*\n* @param score - score to check\n* @returns true if the last entry is negative\n*/\nfunction isLastScoreNegative(score) {\n\tconst last = score[score.length - 1];\n\treturn score.length > 0 && last[last.length - 1] < 0;\n}\nconst PATH_PARSER_OPTIONS_DEFAULTS = {\n\tstrict: false,\n\tend: true,\n\tsensitive: false\n};\n\n//#endregion\n//#region src/matcher/pathMatcher.ts\nfunction createRouteRecordMatcher(record, parent, options) {\n\tconst parser = tokensToParser(tokenizePath(record.path), options);\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\tconst existingKeys = /* @__PURE__ */ new Set();\n\t\tfor (const key of parser.keys) {\n\t\t\tif (existingKeys.has(key.name)) warn$1(`Found duplicated params with name \"${key.name}\" for path \"${record.path}\". Only the last one will be available on \"$route.params\".`);\n\t\t\texistingKeys.add(key.name);\n\t\t}\n\t}\n\tconst matcher = assign(parser, {\n\t\trecord,\n\t\tparent,\n\t\tchildren: [],\n\t\talias: []\n\t});\n\tif (parent) {\n\t\tif (!matcher.record.aliasOf === !parent.record.aliasOf) parent.children.push(matcher);\n\t}\n\treturn matcher;\n}\n\n//#endregion\n//#region src/matcher/index.ts\n/**\n* Creates a Router Matcher.\n*\n* @internal\n* @param routes - array of initial routes\n* @param globalOptions - global route options\n*/\nfunction createRouterMatcher(routes, globalOptions) {\n\tconst matchers = [];\n\tconst matcherMap = /* @__PURE__ */ new Map();\n\tglobalOptions = mergeOptions(PATH_PARSER_OPTIONS_DEFAULTS, globalOptions);\n\tfunction getRecordMatcher(name) {\n\t\treturn matcherMap.get(name);\n\t}\n\tfunction addRoute(record, parent, originalRecord) {\n\t\tconst isRootAdd = !originalRecord;\n\t\tconst mainNormalizedRecord = normalizeRouteRecord(record);\n\t\tif (process.env.NODE_ENV !== \"production\") checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent);\n\t\tmainNormalizedRecord.aliasOf = originalRecord && originalRecord.record;\n\t\tconst options = mergeOptions(globalOptions, record);\n\t\tconst normalizedRecords = [mainNormalizedRecord];\n\t\tif (\"alias\" in record) {\n\t\t\tconst aliases = typeof record.alias === \"string\" ? [record.alias] : record.alias;\n\t\t\tfor (const alias of aliases) normalizedRecords.push(normalizeRouteRecord(assign({}, mainNormalizedRecord, {\n\t\t\t\tcomponents: originalRecord ? originalRecord.record.components : mainNormalizedRecord.components,\n\t\t\t\tpath: alias,\n\t\t\t\taliasOf: originalRecord ? originalRecord.record : mainNormalizedRecord\n\t\t\t})));\n\t\t}\n\t\tlet matcher;\n\t\tlet originalMatcher;\n\t\tfor (const normalizedRecord of normalizedRecords) {\n\t\t\tconst { path } = normalizedRecord;\n\t\t\tif (parent && path[0] !== \"/\") {\n\t\t\t\tconst parentPath = parent.record.path;\n\t\t\t\tconst connectingSlash = parentPath[parentPath.length - 1] === \"/\" ? \"\" : \"/\";\n\t\t\t\tnormalizedRecord.path = parent.record.path + (path && connectingSlash + path);\n\t\t\t}\n\t\t\tif (process.env.NODE_ENV !== \"production\" && normalizedRecord.path === \"*\") throw new Error(\"Catch all routes (\\\"*\\\") must now be defined using a param with a custom regexp.\\nSee more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.\");\n\t\t\tmatcher = createRouteRecordMatcher(normalizedRecord, parent, options);\n\t\t\tif (process.env.NODE_ENV !== \"production\" && parent && path[0] === \"/\") checkMissingParamsInAbsolutePath(matcher, parent);\n\t\t\tif (originalRecord) {\n\t\t\t\toriginalRecord.alias.push(matcher);\n\t\t\t\tif (process.env.NODE_ENV !== \"production\") checkSameParams(originalRecord, matcher);\n\t\t\t} else {\n\t\t\t\toriginalMatcher = originalMatcher || matcher;\n\t\t\t\tif (originalMatcher !== matcher) originalMatcher.alias.push(matcher);\n\t\t\t\tif (isRootAdd && record.name && !isAliasRecord(matcher)) {\n\t\t\t\t\tif (process.env.NODE_ENV !== \"production\") checkSameNameAsAncestor(record, parent);\n\t\t\t\t\tremoveRoute(record.name);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (isMatchable(matcher)) insertMatcher(matcher);\n\t\t\tif (mainNormalizedRecord.children) {\n\t\t\t\tconst children = mainNormalizedRecord.children;\n\t\t\t\tfor (let i = 0; i < children.length; i++) addRoute(children[i], matcher, originalRecord && originalRecord.children[i]);\n\t\t\t}\n\t\t\toriginalRecord = originalRecord || matcher;\n\t\t}\n\t\treturn originalMatcher ? () => {\n\t\t\tremoveRoute(originalMatcher);\n\t\t} : noop;\n\t}\n\tfunction removeRoute(matcherRef) {\n\t\tif (isRouteName(matcherRef)) {\n\t\t\tconst matcher = matcherMap.get(matcherRef);\n\t\t\tif (matcher) {\n\t\t\t\tmatcherMap.delete(matcherRef);\n\t\t\t\tmatchers.splice(matchers.indexOf(matcher), 1);\n\t\t\t\tmatcher.children.forEach(removeRoute);\n\t\t\t\tmatcher.alias.forEach(removeRoute);\n\t\t\t}\n\t\t} else {\n\t\t\tconst index = matchers.indexOf(matcherRef);\n\t\t\tif (index > -1) {\n\t\t\t\tmatchers.splice(index, 1);\n\t\t\t\tif (matcherRef.record.name) matcherMap.delete(matcherRef.record.name);\n\t\t\t\tmatcherRef.children.forEach(removeRoute);\n\t\t\t\tmatcherRef.alias.forEach(removeRoute);\n\t\t\t}\n\t\t}\n\t}\n\tfunction getRoutes() {\n\t\treturn matchers;\n\t}\n\tfunction insertMatcher(matcher) {\n\t\tconst index = findInsertionIndex(matcher, matchers);\n\t\tmatchers.splice(index, 0, matcher);\n\t\tif (matcher.record.name && !isAliasRecord(matcher)) matcherMap.set(matcher.record.name, matcher);\n\t}\n\tfunction resolve(location$1, currentLocation) {\n\t\tlet matcher;\n\t\tlet params = {};\n\t\tlet path;\n\t\tlet name;\n\t\tif (\"name\" in location$1 && location$1.name) {\n\t\t\tmatcher = matcherMap.get(location$1.name);\n\t\t\tif (!matcher) throw createRouterError(ErrorTypes.MATCHER_NOT_FOUND, { location: location$1 });\n\t\t\tif (process.env.NODE_ENV !== \"production\") {\n\t\t\t\tconst invalidParams = Object.keys(location$1.params || {}).filter((paramName) => !matcher.keys.find((k) => k.name === paramName));\n\t\t\t\tif (invalidParams.length) warn$1(`Discarded invalid param(s) \"${invalidParams.join(\"\\\", \\\"\")}\" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);\n\t\t\t}\n\t\t\tname = matcher.record.name;\n\t\t\tparams = assign(pickParams(currentLocation.params, matcher.keys.filter((k) => !k.optional).concat(matcher.parent ? matcher.parent.keys.filter((k) => k.optional) : []).map((k) => k.name)), location$1.params && pickParams(location$1.params, matcher.keys.map((k) => k.name)));\n\t\t\tpath = matcher.stringify(params);\n\t\t} else if (location$1.path != null) {\n\t\t\tpath = location$1.path;\n\t\t\tif (process.env.NODE_ENV !== \"production\" && !path.startsWith(\"/\")) warn$1(`The Matcher cannot resolve relative paths but received \"${path}\". Unless you directly called \\`matcher.resolve(\"${path}\")\\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`);\n\t\t\tmatcher = matchers.find((m) => m.re.test(path));\n\t\t\tif (matcher) {\n\t\t\t\tparams = matcher.parse(path);\n\t\t\t\tname = matcher.record.name;\n\t\t\t}\n\t\t} else {\n\t\t\tmatcher = currentLocation.name ? matcherMap.get(currentLocation.name) : matchers.find((m) => m.re.test(currentLocation.path));\n\t\t\tif (!matcher) throw createRouterError(ErrorTypes.MATCHER_NOT_FOUND, {\n\t\t\t\tlocation: location$1,\n\t\t\t\tcurrentLocation\n\t\t\t});\n\t\t\tname = matcher.record.name;\n\t\t\tparams = assign({}, currentLocation.params, location$1.params);\n\t\t\tpath = matcher.stringify(params);\n\t\t}\n\t\tconst matched = [];\n\t\tlet parentMatcher = matcher;\n\t\twhile (parentMatcher) {\n\t\t\tmatched.unshift(parentMatcher.record);\n\t\t\tparentMatcher = parentMatcher.parent;\n\t\t}\n\t\treturn {\n\t\t\tname,\n\t\t\tpath,\n\t\t\tparams,\n\t\t\tmatched,\n\t\t\tmeta: mergeMetaFields(matched)\n\t\t};\n\t}\n\troutes.forEach((route) => addRoute(route));\n\tfunction clearRoutes() {\n\t\tmatchers.length = 0;\n\t\tmatcherMap.clear();\n\t}\n\treturn {\n\t\taddRoute,\n\t\tresolve,\n\t\tremoveRoute,\n\t\tclearRoutes,\n\t\tgetRoutes,\n\t\tgetRecordMatcher\n\t};\n}\n/**\n* Picks an object param to contain only specified keys.\n*\n* @param params - params object to pick from\n* @param keys - keys to pick\n*/\nfunction pickParams(params, keys) {\n\tconst newParams = {};\n\tfor (const key of keys) if (key in params) newParams[key] = params[key];\n\treturn newParams;\n}\n/**\n* Normalizes a RouteRecordRaw. Creates a copy\n*\n* @param record\n* @returns the normalized version\n*/\nfunction normalizeRouteRecord(record) {\n\tconst normalized = {\n\t\tpath: record.path,\n\t\tredirect: record.redirect,\n\t\tname: record.name,\n\t\tmeta: record.meta || {},\n\t\taliasOf: record.aliasOf,\n\t\tbeforeEnter: record.beforeEnter,\n\t\tprops: normalizeRecordProps(record),\n\t\tchildren: record.children || [],\n\t\tinstances: {},\n\t\tleaveGuards: /* @__PURE__ */ new Set(),\n\t\tupdateGuards: /* @__PURE__ */ new Set(),\n\t\tenterCallbacks: {},\n\t\tcomponents: \"components\" in record ? record.components || null : record.component && { default: record.component }\n\t};\n\tObject.defineProperty(normalized, \"mods\", { value: {} });\n\treturn normalized;\n}\n/**\n* Normalize the optional `props` in a record to always be an object similar to\n* components. Also accept a boolean for components.\n* @param record\n*/\nfunction normalizeRecordProps(record) {\n\tconst propsObject = {};\n\tconst props = record.props || false;\n\tif (\"component\" in record) propsObject.default = props;\n\telse for (const name in record.components) propsObject[name] = typeof props === \"object\" ? props[name] : props;\n\treturn propsObject;\n}\n/**\n* Checks if a record or any of its parent is an alias\n* @param record\n*/\nfunction isAliasRecord(record) {\n\twhile (record) {\n\t\tif (record.record.aliasOf) return true;\n\t\trecord = record.parent;\n\t}\n\treturn false;\n}\n/**\n* Merge meta fields of an array of records\n*\n* @param matched - array of matched records\n*/\nfunction mergeMetaFields(matched) {\n\treturn matched.reduce((meta, record) => assign(meta, record.meta), {});\n}\nfunction isSameParam(a, b) {\n\treturn a.name === b.name && a.optional === b.optional && a.repeatable === b.repeatable;\n}\n/**\n* Check if a path and its alias have the same required params\n*\n* @param a - original record\n* @param b - alias record\n*/\nfunction checkSameParams(a, b) {\n\tfor (const key of a.keys) if (!key.optional && !b.keys.find(isSameParam.bind(null, key))) return warn$1(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n\tfor (const key of b.keys) if (!key.optional && !a.keys.find(isSameParam.bind(null, key))) return warn$1(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n}\n/**\n* A route with a name and a child with an empty path without a name should warn when adding the route\n*\n* @param mainNormalizedRecord - RouteRecordNormalized\n* @param parent - RouteRecordMatcher\n*/\nfunction checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent) {\n\tif (parent && parent.record.name && !mainNormalizedRecord.name && !mainNormalizedRecord.path) warn$1(`The route named \"${String(parent.record.name)}\" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);\n}\nfunction checkSameNameAsAncestor(record, parent) {\n\tfor (let ancestor = parent; ancestor; ancestor = ancestor.parent) if (ancestor.record.name === record.name) throw new Error(`A route named \"${String(record.name)}\" has been added as a ${parent === ancestor ? \"child\" : \"descendant\"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);\n}\nfunction checkMissingParamsInAbsolutePath(record, parent) {\n\tfor (const key of parent.keys) if (!record.keys.find(isSameParam.bind(null, key))) return warn$1(`Absolute path \"${record.record.path}\" must have the exact same param named \"${key.name}\" as its parent \"${parent.record.path}\".`);\n}\n/**\n* Performs a binary search to find the correct insertion index for a new matcher.\n*\n* Matchers are primarily sorted by their score. If scores are tied then we also consider parent/child relationships,\n* with descendants coming before ancestors. If there's still a tie, new routes are inserted after existing routes.\n*\n* @param matcher - new matcher to be inserted\n* @param matchers - existing matchers\n*/\nfunction findInsertionIndex(matcher, matchers) {\n\tlet lower = 0;\n\tlet upper = matchers.length;\n\twhile (lower !== upper) {\n\t\tconst mid = lower + upper >> 1;\n\t\tif (comparePathParserScore(matcher, matchers[mid]) < 0) upper = mid;\n\t\telse lower = mid + 1;\n\t}\n\tconst insertionAncestor = getInsertionAncestor(matcher);\n\tif (insertionAncestor) {\n\t\tupper = matchers.lastIndexOf(insertionAncestor, upper - 1);\n\t\tif (process.env.NODE_ENV !== \"production\" && upper < 0) warn$1(`Finding ancestor route \"${insertionAncestor.record.path}\" failed for \"${matcher.record.path}\"`);\n\t}\n\treturn upper;\n}\nfunction getInsertionAncestor(matcher) {\n\tlet ancestor = matcher;\n\twhile (ancestor = ancestor.parent) if (isMatchable(ancestor) && comparePathParserScore(matcher, ancestor) === 0) return ancestor;\n}\n/**\n* Checks if a matcher can be reachable. This means if it's possible to reach it as a route. For example, routes without\n* a component, or name, or redirect, are just used to group other routes.\n* @param matcher\n* @param matcher.record record of the matcher\n* @returns\n*/\nfunction isMatchable({ record }) {\n\treturn !!(record.name || record.components && Object.keys(record.components).length || record.redirect);\n}\n\n//#endregion\n//#region src/RouterLink.ts\n/**\n* Returns the internal behavior of a {@link RouterLink} without the rendering part.\n*\n* @param props - a `to` location and an optional `replace` flag\n*/\nfunction useLink(props) {\n\tconst router = inject(routerKey);\n\tconst currentRoute = inject(routeLocationKey);\n\tlet hasPrevious = false;\n\tlet previousTo = null;\n\tconst route = computed(() => {\n\t\tconst to = unref(props.to);\n\t\tif (process.env.NODE_ENV !== \"production\" && (!hasPrevious || to !== previousTo)) {\n\t\t\tif (!isRouteLocation(to)) if (hasPrevious) warn$1(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- previous to:`, previousTo, `\\n- props:`, props);\n\t\t\telse warn$1(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- props:`, props);\n\t\t\tpreviousTo = to;\n\t\t\thasPrevious = true;\n\t\t}\n\t\treturn router.resolve(to);\n\t});\n\tconst activeRecordIndex = computed(() => {\n\t\tconst { matched } = route.value;\n\t\tconst { length } = matched;\n\t\tconst routeMatched = matched[length - 1];\n\t\tconst currentMatched = currentRoute.matched;\n\t\tif (!routeMatched || !currentMatched.length) return -1;\n\t\tconst index = currentMatched.findIndex(isSameRouteRecord.bind(null, routeMatched));\n\t\tif (index > -1) return index;\n\t\tconst parentRecordPath = getOriginalPath(matched[length - 2]);\n\t\treturn length > 1 && getOriginalPath(routeMatched) === parentRecordPath && currentMatched[currentMatched.length - 1].path !== parentRecordPath ? currentMatched.findIndex(isSameRouteRecord.bind(null, matched[length - 2])) : index;\n\t});\n\tconst isActive = computed(() => activeRecordIndex.value > -1 && includesParams(currentRoute.params, route.value.params));\n\tconst isExactActive = computed(() => activeRecordIndex.value > -1 && activeRecordIndex.value === currentRoute.matched.length - 1 && isSameRouteLocationParams(currentRoute.params, route.value.params));\n\tfunction navigate(e = {}) {\n\t\tif (guardEvent(e)) {\n\t\t\tconst p = router[unref(props.replace) ? \"replace\" : \"push\"](unref(props.to)).catch(noop);\n\t\t\tif (props.viewTransition && typeof document !== \"undefined\" && \"startViewTransition\" in document) document.startViewTransition(() => p);\n\t\t\treturn p;\n\t\t}\n\t\treturn Promise.resolve();\n\t}\n\tif ((process.env.NODE_ENV !== \"production\" || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n\t\tconst instance = getCurrentInstance();\n\t\tif (instance) {\n\t\t\tconst linkContextDevtools = {\n\t\t\t\troute: route.value,\n\t\t\t\tisActive: isActive.value,\n\t\t\t\tisExactActive: isExactActive.value,\n\t\t\t\terror: null\n\t\t\t};\n\t\t\tinstance.__vrl_devtools = instance.__vrl_devtools || [];\n\t\t\tinstance.__vrl_devtools.push(linkContextDevtools);\n\t\t\twatchEffect(() => {\n\t\t\t\tlinkContextDevtools.route = route.value;\n\t\t\t\tlinkContextDevtools.isActive = isActive.value;\n\t\t\t\tlinkContextDevtools.isExactActive = isExactActive.value;\n\t\t\t\tlinkContextDevtools.error = isRouteLocation(unref(props.to)) ? null : \"Invalid \\\"to\\\" value\";\n\t\t\t}, { flush: \"post\" });\n\t\t}\n\t}\n\t/**\n\t* NOTE: update {@link _RouterLinkI}'s `$slots` type when updating this\n\t*/\n\treturn {\n\t\troute,\n\t\thref: computed(() => route.value.href),\n\t\tisActive,\n\t\tisExactActive,\n\t\tnavigate\n\t};\n}\nfunction preferSingleVNode(vnodes) {\n\treturn vnodes.length === 1 ? vnodes[0] : vnodes;\n}\nconst RouterLinkImpl = /* @__PURE__ */ defineComponent({\n\tname: \"RouterLink\",\n\tcompatConfig: { MODE: 3 },\n\tprops: {\n\t\tto: {\n\t\t\ttype: [String, Object],\n\t\t\trequired: true\n\t\t},\n\t\treplace: Boolean,\n\t\tactiveClass: String,\n\t\texactActiveClass: String,\n\t\tcustom: Boolean,\n\t\tariaCurrentValue: {\n\t\t\ttype: String,\n\t\t\tdefault: \"page\"\n\t\t},\n\t\tviewTransition: Boolean\n\t},\n\tuseLink,\n\tsetup(props, { slots }) {\n\t\tconst link = reactive(useLink(props));\n\t\tconst { options } = inject(routerKey);\n\t\tconst elClass = computed(() => ({\n\t\t\t[getLinkClass(props.activeClass, options.linkActiveClass, \"router-link-active\")]: link.isActive,\n\t\t\t[getLinkClass(props.exactActiveClass, options.linkExactActiveClass, \"router-link-exact-active\")]: link.isExactActive\n\t\t}));\n\t\treturn () => {\n\t\t\tconst children = slots.default && preferSingleVNode(slots.default(link));\n\t\t\treturn props.custom ? children : h(\"a\", {\n\t\t\t\t\"aria-current\": link.isExactActive ? props.ariaCurrentValue : null,\n\t\t\t\thref: link.href,\n\t\t\t\tonClick: link.navigate,\n\t\t\t\tclass: elClass.value\n\t\t\t}, children);\n\t\t};\n\t}\n});\n/**\n* Component to render a link that triggers a navigation on click.\n*/\nconst RouterLink = RouterLinkImpl;\nfunction guardEvent(e) {\n\tif (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) return;\n\tif (e.defaultPrevented) return;\n\tif (e.button !== void 0 && e.button !== 0) return;\n\tif (e.currentTarget && e.currentTarget.getAttribute) {\n\t\tconst target = e.currentTarget.getAttribute(\"target\");\n\t\tif (/\\b_blank\\b/i.test(target)) return;\n\t}\n\tif (e.preventDefault) e.preventDefault();\n\treturn true;\n}\nfunction includesParams(outer, inner) {\n\tfor (const key in inner) {\n\t\tconst innerValue = inner[key];\n\t\tconst outerValue = outer[key];\n\t\tif (typeof innerValue === \"string\") {\n\t\t\tif (innerValue !== outerValue) return false;\n\t\t} else if (!isArray(outerValue) || outerValue.length !== innerValue.length || innerValue.some((value, i) => value !== outerValue[i])) return false;\n\t}\n\treturn true;\n}\n/**\n* Get the original path value of a record by following its aliasOf\n* @param record\n*/\nfunction getOriginalPath(record) {\n\treturn record ? record.aliasOf ? record.aliasOf.path : record.path : \"\";\n}\n/**\n* Utility class to get the active class based on defaults.\n* @param propClass\n* @param globalClass\n* @param defaultClass\n*/\nconst getLinkClass = (propClass, globalClass, defaultClass) => propClass != null ? propClass : globalClass != null ? globalClass : defaultClass;\n\n//#endregion\n//#region src/RouterView.ts\nconst RouterViewImpl = /* @__PURE__ */ defineComponent({\n\tname: \"RouterView\",\n\tinheritAttrs: false,\n\tprops: {\n\t\tname: {\n\t\t\ttype: String,\n\t\t\tdefault: \"default\"\n\t\t},\n\t\troute: Object\n\t},\n\tcompatConfig: { MODE: 3 },\n\tsetup(props, { attrs, slots }) {\n\t\tprocess.env.NODE_ENV !== \"production\" && warnDeprecatedUsage();\n\t\tconst injectedRoute = inject(routerViewLocationKey);\n\t\tconst routeToDisplay = computed(() => props.route || injectedRoute.value);\n\t\tconst injectedDepth = inject(viewDepthKey, 0);\n\t\tconst depth = computed(() => {\n\t\t\tlet initialDepth = unref(injectedDepth);\n\t\t\tconst { matched } = routeToDisplay.value;\n\t\t\tlet matchedRoute;\n\t\t\twhile ((matchedRoute = matched[initialDepth]) && !matchedRoute.components) initialDepth++;\n\t\t\treturn initialDepth;\n\t\t});\n\t\tconst matchedRouteRef = computed(() => routeToDisplay.value.matched[depth.value]);\n\t\tprovide(viewDepthKey, computed(() => depth.value + 1));\n\t\tprovide(matchedRouteKey, matchedRouteRef);\n\t\tprovide(routerViewLocationKey, routeToDisplay);\n\t\tconst viewRef = ref();\n\t\twatch(() => [\n\t\t\tviewRef.value,\n\t\t\tmatchedRouteRef.value,\n\t\t\tprops.name\n\t\t], ([instance, to, name], [oldInstance, from, oldName]) => {\n\t\t\tif (to) {\n\t\t\t\tto.instances[name] = instance;\n\t\t\t\tif (from && from !== to && instance && instance === oldInstance) {\n\t\t\t\t\tif (!to.leaveGuards.size) to.leaveGuards = from.leaveGuards;\n\t\t\t\t\tif (!to.updateGuards.size) to.updateGuards = from.updateGuards;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (instance && to && (!from || !isSameRouteRecord(to, from) || !oldInstance)) (to.enterCallbacks[name] || []).forEach((callback) => callback(instance));\n\t\t}, { flush: \"post\" });\n\t\treturn () => {\n\t\t\tconst route = routeToDisplay.value;\n\t\t\tconst currentName = props.name;\n\t\t\tconst matchedRoute = matchedRouteRef.value;\n\t\t\tconst ViewComponent = matchedRoute && matchedRoute.components[currentName];\n\t\t\tif (!ViewComponent) return normalizeSlot(slots.default, {\n\t\t\t\tComponent: ViewComponent,\n\t\t\t\troute\n\t\t\t});\n\t\t\tconst routePropsOption = matchedRoute.props[currentName];\n\t\t\tconst routeProps = routePropsOption ? routePropsOption === true ? route.params : typeof routePropsOption === \"function\" ? routePropsOption(route) : routePropsOption : null;\n\t\t\tconst onVnodeUnmounted = (vnode) => {\n\t\t\t\tif (vnode.component.isUnmounted) matchedRoute.instances[currentName] = null;\n\t\t\t};\n\t\t\tconst component = h(ViewComponent, assign({}, routeProps, attrs, {\n\t\t\t\tonVnodeUnmounted,\n\t\t\t\tref: viewRef\n\t\t\t}));\n\t\t\tif ((process.env.NODE_ENV !== \"production\" || __VUE_PROD_DEVTOOLS__) && isBrowser && component.ref) {\n\t\t\t\tconst info = {\n\t\t\t\t\tdepth: depth.value,\n\t\t\t\t\tname: matchedRoute.name,\n\t\t\t\t\tpath: matchedRoute.path,\n\t\t\t\t\tmeta: matchedRoute.meta\n\t\t\t\t};\n\t\t\t\t(isArray(component.ref) ? component.ref.map((r) => r.i) : [component.ref.i]).forEach((instance) => {\n\t\t\t\t\tinstance.__vrv_devtools = info;\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn normalizeSlot(slots.default, {\n\t\t\t\tComponent: component,\n\t\t\t\troute\n\t\t\t}) || component;\n\t\t};\n\t}\n});\nfunction normalizeSlot(slot, data) {\n\tif (!slot) return null;\n\tconst slotContent = slot(data);\n\treturn slotContent.length === 1 ? slotContent[0] : slotContent;\n}\n/**\n* Component to display the current route the user is at.\n*/\nconst RouterView = RouterViewImpl;\nfunction warnDeprecatedUsage() {\n\tconst instance = getCurrentInstance();\n\tconst parentName = instance.parent && instance.parent.type.name;\n\tconst parentSubTreeType = instance.parent && instance.parent.subTree && instance.parent.subTree.type;\n\tif (parentName && (parentName === \"KeepAlive\" || parentName.includes(\"Transition\")) && typeof parentSubTreeType === \"object\" && parentSubTreeType.name === \"RouterView\") {\n\t\tconst comp = parentName === \"KeepAlive\" ? \"keep-alive\" : \"transition\";\n\t\twarn$1(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\nUse slot props instead:\n\n<router-view v-slot=\"{ Component }\">\n  <${comp}>\\n    <component :is=\"Component\" />\\n  </${comp}>\\n</router-view>`);\n\t}\n}\n\n//#endregion\n//#region src/router.ts\n/**\n* Creates a Router instance that can be used by a Vue app.\n*\n* @param options - {@link RouterOptions}\n*/\nfunction createRouter(options) {\n\tconst matcher = createRouterMatcher(options.routes, options);\n\tconst parseQuery$1 = options.parseQuery || parseQuery;\n\tconst stringifyQuery$1 = options.stringifyQuery || stringifyQuery;\n\tconst routerHistory = options.history;\n\tif (process.env.NODE_ENV !== \"production\" && !routerHistory) throw new Error(\"Provide the \\\"history\\\" option when calling \\\"createRouter()\\\": https://router.vuejs.org/api/interfaces/RouterOptions.html#history\");\n\tconst beforeGuards = useCallbacks();\n\tconst beforeResolveGuards = useCallbacks();\n\tconst afterGuards = useCallbacks();\n\tconst currentRoute = shallowRef(START_LOCATION_NORMALIZED);\n\tlet pendingLocation = START_LOCATION_NORMALIZED;\n\tif (isBrowser && options.scrollBehavior && \"scrollRestoration\" in history) history.scrollRestoration = \"manual\";\n\tconst normalizeParams = applyToParams.bind(null, (paramValue) => \"\" + paramValue);\n\tconst encodeParams = applyToParams.bind(null, encodeParam);\n\tconst decodeParams = applyToParams.bind(null, decode);\n\tfunction addRoute(parentOrRoute, route) {\n\t\tlet parent;\n\t\tlet record;\n\t\tif (isRouteName(parentOrRoute)) {\n\t\t\tparent = matcher.getRecordMatcher(parentOrRoute);\n\t\t\tif (process.env.NODE_ENV !== \"production\" && !parent) warn$1(`Parent route \"${String(parentOrRoute)}\" not found when adding child route`, route);\n\t\t\trecord = route;\n\t\t} else record = parentOrRoute;\n\t\treturn matcher.addRoute(record, parent);\n\t}\n\tfunction removeRoute(name) {\n\t\tconst recordMatcher = matcher.getRecordMatcher(name);\n\t\tif (recordMatcher) matcher.removeRoute(recordMatcher);\n\t\telse if (process.env.NODE_ENV !== \"production\") warn$1(`Cannot remove non-existent route \"${String(name)}\"`);\n\t}\n\tfunction getRoutes() {\n\t\treturn matcher.getRoutes().map((routeMatcher) => routeMatcher.record);\n\t}\n\tfunction hasRoute(name) {\n\t\treturn !!matcher.getRecordMatcher(name);\n\t}\n\tfunction resolve(rawLocation, currentLocation) {\n\t\tcurrentLocation = assign({}, currentLocation || currentRoute.value);\n\t\tif (typeof rawLocation === \"string\") {\n\t\t\tconst locationNormalized = parseURL(parseQuery$1, rawLocation, currentLocation.path);\n\t\t\tconst matchedRoute$1 = matcher.resolve({ path: locationNormalized.path }, currentLocation);\n\t\t\tconst href$1 = routerHistory.createHref(locationNormalized.fullPath);\n\t\t\tif (process.env.NODE_ENV !== \"production\") {\n\t\t\t\tif (href$1.startsWith(\"//\")) warn$1(`Location \"${rawLocation}\" resolved to \"${href$1}\". A resolved location cannot start with multiple slashes.`);\n\t\t\t\telse if (!matchedRoute$1.matched.length) warn$1(`No match found for location with path \"${rawLocation}\"`);\n\t\t\t}\n\t\t\treturn assign(locationNormalized, matchedRoute$1, {\n\t\t\t\tparams: decodeParams(matchedRoute$1.params),\n\t\t\t\thash: decode(locationNormalized.hash),\n\t\t\t\tredirectedFrom: void 0,\n\t\t\t\thref: href$1\n\t\t\t});\n\t\t}\n\t\tif (process.env.NODE_ENV !== \"production\" && !isRouteLocation(rawLocation)) {\n\t\t\twarn$1(`router.resolve() was passed an invalid location. This will fail in production.\\n- Location:`, rawLocation);\n\t\t\treturn resolve({});\n\t\t}\n\t\tlet matcherLocation;\n\t\tif (rawLocation.path != null) {\n\t\t\tif (process.env.NODE_ENV !== \"production\" && \"params\" in rawLocation && !(\"name\" in rawLocation) && Object.keys(rawLocation.params).length) warn$1(`Path \"${rawLocation.path}\" was passed with params but they will be ignored. Use a named route alongside params instead.`);\n\t\t\tmatcherLocation = assign({}, rawLocation, { path: parseURL(parseQuery$1, rawLocation.path, currentLocation.path).path });\n\t\t} else {\n\t\t\tconst targetParams = assign({}, rawLocation.params);\n\t\t\tfor (const key in targetParams) if (targetParams[key] == null) delete targetParams[key];\n\t\t\tmatcherLocation = assign({}, rawLocation, { params: encodeParams(targetParams) });\n\t\t\tcurrentLocation.params = encodeParams(currentLocation.params);\n\t\t}\n\t\tconst matchedRoute = matcher.resolve(matcherLocation, currentLocation);\n\t\tconst hash = rawLocation.hash || \"\";\n\t\tif (process.env.NODE_ENV !== \"production\" && hash && !hash.startsWith(\"#\")) warn$1(`A \\`hash\\` should always start with the character \"#\". Replace \"${hash}\" with \"#${hash}\".`);\n\t\tmatchedRoute.params = normalizeParams(decodeParams(matchedRoute.params));\n\t\tconst fullPath = stringifyURL(stringifyQuery$1, assign({}, rawLocation, {\n\t\t\thash: encodeHash(hash),\n\t\t\tpath: matchedRoute.path\n\t\t}));\n\t\tconst href = routerHistory.createHref(fullPath);\n\t\tif (process.env.NODE_ENV !== \"production\") {\n\t\t\tif (href.startsWith(\"//\")) warn$1(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n\t\t\telse if (!matchedRoute.matched.length) warn$1(`No match found for location with path \"${rawLocation.path != null ? rawLocation.path : rawLocation}\"`);\n\t\t}\n\t\treturn assign({\n\t\t\tfullPath,\n\t\t\thash,\n\t\t\tquery: stringifyQuery$1 === stringifyQuery ? normalizeQuery(rawLocation.query) : rawLocation.query || {}\n\t\t}, matchedRoute, {\n\t\t\tredirectedFrom: void 0,\n\t\t\thref\n\t\t});\n\t}\n\tfunction locationAsObject(to) {\n\t\treturn typeof to === \"string\" ? parseURL(parseQuery$1, to, currentRoute.value.path) : assign({}, to);\n\t}\n\tfunction checkCanceledNavigation(to, from) {\n\t\tif (pendingLocation !== to) return createRouterError(ErrorTypes.NAVIGATION_CANCELLED, {\n\t\t\tfrom,\n\t\t\tto\n\t\t});\n\t}\n\tfunction push(to) {\n\t\treturn pushWithRedirect(to);\n\t}\n\tfunction replace(to) {\n\t\treturn push(assign(locationAsObject(to), { replace: true }));\n\t}\n\tfunction handleRedirectRecord(to, from) {\n\t\tconst lastMatched = to.matched[to.matched.length - 1];\n\t\tif (lastMatched && lastMatched.redirect) {\n\t\t\tconst { redirect } = lastMatched;\n\t\t\tlet newTargetLocation = typeof redirect === \"function\" ? redirect(to, from) : redirect;\n\t\t\tif (typeof newTargetLocation === \"string\") {\n\t\t\t\tnewTargetLocation = newTargetLocation.includes(\"?\") || newTargetLocation.includes(\"#\") ? newTargetLocation = locationAsObject(newTargetLocation) : { path: newTargetLocation };\n\t\t\t\tnewTargetLocation.params = {};\n\t\t\t}\n\t\t\tif (process.env.NODE_ENV !== \"production\" && newTargetLocation.path == null && !(\"name\" in newTargetLocation)) {\n\t\t\t\twarn$1(`Invalid redirect found:\\n${JSON.stringify(newTargetLocation, null, 2)}\\n when navigating to \"${to.fullPath}\". A redirect must contain a name or path. This will break in production.`);\n\t\t\t\tthrow new Error(\"Invalid redirect\");\n\t\t\t}\n\t\t\treturn assign({\n\t\t\t\tquery: to.query,\n\t\t\t\thash: to.hash,\n\t\t\t\tparams: newTargetLocation.path != null ? {} : to.params\n\t\t\t}, newTargetLocation);\n\t\t}\n\t}\n\tfunction pushWithRedirect(to, redirectedFrom) {\n\t\tconst targetLocation = pendingLocation = resolve(to);\n\t\tconst from = currentRoute.value;\n\t\tconst data = to.state;\n\t\tconst force = to.force;\n\t\tconst replace$1 = to.replace === true;\n\t\tconst shouldRedirect = handleRedirectRecord(targetLocation, from);\n\t\tif (shouldRedirect) return pushWithRedirect(assign(locationAsObject(shouldRedirect), {\n\t\t\tstate: typeof shouldRedirect === \"object\" ? assign({}, data, shouldRedirect.state) : data,\n\t\t\tforce,\n\t\t\treplace: replace$1\n\t\t}), redirectedFrom || targetLocation);\n\t\tconst toLocation = targetLocation;\n\t\ttoLocation.redirectedFrom = redirectedFrom;\n\t\tlet failure;\n\t\tif (!force && isSameRouteLocation(stringifyQuery$1, from, targetLocation)) {\n\t\t\tfailure = createRouterError(ErrorTypes.NAVIGATION_DUPLICATED, {\n\t\t\t\tto: toLocation,\n\t\t\t\tfrom\n\t\t\t});\n\t\t\thandleScroll(from, from, true, false);\n\t\t}\n\t\treturn (failure ? Promise.resolve(failure) : navigate(toLocation, from)).catch((error) => isNavigationFailure(error) ? isNavigationFailure(error, ErrorTypes.NAVIGATION_GUARD_REDIRECT) ? error : markAsReady(error) : triggerError(error, toLocation, from)).then((failure$1) => {\n\t\t\tif (failure$1) {\n\t\t\t\tif (isNavigationFailure(failure$1, ErrorTypes.NAVIGATION_GUARD_REDIRECT)) {\n\t\t\t\t\tif (process.env.NODE_ENV !== \"production\" && isSameRouteLocation(stringifyQuery$1, resolve(failure$1.to), toLocation) && redirectedFrom && (redirectedFrom._count = redirectedFrom._count ? redirectedFrom._count + 1 : 1) > 30) {\n\t\t\t\t\t\twarn$1(`Detected a possibly infinite redirection in a navigation guard when going from \"${from.fullPath}\" to \"${toLocation.fullPath}\". Aborting to avoid a Stack Overflow.\\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`);\n\t\t\t\t\t\treturn Promise.reject(/* @__PURE__ */ new Error(\"Infinite redirect in navigation guard\"));\n\t\t\t\t\t}\n\t\t\t\t\treturn pushWithRedirect(assign({ replace: replace$1 }, locationAsObject(failure$1.to), {\n\t\t\t\t\t\tstate: typeof failure$1.to === \"object\" ? assign({}, data, failure$1.to.state) : data,\n\t\t\t\t\t\tforce\n\t\t\t\t\t}), redirectedFrom || toLocation);\n\t\t\t\t}\n\t\t\t} else failure$1 = finalizeNavigation(toLocation, from, true, replace$1, data);\n\t\t\ttriggerAfterEach(toLocation, from, failure$1);\n\t\t\treturn failure$1;\n\t\t});\n\t}\n\t/**\n\t* Helper to reject and skip all navigation guards if a new navigation happened\n\t* @param to\n\t* @param from\n\t*/\n\tfunction checkCanceledNavigationAndReject(to, from) {\n\t\tconst error = checkCanceledNavigation(to, from);\n\t\treturn error ? Promise.reject(error) : Promise.resolve();\n\t}\n\tfunction runWithContext(fn) {\n\t\tconst app = installedApps.values().next().value;\n\t\treturn app && typeof app.runWithContext === \"function\" ? app.runWithContext(fn) : fn();\n\t}\n\tfunction navigate(to, from) {\n\t\tlet guards;\n\t\tconst [leavingRecords, updatingRecords, enteringRecords] = extractChangingRecords(to, from);\n\t\tguards = extractComponentsGuards(leavingRecords.reverse(), \"beforeRouteLeave\", to, from);\n\t\tfor (const record of leavingRecords) record.leaveGuards.forEach((guard) => {\n\t\t\tguards.push(guardToPromiseFn(guard, to, from));\n\t\t});\n\t\tconst canceledNavigationCheck = checkCanceledNavigationAndReject.bind(null, to, from);\n\t\tguards.push(canceledNavigationCheck);\n\t\treturn runGuardQueue(guards).then(() => {\n\t\t\tguards = [];\n\t\t\tfor (const guard of beforeGuards.list()) guards.push(guardToPromiseFn(guard, to, from));\n\t\t\tguards.push(canceledNavigationCheck);\n\t\t\treturn runGuardQueue(guards);\n\t\t}).then(() => {\n\t\t\tguards = extractComponentsGuards(updatingRecords, \"beforeRouteUpdate\", to, from);\n\t\t\tfor (const record of updatingRecords) record.updateGuards.forEach((guard) => {\n\t\t\t\tguards.push(guardToPromiseFn(guard, to, from));\n\t\t\t});\n\t\t\tguards.push(canceledNavigationCheck);\n\t\t\treturn runGuardQueue(guards);\n\t\t}).then(() => {\n\t\t\tguards = [];\n\t\t\tfor (const record of enteringRecords) if (record.beforeEnter) if (isArray(record.beforeEnter)) for (const beforeEnter of record.beforeEnter) guards.push(guardToPromiseFn(beforeEnter, to, from));\n\t\t\telse guards.push(guardToPromiseFn(record.beforeEnter, to, from));\n\t\t\tguards.push(canceledNavigationCheck);\n\t\t\treturn runGuardQueue(guards);\n\t\t}).then(() => {\n\t\t\tto.matched.forEach((record) => record.enterCallbacks = {});\n\t\t\tguards = extractComponentsGuards(enteringRecords, \"beforeRouteEnter\", to, from, runWithContext);\n\t\t\tguards.push(canceledNavigationCheck);\n\t\t\treturn runGuardQueue(guards);\n\t\t}).then(() => {\n\t\t\tguards = [];\n\t\t\tfor (const guard of beforeResolveGuards.list()) guards.push(guardToPromiseFn(guard, to, from));\n\t\t\tguards.push(canceledNavigationCheck);\n\t\t\treturn runGuardQueue(guards);\n\t\t}).catch((err) => isNavigationFailure(err, ErrorTypes.NAVIGATION_CANCELLED) ? err : Promise.reject(err));\n\t}\n\tfunction triggerAfterEach(to, from, failure) {\n\t\tafterGuards.list().forEach((guard) => runWithContext(() => guard(to, from, failure)));\n\t}\n\t/**\n\t* - Cleans up any navigation guards\n\t* - Changes the url if necessary\n\t* - Calls the scrollBehavior\n\t*/\n\tfunction finalizeNavigation(toLocation, from, isPush, replace$1, data) {\n\t\tconst error = checkCanceledNavigation(toLocation, from);\n\t\tif (error) return error;\n\t\tconst isFirstNavigation = from === START_LOCATION_NORMALIZED;\n\t\tconst state = !isBrowser ? {} : history.state;\n\t\tif (isPush) if (replace$1 || isFirstNavigation) routerHistory.replace(toLocation.fullPath, assign({ scroll: isFirstNavigation && state && state.scroll }, data));\n\t\telse routerHistory.push(toLocation.fullPath, data);\n\t\tcurrentRoute.value = toLocation;\n\t\thandleScroll(toLocation, from, isPush, isFirstNavigation);\n\t\tmarkAsReady();\n\t}\n\tlet removeHistoryListener;\n\tfunction setupListeners() {\n\t\tif (removeHistoryListener) return;\n\t\tremoveHistoryListener = routerHistory.listen((to, _from, info) => {\n\t\t\tif (!router.listening) return;\n\t\t\tconst toLocation = resolve(to);\n\t\t\tconst shouldRedirect = handleRedirectRecord(toLocation, router.currentRoute.value);\n\t\t\tif (shouldRedirect) {\n\t\t\t\tpushWithRedirect(assign(shouldRedirect, {\n\t\t\t\t\treplace: true,\n\t\t\t\t\tforce: true\n\t\t\t\t}), toLocation).catch(noop);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpendingLocation = toLocation;\n\t\t\tconst from = currentRoute.value;\n\t\t\tif (isBrowser) saveScrollPosition(getScrollKey(from.fullPath, info.delta), computeScrollPosition());\n\t\t\tnavigate(toLocation, from).catch((error) => {\n\t\t\t\tif (isNavigationFailure(error, ErrorTypes.NAVIGATION_ABORTED | ErrorTypes.NAVIGATION_CANCELLED)) return error;\n\t\t\t\tif (isNavigationFailure(error, ErrorTypes.NAVIGATION_GUARD_REDIRECT)) {\n\t\t\t\t\tpushWithRedirect(assign(locationAsObject(error.to), { force: true }), toLocation).then((failure) => {\n\t\t\t\t\t\tif (isNavigationFailure(failure, ErrorTypes.NAVIGATION_ABORTED | ErrorTypes.NAVIGATION_DUPLICATED) && !info.delta && info.type === NavigationType.pop) routerHistory.go(-1, false);\n\t\t\t\t\t}).catch(noop);\n\t\t\t\t\treturn Promise.reject();\n\t\t\t\t}\n\t\t\t\tif (info.delta) routerHistory.go(-info.delta, false);\n\t\t\t\treturn triggerError(error, toLocation, from);\n\t\t\t}).then((failure) => {\n\t\t\t\tfailure = failure || finalizeNavigation(toLocation, from, false);\n\t\t\t\tif (failure) {\n\t\t\t\t\tif (info.delta && !isNavigationFailure(failure, ErrorTypes.NAVIGATION_CANCELLED)) routerHistory.go(-info.delta, false);\n\t\t\t\t\telse if (info.type === NavigationType.pop && isNavigationFailure(failure, ErrorTypes.NAVIGATION_ABORTED | ErrorTypes.NAVIGATION_DUPLICATED)) routerHistory.go(-1, false);\n\t\t\t\t}\n\t\t\t\ttriggerAfterEach(toLocation, from, failure);\n\t\t\t}).catch(noop);\n\t\t});\n\t}\n\tlet readyHandlers = useCallbacks();\n\tlet errorListeners = useCallbacks();\n\tlet ready;\n\t/**\n\t* Trigger errorListeners added via onError and throws the error as well\n\t*\n\t* @param error - error to throw\n\t* @param to - location we were navigating to when the error happened\n\t* @param from - location we were navigating from when the error happened\n\t* @returns the error as a rejected promise\n\t*/\n\tfunction triggerError(error, to, from) {\n\t\tmarkAsReady(error);\n\t\tconst list = errorListeners.list();\n\t\tif (list.length) list.forEach((handler) => handler(error, to, from));\n\t\telse {\n\t\t\tif (process.env.NODE_ENV !== \"production\") warn$1(\"uncaught error during route navigation:\");\n\t\t\tconsole.error(error);\n\t\t}\n\t\treturn Promise.reject(error);\n\t}\n\tfunction isReady() {\n\t\tif (ready && currentRoute.value !== START_LOCATION_NORMALIZED) return Promise.resolve();\n\t\treturn new Promise((resolve$1, reject) => {\n\t\t\treadyHandlers.add([resolve$1, reject]);\n\t\t});\n\t}\n\tfunction markAsReady(err) {\n\t\tif (!ready) {\n\t\t\tready = !err;\n\t\t\tsetupListeners();\n\t\t\treadyHandlers.list().forEach(([resolve$1, reject]) => err ? reject(err) : resolve$1());\n\t\t\treadyHandlers.reset();\n\t\t}\n\t\treturn err;\n\t}\n\tfunction handleScroll(to, from, isPush, isFirstNavigation) {\n\t\tconst { scrollBehavior } = options;\n\t\tif (!isBrowser || !scrollBehavior) return Promise.resolve();\n\t\tconst scrollPosition = !isPush && getSavedScrollPosition(getScrollKey(to.fullPath, 0)) || (isFirstNavigation || !isPush) && history.state && history.state.scroll || null;\n\t\treturn nextTick().then(() => scrollBehavior(to, from, scrollPosition)).then((position) => position && scrollToPosition(position)).catch((err) => triggerError(err, to, from));\n\t}\n\tconst go = (delta) => routerHistory.go(delta);\n\tlet started;\n\tconst installedApps = /* @__PURE__ */ new Set();\n\tconst router = {\n\t\tcurrentRoute,\n\t\tlistening: true,\n\t\taddRoute,\n\t\tremoveRoute,\n\t\tclearRoutes: matcher.clearRoutes,\n\t\thasRoute,\n\t\tgetRoutes,\n\t\tresolve,\n\t\toptions,\n\t\tpush,\n\t\treplace,\n\t\tgo,\n\t\tback: () => go(-1),\n\t\tforward: () => go(1),\n\t\tbeforeEach: beforeGuards.add,\n\t\tbeforeResolve: beforeResolveGuards.add,\n\t\tafterEach: afterGuards.add,\n\t\tonError: errorListeners.add,\n\t\tisReady,\n\t\tinstall(app) {\n\t\t\tapp.component(\"RouterLink\", RouterLink);\n\t\t\tapp.component(\"RouterView\", RouterView);\n\t\t\tapp.config.globalProperties.$router = router;\n\t\t\tObject.defineProperty(app.config.globalProperties, \"$route\", {\n\t\t\t\tenumerable: true,\n\t\t\t\tget: () => unref(currentRoute)\n\t\t\t});\n\t\t\tif (isBrowser && !started && currentRoute.value === START_LOCATION_NORMALIZED) {\n\t\t\t\tstarted = true;\n\t\t\t\tpush(routerHistory.location).catch((err) => {\n\t\t\t\t\tif (process.env.NODE_ENV !== \"production\") warn$1(\"Unexpected error when starting the router:\", err);\n\t\t\t\t});\n\t\t\t}\n\t\t\tconst reactiveRoute = {};\n\t\t\tfor (const key in START_LOCATION_NORMALIZED) Object.defineProperty(reactiveRoute, key, {\n\t\t\t\tget: () => currentRoute.value[key],\n\t\t\t\tenumerable: true\n\t\t\t});\n\t\t\tapp.provide(routerKey, router);\n\t\t\tapp.provide(routeLocationKey, shallowReactive(reactiveRoute));\n\t\t\tapp.provide(routerViewLocationKey, currentRoute);\n\t\t\tconst unmountApp = app.unmount;\n\t\t\tinstalledApps.add(app);\n\t\t\tapp.unmount = function() {\n\t\t\t\tinstalledApps.delete(app);\n\t\t\t\tif (installedApps.size < 1) {\n\t\t\t\t\tpendingLocation = START_LOCATION_NORMALIZED;\n\t\t\t\t\tremoveHistoryListener && removeHistoryListener();\n\t\t\t\t\tremoveHistoryListener = null;\n\t\t\t\t\tcurrentRoute.value = START_LOCATION_NORMALIZED;\n\t\t\t\t\tstarted = false;\n\t\t\t\t\tready = false;\n\t\t\t\t}\n\t\t\t\tunmountApp();\n\t\t\t};\n\t\t\tif ((process.env.NODE_ENV !== \"production\" || __VUE_PROD_DEVTOOLS__) && isBrowser) addDevtools(app, router, matcher);\n\t\t}\n\t};\n\tfunction runGuardQueue(guards) {\n\t\treturn guards.reduce((promise, guard) => promise.then(() => runWithContext(guard)), Promise.resolve());\n\t}\n\treturn router;\n}\n\n//#endregion\n//#region src/useApi.ts\n/**\n* Returns the router instance. Equivalent to using `$router` inside\n* templates.\n*/\nfunction useRouter() {\n\treturn inject(routerKey);\n}\n/**\n* Returns the current route location. Equivalent to using `$route` inside\n* templates.\n*/\nfunction useRoute(_name) {\n\treturn inject(routeLocationKey);\n}\n\n//#endregion\nexport { NavigationFailureType, RouterLink, RouterView, START_LOCATION_NORMALIZED as START_LOCATION, createMemoryHistory, createRouter, createRouterMatcher, createWebHashHistory, createWebHistory, isNavigationFailure, loadRouteLocation, matchedRouteKey, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, routeLocationKey, routerKey, routerViewLocationKey, stringifyQuery, useLink, useRoute, useRouter, viewDepthKey };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,YAAY,OAAO,aAAa;AAkBtC,SAAS,iBAAiB,WAAW;AACpC,SAAO,OAAO,cAAc,YAAY,iBAAiB,aAAa,WAAW,aAAa,eAAe;AAC9G;AACA,SAAS,WAAW,KAAK;AACxB,SAAO,IAAI,cAAc,IAAI,OAAO,WAAW,MAAM,YAAY,IAAI,WAAW,iBAAiB,IAAI,OAAO;AAC7G;AACA,IAAM,SAAS,OAAO;AACtB,SAAS,cAAc,IAAI,QAAQ;AAClC,QAAM,YAAY,CAAC;AACnB,aAAW,OAAO,QAAQ;AACzB,UAAM,QAAQ,OAAO,GAAG;AACxB,cAAU,GAAG,IAAI,QAAQ,KAAK,IAAI,MAAM,IAAI,EAAE,IAAI,GAAG,KAAK;AAAA,EAC3D;AACA,SAAO;AACR;AACA,IAAM,OAAO,MAAM;AAAC;AAKpB,IAAM,UAAU,MAAM;AACtB,SAAS,aAAa,UAAU,gBAAgB;AAC/C,QAAM,UAAU,CAAC;AACjB,aAAW,OAAO,SAAU,SAAQ,GAAG,IAAI,OAAO,iBAAiB,eAAe,GAAG,IAAI,SAAS,GAAG;AACrG,SAAO;AACR;AAIA,SAAS,OAAO,KAAK;AACpB,QAAM,OAAO,MAAM,KAAK,SAAS,EAAE,MAAM,CAAC;AAC1C,UAAQ,KAAK,MAAM,SAAS,CAAC,wBAAwB,GAAG,EAAE,OAAO,IAAI,CAAC;AACvE;AAqBA,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,UAAU;AAehB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AASrB,SAAS,aAAa,MAAM;AAC3B,SAAO,QAAQ,OAAO,KAAK,UAAU,KAAK,IAAI,EAAE,QAAQ,aAAa,GAAG,EAAE,QAAQ,qBAAqB,GAAG,EAAE,QAAQ,sBAAsB,GAAG;AAC9I;AAOA,SAAS,WAAW,MAAM;AACzB,SAAO,aAAa,IAAI,EAAE,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,oBAAoB,GAAG,EAAE,QAAQ,cAAc,GAAG;AACrH;AAQA,SAAS,iBAAiB,MAAM;AAC/B,SAAO,aAAa,IAAI,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,cAAc,GAAG,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,cAAc,KAAK,EAAE,QAAQ,iBAAiB,GAAG,EAAE,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,oBAAoB,GAAG,EAAE,QAAQ,cAAc,GAAG;AAC3P;AAMA,SAAS,eAAe,MAAM;AAC7B,SAAO,iBAAiB,IAAI,EAAE,QAAQ,UAAU,KAAK;AACtD;AAOA,SAAS,WAAW,MAAM;AACzB,SAAO,aAAa,IAAI,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,OAAO,KAAK;AACvE;AAUA,SAAS,YAAY,MAAM;AAC1B,SAAO,WAAW,IAAI,EAAE,QAAQ,UAAU,KAAK;AAChD;AACA,SAAS,OAAO,MAAM;AACrB,MAAI,QAAQ,KAAM,QAAO;AACzB,MAAI;AACH,WAAO,mBAAmB,KAAK,IAAI;AAAA,EACpC,SAAS,KAAK;AACb,IAAyC,OAAO,mBAAmB,IAAI,yBAAyB;AAAA,EACjG;AACA,SAAO,KAAK;AACb;AAIA,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB,CAAC,SAAS,KAAK,QAAQ,mBAAmB,EAAE;AAUxE,SAAS,SAAS,cAAcA,WAAU,kBAAkB,KAAK;AAChE,MAAI,MAAM,QAAQ,CAAC,GAAG,eAAe,IAAI,OAAO;AAChD,QAAM,UAAUA,UAAS,QAAQ,GAAG;AACpC,MAAI,YAAYA,UAAS,QAAQ,GAAG;AACpC,cAAY,WAAW,KAAK,YAAY,UAAU,KAAK;AACvD,MAAI,aAAa,GAAG;AACnB,WAAOA,UAAS,MAAM,GAAG,SAAS;AAClC,mBAAeA,UAAS,MAAM,WAAW,UAAU,IAAI,UAAUA,UAAS,MAAM;AAChF,YAAQ,aAAa,aAAa,MAAM,CAAC,CAAC;AAAA,EAC3C;AACA,MAAI,WAAW,GAAG;AACjB,WAAO,QAAQA,UAAS,MAAM,GAAG,OAAO;AACxC,WAAOA,UAAS,MAAM,SAASA,UAAS,MAAM;AAAA,EAC/C;AACA,SAAO,oBAAoB,QAAQ,OAAO,OAAOA,WAAU,eAAe;AAC1E,SAAO;AAAA,IACN,UAAU,OAAO,eAAe;AAAA,IAChC;AAAA,IACA;AAAA,IACA,MAAM,OAAO,IAAI;AAAA,EAClB;AACD;AAWA,SAAS,aAAa,kBAAkBC,WAAU;AACjD,QAAM,QAAQA,UAAS,QAAQ,iBAAiBA,UAAS,KAAK,IAAI;AAClE,SAAOA,UAAS,QAAQ,SAAS,OAAO,SAASA,UAAS,QAAQ;AACnE;AAOA,SAAS,UAAU,UAAU,MAAM;AAClC,MAAI,CAAC,QAAQ,CAAC,SAAS,YAAY,EAAE,WAAW,KAAK,YAAY,CAAC,EAAG,QAAO;AAC5E,SAAO,SAAS,MAAM,KAAK,MAAM,KAAK;AACvC;AAUA,SAAS,oBAAoB,kBAAkB,GAAG,GAAG;AACpD,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,SAAO,aAAa,MAAM,eAAe,cAAc,kBAAkB,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,UAAU,CAAC,KAAK,0BAA0B,EAAE,QAAQ,EAAE,MAAM,KAAK,iBAAiB,EAAE,KAAK,MAAM,iBAAiB,EAAE,KAAK,KAAK,EAAE,SAAS,EAAE;AACpP;AAQA,SAAS,kBAAkB,GAAG,GAAG;AAChC,UAAQ,EAAE,WAAW,QAAQ,EAAE,WAAW;AAC3C;AACA,SAAS,0BAA0B,GAAG,GAAG;AACxC,MAAI,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAC5D,aAAW,OAAO,EAAG,KAAI,CAAC,+BAA+B,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;AACjF,SAAO;AACR;AACA,SAAS,+BAA+B,GAAG,GAAG;AAC7C,SAAO,QAAQ,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI,MAAM;AAC5F;AAQA,SAAS,kBAAkB,GAAG,GAAG;AAChC,SAAO,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,MAAM,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM;AACjH;AAOA,SAAS,oBAAoB,IAAI,MAAM;AACtC,MAAI,GAAG,WAAW,GAAG,EAAG,QAAO;AAC/B,MAA6C,CAAC,KAAK,WAAW,GAAG,GAAG;AACnE,WAAO,mFAAmF,EAAE,WAAW,IAAI,4BAA4B,IAAI,IAAI;AAC/I,WAAO;AAAA,EACR;AACA,MAAI,CAAC,GAAI,QAAO;AAChB,QAAM,eAAe,KAAK,MAAM,GAAG;AACnC,QAAM,aAAa,GAAG,MAAM,GAAG;AAC/B,QAAM,gBAAgB,WAAW,WAAW,SAAS,CAAC;AACtD,MAAI,kBAAkB,QAAQ,kBAAkB,IAAK,YAAW,KAAK,EAAE;AACvE,MAAI,WAAW,aAAa,SAAS;AACrC,MAAI;AACJ,MAAI;AACJ,OAAK,aAAa,GAAG,aAAa,WAAW,QAAQ,cAAc;AAClE,cAAU,WAAW,UAAU;AAC/B,QAAI,YAAY,IAAK;AACrB,QAAI,YAAY,MAAM;AACrB,UAAI,WAAW,EAAG;AAAA,IACnB,MAAO;AAAA,EACR;AACA,SAAO,aAAa,MAAM,GAAG,QAAQ,EAAE,KAAK,GAAG,IAAI,MAAM,WAAW,MAAM,UAAU,EAAE,KAAK,GAAG;AAC/F;AAgBA,IAAM,4BAA4B;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ,CAAC;AAAA,EACT,OAAO,CAAC;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS,CAAC;AAAA,EACV,MAAM,CAAC;AAAA,EACP,gBAAgB;AACjB;AAIA,IAAI,iBAAiC,SAAS,kBAAkB;AAC/D,mBAAiB,KAAK,IAAI;AAC1B,mBAAiB,MAAM,IAAI;AAC3B,SAAO;AACR,EAAE,CAAC,CAAC;AACJ,IAAI,sBAAsC,SAAS,uBAAuB;AACzE,wBAAsB,MAAM,IAAI;AAChC,wBAAsB,SAAS,IAAI;AACnC,wBAAsB,SAAS,IAAI;AACnC,SAAO;AACR,EAAE,CAAC,CAAC;AAIJ,IAAM,QAAQ;AAOd,SAAS,cAAc,MAAM;AAC5B,MAAI,CAAC,KAAM,KAAI,WAAW;AACzB,UAAM,SAAS,SAAS,cAAc,MAAM;AAC5C,WAAO,UAAU,OAAO,aAAa,MAAM,KAAK;AAChD,WAAO,KAAK,QAAQ,mBAAmB,EAAE;AAAA,EAC1C,MAAO,QAAO;AACd,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,IAAK,QAAO,MAAM;AACrD,SAAO,oBAAoB,IAAI;AAChC;AACA,IAAM,iBAAiB;AACvB,SAAS,WAAW,MAAMA,WAAU;AACnC,SAAO,KAAK,QAAQ,gBAAgB,GAAG,IAAIA;AAC5C;AAIA,SAAS,mBAAmB,IAAI,QAAQ;AACvC,QAAM,UAAU,SAAS,gBAAgB,sBAAsB;AAC/D,QAAM,SAAS,GAAG,sBAAsB;AACxC,SAAO;AAAA,IACN,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO,OAAO,QAAQ,QAAQ,OAAO,QAAQ;AAAA,IACnD,KAAK,OAAO,MAAM,QAAQ,OAAO,OAAO,OAAO;AAAA,EAChD;AACD;AACA,IAAM,wBAAwB,OAAO;AAAA,EACpC,MAAM,OAAO;AAAA,EACb,KAAK,OAAO;AACb;AACA,SAAS,iBAAiB,UAAU;AACnC,MAAI;AACJ,MAAI,QAAQ,UAAU;AACrB,UAAM,aAAa,SAAS;AAC5B,UAAM,eAAe,OAAO,eAAe,YAAY,WAAW,WAAW,GAAG;AAsBhF,QAA6C,OAAO,SAAS,OAAO,UAAU;AAC7E,UAAI,CAAC,gBAAgB,CAAC,SAAS,eAAe,SAAS,GAAG,MAAM,CAAC,CAAC,EAAG,KAAI;AACxE,cAAM,UAAU,SAAS,cAAc,SAAS,EAAE;AAClD,YAAI,gBAAgB,SAAS;AAC5B,iBAAO,iBAAiB,SAAS,EAAE,sDAAsD,SAAS,EAAE,iCAAiC;AACrI;AAAA,QACD;AAAA,MACD,SAAS,KAAK;AACb,eAAO,iBAAiB,SAAS,EAAE,4QAA4Q;AAC/S;AAAA,MACD;AAAA,IACD;AACA,UAAM,KAAK,OAAO,eAAe,WAAW,eAAe,SAAS,eAAe,WAAW,MAAM,CAAC,CAAC,IAAI,SAAS,cAAc,UAAU,IAAI;AAC/I,QAAI,CAAC,IAAI;AACR,MAAyC,OAAO,yCAAyC,SAAS,EAAE,+BAA+B;AACnI;AAAA,IACD;AACA,sBAAkB,mBAAmB,IAAI,QAAQ;AAAA,EAClD,MAAO,mBAAkB;AACzB,MAAI,oBAAoB,SAAS,gBAAgB,MAAO,QAAO,SAAS,eAAe;AAAA,MAClF,QAAO,SAAS,gBAAgB,QAAQ,OAAO,gBAAgB,OAAO,OAAO,SAAS,gBAAgB,OAAO,OAAO,gBAAgB,MAAM,OAAO,OAAO;AAC9J;AACA,SAAS,aAAa,MAAM,OAAO;AAClC,UAAQ,QAAQ,QAAQ,QAAQ,MAAM,WAAW,QAAQ,MAAM;AAChE;AACA,IAAM,kBAAkC,oBAAI,IAAI;AAChD,SAAS,mBAAmB,KAAK,gBAAgB;AAChD,kBAAgB,IAAI,KAAK,cAAc;AACxC;AACA,SAAS,uBAAuB,KAAK;AACpC,QAAM,SAAS,gBAAgB,IAAI,GAAG;AACtC,kBAAgB,OAAO,GAAG;AAC1B,SAAO;AACR;AAQA,SAAS,gBAAgB,OAAO;AAC/B,SAAO,OAAO,UAAU,YAAY,SAAS,OAAO,UAAU;AAC/D;AACA,SAAS,YAAY,MAAM;AAC1B,SAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AACpD;AAUA,IAAI,aAA6B,SAAS,cAAc;AACvD,eAAa,aAAa,mBAAmB,IAAI,CAAC,IAAI;AACtD,eAAa,aAAa,2BAA2B,IAAI,CAAC,IAAI;AAC9D,eAAa,aAAa,oBAAoB,IAAI,CAAC,IAAI;AACvD,eAAa,aAAa,sBAAsB,IAAI,CAAC,IAAI;AACzD,eAAa,aAAa,uBAAuB,IAAI,EAAE,IAAI;AAC3D,SAAO;AACR,EAAE,CAAC,CAAC;AACJ,IAAM,0BAA0B,OAAO,OAAwC,uBAAuB,EAAE;AAKxG,IAAI,wBAAwC,SAAS,yBAAyB;AAK7E,0BAAwB,wBAAwB,SAAS,IAAI,CAAC,IAAI;AAKlE,0BAAwB,wBAAwB,WAAW,IAAI,CAAC,IAAI;AAKpE,0BAAwB,wBAAwB,YAAY,IAAI,EAAE,IAAI;AACtE,SAAO;AACR,EAAE,CAAC,CAAC;AACJ,IAAM,oBAAoB;AAAA,EACzB,CAAC,WAAW,iBAAiB,EAAE,EAAE,UAAAA,WAAU,gBAAgB,GAAG;AAC7D,WAAO;AAAA,GAAkB,KAAK,UAAUA,SAAQ,CAAC,GAAG,kBAAkB,uBAAuB,KAAK,UAAU,eAAe,IAAI,EAAE;AAAA,EAClI;AAAA,EACA,CAAC,WAAW,yBAAyB,EAAE,EAAE,MAAM,GAAG,GAAG;AACpD,WAAO,oBAAoB,KAAK,QAAQ,SAAS,eAAe,EAAE,CAAC;AAAA,EACpE;AAAA,EACA,CAAC,WAAW,kBAAkB,EAAE,EAAE,MAAM,GAAG,GAAG;AAC7C,WAAO,4BAA4B,KAAK,QAAQ,SAAS,GAAG,QAAQ;AAAA,EACrE;AAAA,EACA,CAAC,WAAW,oBAAoB,EAAE,EAAE,MAAM,GAAG,GAAG;AAC/C,WAAO,8BAA8B,KAAK,QAAQ,SAAS,GAAG,QAAQ;AAAA,EACvE;AAAA,EACA,CAAC,WAAW,qBAAqB,EAAE,EAAE,MAAM,GAAG,GAAG;AAChD,WAAO,sDAAsD,KAAK,QAAQ;AAAA,EAC3E;AACD;AAOA,SAAS,kBAAkB,MAAM,QAAQ;AACxC,MAAI,KAAgD,QAAO,OAAO,IAAI,MAAM,kBAAkB,IAAI,EAAE,MAAM,CAAC,GAAG;AAAA,IAC7G;AAAA,IACA,CAAC,uBAAuB,GAAG;AAAA,EAC5B,GAAG,MAAM;AAAA,MACJ,QAAO,OAAuB,IAAI,MAAM,GAAG;AAAA,IAC/C;AAAA,IACA,CAAC,uBAAuB,GAAG;AAAA,EAC5B,GAAG,MAAM;AACV;AACA,SAAS,oBAAoB,OAAO,MAAM;AACzC,SAAO,iBAAiB,SAAS,2BAA2B,UAAU,QAAQ,QAAQ,CAAC,EAAE,MAAM,OAAO;AACvG;AACA,IAAM,kBAAkB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AACD;AACA,SAAS,eAAe,IAAI;AAC3B,MAAI,OAAO,OAAO,SAAU,QAAO;AACnC,MAAI,GAAG,QAAQ,KAAM,QAAO,GAAG;AAC/B,QAAMA,YAAW,CAAC;AAClB,aAAW,OAAO,gBAAiB,KAAI,OAAO,GAAI,CAAAA,UAAS,GAAG,IAAI,GAAG,GAAG;AACxE,SAAO,KAAK,UAAUA,WAAU,MAAM,CAAC;AACxC;AAaA,SAAS,WAAW,QAAQ;AAC3B,QAAM,QAAQ,CAAC;AACf,MAAI,WAAW,MAAM,WAAW,IAAK,QAAO;AAC5C,QAAM,gBAAgB,OAAO,CAAC,MAAM,MAAM,OAAO,MAAM,CAAC,IAAI,QAAQ,MAAM,GAAG;AAC7E,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC7C,UAAM,cAAc,aAAa,CAAC,EAAE,QAAQ,SAAS,GAAG;AACxD,UAAM,QAAQ,YAAY,QAAQ,GAAG;AACrC,UAAM,MAAM,OAAO,QAAQ,IAAI,cAAc,YAAY,MAAM,GAAG,KAAK,CAAC;AACxE,UAAM,QAAQ,QAAQ,IAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,CAAC,CAAC;AACpE,QAAI,OAAO,OAAO;AACjB,UAAI,eAAe,MAAM,GAAG;AAC5B,UAAI,CAAC,QAAQ,YAAY,EAAG,gBAAe,MAAM,GAAG,IAAI,CAAC,YAAY;AACrE,mBAAa,KAAK,KAAK;AAAA,IACxB,MAAO,OAAM,GAAG,IAAI;AAAA,EACrB;AACA,SAAO;AACR;AAUA,SAAS,eAAe,OAAO;AAC9B,MAAI,SAAS;AACb,WAAS,OAAO,OAAO;AACtB,UAAM,QAAQ,MAAM,GAAG;AACvB,UAAM,eAAe,GAAG;AACxB,QAAI,SAAS,MAAM;AAClB,UAAI,UAAU,OAAQ,YAAW,OAAO,SAAS,MAAM,MAAM;AAC7D;AAAA,IACD;AACA,KAAC,QAAQ,KAAK,IAAI,MAAM,IAAI,CAAC,MAAM,KAAK,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,iBAAiB,KAAK,CAAC,GAAG,QAAQ,CAAC,YAAY;AACvH,UAAI,YAAY,QAAQ;AACvB,mBAAW,OAAO,SAAS,MAAM,MAAM;AACvC,YAAI,WAAW,KAAM,WAAU,MAAM;AAAA,MACtC;AAAA,IACD,CAAC;AAAA,EACF;AACA,SAAO;AACR;AASA,SAAS,eAAe,OAAO;AAC9B,QAAM,kBAAkB,CAAC;AACzB,aAAW,OAAO,OAAO;AACxB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,OAAQ,iBAAgB,GAAG,IAAI,QAAQ,KAAK,IAAI,MAAM,IAAI,CAAC,MAAM,KAAK,OAAO,OAAO,KAAK,CAAC,IAAI,SAAS,OAAO,QAAQ,KAAK;AAAA,EAC1I;AACA,SAAO;AACR;AAWA,IAAM,kBAAkB,OAAO,OAAwC,iCAAiC,EAAE;AAO1G,IAAM,eAAe,OAAO,OAAwC,sBAAsB,EAAE;AAO5F,IAAM,YAAY,OAAO,OAAwC,WAAW,EAAE;AAO9E,IAAM,mBAAmB,OAAO,OAAwC,mBAAmB,EAAE;AAO7F,IAAM,wBAAwB,OAAO,OAAwC,yBAAyB,EAAE;AAOxG,SAAS,eAAe;AACvB,MAAI,WAAW,CAAC;AAChB,WAAS,IAAI,SAAS;AACrB,aAAS,KAAK,OAAO;AACrB,WAAO,MAAM;AACZ,YAAM,IAAI,SAAS,QAAQ,OAAO;AAClC,UAAI,IAAI,GAAI,UAAS,OAAO,GAAG,CAAC;AAAA,IACjC;AAAA,EACD;AACA,WAAS,QAAQ;AAChB,eAAW,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACN;AAAA,IACA,MAAM,MAAM,SAAS,MAAM;AAAA,IAC3B;AAAA,EACD;AACD;AAIA,SAAS,cAAc,QAAQ,MAAM,OAAO;AAC3C,QAAM,iBAAiB,MAAM;AAC5B,WAAO,IAAI,EAAE,OAAO,KAAK;AAAA,EAC1B;AACA,cAAY,cAAc;AAC1B,gBAAc,cAAc;AAC5B,cAAY,MAAM;AACjB,WAAO,IAAI,EAAE,IAAI,KAAK;AAAA,EACvB,CAAC;AACD,SAAO,IAAI,EAAE,IAAI,KAAK;AACvB;AAQA,SAAS,mBAAmB,YAAY;AACvC,MAA6C,CAAC,mBAAmB,GAAG;AACnE,WAAO,wGAAwG;AAC/G;AAAA,EACD;AACA,QAAM,eAAe,OAAO,iBAAiB,CAAC,CAAC,EAAE;AACjD,MAAI,CAAC,cAAc;AAClB,IAAyC,OAAO,0LAA0L;AAC1O;AAAA,EACD;AACA,gBAAc,cAAc,eAAe,UAAU;AACtD;AAQA,SAAS,oBAAoB,aAAa;AACzC,MAA6C,CAAC,mBAAmB,GAAG;AACnE,WAAO,yGAAyG;AAChH;AAAA,EACD;AACA,QAAM,eAAe,OAAO,iBAAiB,CAAC,CAAC,EAAE;AACjD,MAAI,CAAC,cAAc;AAClB,IAAyC,OAAO,2LAA2L;AAC3O;AAAA,EACD;AACA,gBAAc,cAAc,gBAAgB,WAAW;AACxD;AACA,SAAS,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,iBAAiB,CAAC,OAAO,GAAG,GAAG;AACvF,QAAM,qBAAqB,WAAW,OAAO,eAAe,IAAI,IAAI,OAAO,eAAe,IAAI,KAAK,CAAC;AACpG,SAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,UAAM,OAAO,CAAC,UAAU;AACvB,UAAI,UAAU,MAAO,QAAO,kBAAkB,WAAW,oBAAoB;AAAA,QAC5E;AAAA,QACA;AAAA,MACD,CAAC,CAAC;AAAA,eACO,iBAAiB,MAAO,QAAO,KAAK;AAAA,eACpC,gBAAgB,KAAK,EAAG,QAAO,kBAAkB,WAAW,2BAA2B;AAAA,QAC/F,MAAM;AAAA,QACN,IAAI;AAAA,MACL,CAAC,CAAC;AAAA,WACG;AACJ,YAAI,sBAAsB,OAAO,eAAe,IAAI,MAAM,sBAAsB,OAAO,UAAU,WAAY,oBAAmB,KAAK,KAAK;AAC1I,gBAAQ;AAAA,MACT;AAAA,IACD;AACA,UAAM,cAAc,eAAe,MAAM,MAAM,KAAK,UAAU,OAAO,UAAU,IAAI,GAAG,IAAI,MAAM,OAAwC,oBAAoB,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC;AACnL,QAAI,YAAY,QAAQ,QAAQ,WAAW;AAC3C,QAAI,MAAM,SAAS,EAAG,aAAY,UAAU,KAAK,IAAI;AACrD,QAA6C,MAAM,SAAS,GAAG;AAC9D,YAAM,UAAU,kDAAkD,MAAM,OAAO,MAAO,MAAM,OAAO,MAAO,EAAE;AAAA,EAAM,MAAM,SAAS,CAAC;AAAA;AAClI,UAAI,OAAO,gBAAgB,YAAY,UAAU,YAAa,aAAY,UAAU,KAAK,CAAC,kBAAkB;AAC3G,YAAI,CAAC,KAAK,SAAS;AAClB,iBAAO,OAAO;AACd,iBAAO,QAAQ,OAAuB,IAAI,MAAM,0BAA0B,CAAC;AAAA,QAC5E;AACA,eAAO;AAAA,MACR,CAAC;AAAA,eACQ,gBAAgB,QAAQ;AAChC,YAAI,CAAC,KAAK,SAAS;AAClB,iBAAO,OAAO;AACd,iBAAuB,IAAI,MAAM,0BAA0B,CAAC;AAC5D;AAAA,QACD;AAAA,MACD;AAAA,IACD;AACA,cAAU,MAAM,CAAC,QAAQ,OAAO,GAAG,CAAC;AAAA,EACrC,CAAC;AACF;AACA,SAAS,oBAAoB,MAAM,IAAI,MAAM;AAC5C,MAAI,SAAS;AACb,SAAO,WAAW;AACjB,QAAI,aAAa,EAAG,QAAO,0FAA0F,KAAK,QAAQ,SAAS,GAAG,QAAQ,iGAAiG;AACvP,SAAK,UAAU;AACf,QAAI,WAAW,EAAG,MAAK,MAAM,MAAM,SAAS;AAAA,EAC7C;AACD;AACA,SAAS,wBAAwB,SAAS,WAAW,IAAI,MAAM,iBAAiB,CAAC,OAAO,GAAG,GAAG;AAC7F,QAAM,SAAS,CAAC;AAChB,aAAW,UAAU,SAAS;AAC7B,QAA6C,CAAC,OAAO,cAAc,OAAO,YAAY,CAAC,OAAO,SAAS,OAAQ,QAAO,qBAAqB,OAAO,IAAI,8DAA8D;AACpN,eAAW,QAAQ,OAAO,YAAY;AACrC,UAAI,eAAe,OAAO,WAAW,IAAI;AACzC,UAAI,MAAuC;AAC1C,YAAI,CAAC,gBAAgB,OAAO,iBAAiB,YAAY,OAAO,iBAAiB,YAAY;AAC5F,iBAAO,cAAc,IAAI,0BAA0B,OAAO,IAAI,yCAAyC,OAAO,YAAY,CAAC,IAAI;AAC/H,gBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC1C,WAAW,UAAU,cAAc;AAClC,iBAAO,cAAc,IAAI,0BAA0B,OAAO,IAAI,6LAA6L;AAC3P,gBAAM,UAAU;AAChB,yBAAe,MAAM;AAAA,QACtB,WAAW,aAAa,iBAAiB,CAAC,aAAa,qBAAqB;AAC3E,uBAAa,sBAAsB;AACnC,iBAAO,cAAc,IAAI,0BAA0B,OAAO,IAAI,oJAAoJ;AAAA,QACnN;AAAA,MACD;AACA,UAAI,cAAc,sBAAsB,CAAC,OAAO,UAAU,IAAI,EAAG;AACjE,UAAI,iBAAiB,YAAY,GAAG;AACnC,cAAM,SAAS,aAAa,aAAa,cAAc,SAAS;AAChE,iBAAS,OAAO,KAAK,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,cAAc,CAAC;AAAA,MACrF,OAAO;AACN,YAAI,mBAAmB,aAAa;AACpC,YAA6C,EAAE,WAAW,mBAAmB;AAC5E,iBAAO,cAAc,IAAI,0BAA0B,OAAO,IAAI,4LAA4L;AAC1P,6BAAmB,QAAQ,QAAQ,gBAAgB;AAAA,QACpD;AACA,eAAO,KAAK,MAAM,iBAAiB,KAAK,CAAC,aAAa;AACrD,cAAI,CAAC,SAAU,OAAM,IAAI,MAAM,+BAA+B,IAAI,SAAS,OAAO,IAAI,GAAG;AACzF,gBAAM,oBAAoB,WAAW,QAAQ,IAAI,SAAS,UAAU;AACpE,iBAAO,KAAK,IAAI,IAAI;AACpB,iBAAO,WAAW,IAAI,IAAI;AAC1B,gBAAM,SAAS,kBAAkB,aAAa,mBAAmB,SAAS;AAC1E,iBAAO,SAAS,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,cAAc,EAAE;AAAA,QACjF,CAAC,CAAC;AAAA,MACH;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;AAMA,SAAS,kBAAkB,OAAO;AACjC,SAAO,MAAM,QAAQ,MAAM,CAAC,WAAW,OAAO,QAAQ,IAAI,QAAQ,OAAuB,IAAI,MAAM,qCAAqC,CAAC,IAAI,QAAQ,IAAI,MAAM,QAAQ,IAAI,CAAC,WAAW,OAAO,cAAc,QAAQ,IAAI,OAAO,KAAK,OAAO,UAAU,EAAE,OAAO,CAAC,UAAU,SAAS;AACjR,UAAM,eAAe,OAAO,WAAW,IAAI;AAC3C,QAAI,OAAO,iBAAiB,cAAc,EAAE,iBAAiB,cAAe,UAAS,KAAK,aAAa,EAAE,KAAK,CAAC,aAAa;AAC3H,UAAI,CAAC,SAAU,QAAO,QAAQ,OAAuB,IAAI,MAAM,+BAA+B,IAAI,SAAS,OAAO,IAAI,yDAAyD,CAAC;AAChL,YAAM,oBAAoB,WAAW,QAAQ,IAAI,SAAS,UAAU;AACpE,aAAO,KAAK,IAAI,IAAI;AACpB,aAAO,WAAW,IAAI,IAAI;AAAA,IAC3B,CAAC,CAAC;AACF,WAAO;AAAA,EACR,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK;AAC3B;AAQA,SAAS,uBAAuB,IAAI,MAAM;AACzC,QAAM,iBAAiB,CAAC;AACxB,QAAM,kBAAkB,CAAC;AACzB,QAAM,kBAAkB,CAAC;AACzB,QAAM,MAAM,KAAK,IAAI,KAAK,QAAQ,QAAQ,GAAG,QAAQ,MAAM;AAC3D,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,UAAM,aAAa,KAAK,QAAQ,CAAC;AACjC,QAAI,WAAY,KAAI,GAAG,QAAQ,KAAK,CAAC,WAAW,kBAAkB,QAAQ,UAAU,CAAC,EAAG,iBAAgB,KAAK,UAAU;AAAA,QAClH,gBAAe,KAAK,UAAU;AACnC,UAAM,WAAW,GAAG,QAAQ,CAAC;AAC7B,QAAI,UAAU;AACb,UAAI,CAAC,KAAK,QAAQ,KAAK,CAAC,WAAW,kBAAkB,QAAQ,QAAQ,CAAC,EAAG,iBAAgB,KAAK,QAAQ;AAAA,IACvG;AAAA,EACD;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAWA,SAAS,oBAAoB,eAAe,SAAS;AACpD,QAAM,OAAO,OAAO,CAAC,GAAG,eAAe,EAAE,SAAS,cAAc,QAAQ,IAAI,CAAC,YAAY,KAAK,SAAS;AAAA,IACtG;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC,CAAC,EAAE,CAAC;AACL,SAAO,EAAE,SAAS;AAAA,IACjB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS,cAAc;AAAA,IACvB;AAAA,IACA,OAAO;AAAA,EACR,EAAE;AACH;AACA,SAAS,cAAc,SAAS;AAC/B,SAAO,EAAE,SAAS,EAAE,QAAQ,EAAE;AAC/B;AACA,IAAI,WAAW;AACf,SAAS,YAAY,KAAK,QAAQ,SAAS;AAC1C,MAAI,OAAO,cAAe;AAC1B,SAAO,gBAAgB;AACvB,QAAM,KAAK;AACX,sBAAoB;AAAA,IACnB,IAAI,sBAAsB,KAAK,MAAM,KAAK;AAAA,IAC1C,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,qBAAqB,CAAC,SAAS;AAAA,IAC/B;AAAA,EACD,GAAG,CAAC,QAAQ;AACX,QAAI,OAAO,IAAI,QAAQ,WAAY,QAAO,uNAAuN;AACjQ,QAAI,GAAG,iBAAiB,CAAC,SAAS,QAAQ;AACzC,UAAI,QAAQ,aAAc,SAAQ,aAAa,MAAM,KAAK;AAAA,QACzD,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO,oBAAoB,OAAO,aAAa,OAAO,eAAe;AAAA,MACtE,CAAC;AAAA,IACF,CAAC;AACD,QAAI,GAAG,mBAAmB,CAAC,EAAE,UAAU,MAAM,kBAAkB,MAAM;AACpE,UAAI,kBAAkB,gBAAgB;AACrC,cAAM,OAAO,kBAAkB;AAC/B,aAAK,KAAK,KAAK;AAAA,UACd,QAAQ,KAAK,OAAO,GAAG,KAAK,KAAK,SAAS,CAAC,OAAO,MAAM,KAAK;AAAA,UAC7D,WAAW;AAAA,UACX,SAAS;AAAA,UACT,iBAAiB;AAAA,QAClB,CAAC;AAAA,MACF;AACA,UAAI,QAAQ,kBAAkB,cAAc,GAAG;AAC9C,0BAAkB,gBAAgB;AAClC,0BAAkB,eAAe,QAAQ,CAAC,iBAAiB;AAC1D,cAAI,QAAQ,aAAa,MAAM;AAC/B,cAAI,kBAAkB;AACtB,cAAI,UAAU;AACd,cAAI,YAAY;AAChB,cAAI,aAAa,OAAO;AACvB,oBAAQ,aAAa;AACrB,8BAAkB;AAClB,wBAAY;AAAA,UACb,WAAW,aAAa,eAAe;AACtC,8BAAkB;AAClB,sBAAU;AAAA,UACX,WAAW,aAAa,UAAU;AACjC,8BAAkB;AAClB,sBAAU;AAAA,UACX;AACA,eAAK,KAAK,KAAK;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AACD,UAAM,OAAO,cAAc,MAAM;AAChC,wBAAkB;AAClB,UAAI,sBAAsB;AAC1B,UAAI,kBAAkB,iBAAiB;AACvC,UAAI,mBAAmB,iBAAiB;AAAA,IACzC,CAAC;AACD,UAAM,qBAAqB,wBAAwB;AACnD,QAAI,iBAAiB;AAAA,MACpB,IAAI;AAAA,MACJ,OAAO,SAAS,KAAK,MAAM,KAAK,EAAE;AAAA,MAClC,OAAO;AAAA,IACR,CAAC;AACD,WAAO,QAAQ,CAAC,OAAO,OAAO;AAC7B,UAAI,iBAAiB;AAAA,QACpB,SAAS;AAAA,QACT,OAAO;AAAA,UACN,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,SAAS;AAAA,UACT,MAAM,IAAI,IAAI;AAAA,UACd,MAAM,EAAE,MAAM;AAAA,UACd,SAAS,GAAG,KAAK;AAAA,QAClB;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AACD,QAAI,eAAe;AACnB,WAAO,WAAW,CAAC,IAAI,SAAS;AAC/B,YAAM,OAAO;AAAA,QACZ,OAAO,cAAc,YAAY;AAAA,QACjC,MAAM,oBAAoB,MAAM,yCAAyC;AAAA,QACzE,IAAI,oBAAoB,IAAI,iBAAiB;AAAA,MAC9C;AACA,aAAO,eAAe,GAAG,MAAM,kBAAkB,EAAE,OAAO,eAAe,CAAC;AAC1E,UAAI,iBAAiB;AAAA,QACpB,SAAS;AAAA,QACT,OAAO;AAAA,UACN,MAAM,IAAI,IAAI;AAAA,UACd,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb;AAAA,UACA,SAAS,GAAG,KAAK;AAAA,QAClB;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AACD,WAAO,UAAU,CAAC,IAAI,MAAM,YAAY;AACvC,YAAM,OAAO,EAAE,OAAO,cAAc,WAAW,EAAE;AACjD,UAAI,SAAS;AACZ,aAAK,UAAU,EAAE,SAAS;AAAA,UACzB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,UAAU,QAAQ,UAAU;AAAA,UACrC,SAAS;AAAA,UACT,OAAO;AAAA,QACR,EAAE;AACF,aAAK,SAAS,cAAc,GAAG;AAAA,MAChC,MAAO,MAAK,SAAS,cAAc,GAAG;AACtC,WAAK,OAAO,oBAAoB,MAAM,yCAAyC;AAC/E,WAAK,KAAK,oBAAoB,IAAI,iBAAiB;AACnD,UAAI,iBAAiB;AAAA,QACpB,SAAS;AAAA,QACT,OAAO;AAAA,UACN,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,MAAM,IAAI,IAAI;AAAA,UACd;AAAA,UACA,SAAS,UAAU,YAAY;AAAA,UAC/B,SAAS,GAAG,KAAK;AAAA,QAClB;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AAID,UAAM,oBAAoB,sBAAsB;AAChD,QAAI,aAAa;AAAA,MAChB,IAAI;AAAA,MACJ,OAAO,YAAY,KAAK,MAAM,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,uBAAuB;AAAA,IACxB,CAAC;AACD,aAAS,oBAAoB;AAC5B,UAAI,CAAC,oBAAqB;AAC1B,YAAM,UAAU;AAChB,UAAI,SAAS,QAAQ,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,OAAO,OAAO,UAAU;AACnG,aAAO,QAAQ,4BAA4B;AAC3C,UAAI,QAAQ,OAAQ,UAAS,OAAO,OAAO,CAAC,UAAU,gBAAgB,OAAO,QAAQ,OAAO,YAAY,CAAC,CAAC;AAC1G,aAAO,QAAQ,CAAC,UAAU,sBAAsB,OAAO,OAAO,aAAa,KAAK,CAAC;AACjF,cAAQ,YAAY,OAAO,IAAI,6BAA6B;AAAA,IAC7D;AACA,QAAI;AACJ,QAAI,GAAG,iBAAiB,CAAC,YAAY;AACpC,4BAAsB;AACtB,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,kBAAmB,mBAAkB;AAAA,IACzF,CAAC;AAID,QAAI,GAAG,kBAAkB,CAAC,YAAY;AACrC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;AACrE,cAAM,QAAQ,QAAQ,UAAU,EAAE,KAAK,CAAC,YAAY,QAAQ,OAAO,YAAY,QAAQ,MAAM;AAC7F,YAAI,MAAO,SAAQ,QAAQ,EAAE,SAAS,0CAA0C,KAAK,EAAE;AAAA,MACxF;AAAA,IACD,CAAC;AACD,QAAI,kBAAkB,iBAAiB;AACvC,QAAI,mBAAmB,iBAAiB;AAAA,EACzC,CAAC;AACF;AACA,SAAS,eAAe,KAAK;AAC5B,MAAI,IAAI,SAAU,QAAO,IAAI,aAAa,MAAM;AAAA,MAC3C,QAAO,IAAI,aAAa,MAAM;AACpC;AACA,SAAS,0CAA0C,OAAO;AACzD,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,SAAS,CAAC;AAAA,IACf,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO,OAAO;AAAA,EACf,CAAC;AACD,MAAI,OAAO,QAAQ,KAAM,QAAO,KAAK;AAAA,IACpC,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO,OAAO;AAAA,EACf,CAAC;AACD,SAAO,KAAK;AAAA,IACX,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO,MAAM;AAAA,EACd,CAAC;AACD,MAAI,MAAM,KAAK,OAAQ,QAAO,KAAK;AAAA,IAClC,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO,EAAE,SAAS;AAAA,MACjB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,MAAM,KAAK,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,GAAG,eAAe,GAAG,CAAC,EAAE,EAAE,KAAK,GAAG;AAAA,MAC9E,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACd,EAAE;AAAA,EACH,CAAC;AACD,MAAI,OAAO,YAAY,KAAM,QAAO,KAAK;AAAA,IACxC,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO,OAAO;AAAA,EACf,CAAC;AACD,MAAI,MAAM,MAAM,OAAQ,QAAO,KAAK;AAAA,IACnC,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO,MAAM,MAAM,IAAI,CAAC,UAAU,MAAM,OAAO,IAAI;AAAA,EACpD,CAAC;AACD,MAAI,OAAO,KAAK,MAAM,OAAO,IAAI,EAAE,OAAQ,QAAO,KAAK;AAAA,IACtD,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO,MAAM,OAAO;AAAA,EACrB,CAAC;AACD,SAAO,KAAK;AAAA,IACX,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,EAAE,SAAS;AAAA,MACjB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,MAAM,MAAM,IAAI,CAAC,UAAU,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK;AAAA,MAChE,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACd,EAAE;AAAA,EACH,CAAC;AACD,SAAO;AACR;AAIA,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,SAAS,8BAA8B,OAAO;AAC7C,QAAM,OAAO,CAAC;AACd,QAAM,EAAE,OAAO,IAAI;AACnB,MAAI,OAAO,QAAQ,KAAM,MAAK,KAAK;AAAA,IAClC,OAAO,OAAO,OAAO,IAAI;AAAA,IACzB,WAAW;AAAA,IACX,iBAAiB;AAAA,EAClB,CAAC;AACD,MAAI,OAAO,QAAS,MAAK,KAAK;AAAA,IAC7B,OAAO;AAAA,IACP,WAAW;AAAA,IACX,iBAAiB;AAAA,EAClB,CAAC;AACD,MAAI,MAAM,WAAY,MAAK,KAAK;AAAA,IAC/B,OAAO;AAAA,IACP,WAAW;AAAA,IACX,iBAAiB;AAAA,EAClB,CAAC;AACD,MAAI,MAAM,iBAAkB,MAAK,KAAK;AAAA,IACrC,OAAO;AAAA,IACP,WAAW;AAAA,IACX,iBAAiB;AAAA,EAClB,CAAC;AACD,MAAI,MAAM,YAAa,MAAK,KAAK;AAAA,IAChC,OAAO;AAAA,IACP,WAAW;AAAA,IACX,iBAAiB;AAAA,EAClB,CAAC;AACD,MAAI,OAAO,SAAU,MAAK,KAAK;AAAA,IAC9B,OAAO,OAAO,OAAO,aAAa,WAAW,aAAa,OAAO,QAAQ,KAAK;AAAA,IAC9E,WAAW;AAAA,IACX,iBAAiB;AAAA,EAClB,CAAC;AACD,MAAI,KAAK,OAAO;AAChB,MAAI,MAAM,MAAM;AACf,SAAK,OAAO,eAAe;AAC3B,WAAO,UAAU;AAAA,EAClB;AACA,SAAO;AAAA,IACN;AAAA,IACA,OAAO,OAAO;AAAA,IACd;AAAA,IACA,UAAU,MAAM,SAAS,IAAI,6BAA6B;AAAA,EAC3D;AACD;AACA,IAAI,gBAAgB;AACpB,IAAM,oBAAoB;AAC1B,SAAS,sBAAsB,OAAO,cAAc;AACnD,QAAM,gBAAgB,aAAa,QAAQ,UAAU,kBAAkB,aAAa,QAAQ,aAAa,QAAQ,SAAS,CAAC,GAAG,MAAM,MAAM;AAC1I,QAAM,mBAAmB,MAAM,cAAc;AAC7C,MAAI,CAAC,cAAe,OAAM,cAAc,aAAa,QAAQ,KAAK,CAAC,UAAU,kBAAkB,OAAO,MAAM,MAAM,CAAC;AACnH,QAAM,SAAS,QAAQ,CAAC,eAAe,sBAAsB,YAAY,YAAY,CAAC;AACvF;AACA,SAAS,6BAA6B,OAAO;AAC5C,QAAM,aAAa;AACnB,QAAM,SAAS,QAAQ,4BAA4B;AACpD;AACA,SAAS,gBAAgB,OAAO,QAAQ;AACvC,QAAM,QAAQ,OAAO,MAAM,EAAE,EAAE,MAAM,iBAAiB;AACtD,QAAM,aAAa;AACnB,MAAI,CAAC,SAAS,MAAM,SAAS,EAAG,QAAO;AACvC,MAAI,IAAI,OAAO,MAAM,CAAC,EAAE,QAAQ,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,GAAG;AACnE,UAAM,SAAS,QAAQ,CAAC,UAAU,gBAAgB,OAAO,MAAM,CAAC;AAChE,QAAI,MAAM,OAAO,SAAS,OAAO,WAAW,KAAK;AAChD,YAAM,aAAa,MAAM,GAAG,KAAK,MAAM;AACvC,aAAO;AAAA,IACR;AACA,WAAO;AAAA,EACR;AACA,QAAM,OAAO,MAAM,OAAO,KAAK,YAAY;AAC3C,QAAM,cAAc,OAAO,IAAI;AAC/B,MAAI,CAAC,OAAO,WAAW,GAAG,MAAM,YAAY,SAAS,MAAM,KAAK,KAAK,SAAS,MAAM,GAAI,QAAO;AAC/F,MAAI,YAAY,WAAW,MAAM,KAAK,KAAK,WAAW,MAAM,EAAG,QAAO;AACtE,MAAI,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,IAAI,EAAE,SAAS,MAAM,EAAG,QAAO;AAC5E,SAAO,MAAM,SAAS,KAAK,CAAC,UAAU,gBAAgB,OAAO,MAAM,CAAC;AACrE;AACA,SAAS,KAAK,KAAK,MAAM;AACxB,QAAM,MAAM,CAAC;AACb,aAAW,OAAO,IAAK,KAAI,CAAC,KAAK,SAAS,GAAG,EAAG,KAAI,GAAG,IAAI,IAAI,GAAG;AAClE,SAAO;AACR;;;ACrrCA,IAAI,qBAAqB,MAAM,SAAS,WAAW,OAAO,SAAS;AAMnE,SAAS,sBAAsB,MAAM,YAAY;AAChD,QAAM,EAAE,UAAU,QAAQ,KAAK,IAAI;AACnC,QAAM,UAAU,KAAK,QAAQ,GAAG;AAChC,MAAI,UAAU,IAAI;AACjB,QAAI,WAAW,KAAK,SAAS,KAAK,MAAM,OAAO,CAAC,IAAI,KAAK,MAAM,OAAO,EAAE,SAAS;AACjF,QAAI,eAAe,KAAK,MAAM,QAAQ;AACtC,QAAI,aAAa,CAAC,MAAM,IAAK,gBAAe,MAAM;AAClD,WAAO,UAAU,cAAc,EAAE;AAAA,EAClC;AACA,SAAO,UAAU,UAAU,IAAI,IAAI,SAAS;AAC7C;AACA,SAAS,oBAAoB,MAAM,cAAc,iBAAiB,SAAS;AAC1E,MAAI,YAAY,CAAC;AACjB,MAAI,YAAY,CAAC;AACjB,MAAI,aAAa;AACjB,QAAM,kBAAkB,CAAC,EAAE,MAAM,MAAM;AACtC,UAAM,KAAK,sBAAsB,MAAM,QAAQ;AAC/C,UAAM,OAAO,gBAAgB;AAC7B,UAAM,YAAY,aAAa;AAC/B,QAAI,QAAQ;AACZ,QAAI,OAAO;AACV,sBAAgB,QAAQ;AACxB,mBAAa,QAAQ;AACrB,UAAI,cAAc,eAAe,MAAM;AACtC,qBAAa;AACb;AAAA,MACD;AACA,cAAQ,YAAY,MAAM,WAAW,UAAU,WAAW;AAAA,IAC3D,MAAO,SAAQ,EAAE;AACjB,cAAU,QAAQ,CAAC,aAAa;AAC/B,eAAS,gBAAgB,OAAO,MAAM;AAAA,QACrC;AAAA,QACA,MAAM,eAAe;AAAA,QACrB,WAAW,QAAQ,QAAQ,IAAI,oBAAoB,UAAU,oBAAoB,OAAO,oBAAoB;AAAA,MAC7G,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AACA,WAAS,iBAAiB;AACzB,iBAAa,gBAAgB;AAAA,EAC9B;AACA,WAAS,OAAO,UAAU;AACzB,cAAU,KAAK,QAAQ;AACvB,UAAM,WAAW,MAAM;AACtB,YAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,QAAQ,GAAI,WAAU,OAAO,OAAO,CAAC;AAAA,IAC1C;AACA,cAAU,KAAK,QAAQ;AACvB,WAAO;AAAA,EACR;AACA,WAAS,uBAAuB;AAC/B,QAAI,SAAS,oBAAoB,UAAU;AAC1C,YAAM,EAAE,SAAS,UAAU,IAAI;AAC/B,UAAI,CAAC,UAAU,MAAO;AACtB,gBAAU,aAAa,OAAO,CAAC,GAAG,UAAU,OAAO,EAAE,QAAQ,sBAAsB,EAAE,CAAC,GAAG,EAAE;AAAA,IAC5F;AAAA,EACD;AACA,WAAS,UAAU;AAClB,eAAW,YAAY,UAAW,UAAS;AAC3C,gBAAY,CAAC;AACb,WAAO,oBAAoB,YAAY,eAAe;AACtD,WAAO,oBAAoB,YAAY,oBAAoB;AAC3D,aAAS,oBAAoB,oBAAoB,oBAAoB;AAAA,EACtE;AACA,SAAO,iBAAiB,YAAY,eAAe;AACnD,SAAO,iBAAiB,YAAY,oBAAoB;AACxD,WAAS,iBAAiB,oBAAoB,oBAAoB;AAClE,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAIA,SAAS,WAAW,MAAM,SAAS,SAAS,WAAW,OAAO,gBAAgB,OAAO;AACpF,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,OAAO,QAAQ;AAAA,IACzB,QAAQ,gBAAgB,sBAAsB,IAAI;AAAA,EACnD;AACD;AACA,SAAS,0BAA0B,MAAM;AACxC,QAAM,EAAE,SAAS,WAAW,UAAU,WAAW,IAAI;AACrD,QAAM,kBAAkB,EAAE,OAAO,sBAAsB,MAAM,UAAU,EAAE;AACzE,QAAM,eAAe,EAAE,OAAO,UAAU,MAAM;AAC9C,MAAI,CAAC,aAAa,MAAO,gBAAe,gBAAgB,OAAO;AAAA,IAC9D,MAAM;AAAA,IACN,SAAS,gBAAgB;AAAA,IACzB,SAAS;AAAA,IACT,UAAU,UAAU,SAAS;AAAA,IAC7B,UAAU;AAAA,IACV,QAAQ;AAAA,EACT,GAAG,IAAI;AACP,WAAS,eAAe,IAAI,OAAO,WAAW;AAU7C,UAAM,YAAY,KAAK,QAAQ,GAAG;AAClC,UAAM,MAAM,YAAY,MAAM,WAAW,QAAQ,SAAS,cAAc,MAAM,IAAI,OAAO,KAAK,MAAM,SAAS,KAAK,KAAK,mBAAmB,IAAI,OAAO;AACrJ,QAAI;AACH,gBAAU,YAAY,iBAAiB,WAAW,EAAE,OAAO,IAAI,GAAG;AAClE,mBAAa,QAAQ;AAAA,IACtB,SAAS,KAAK;AACb,UAAI,KAAuC,QAAO,iCAAiC,GAAG;AAAA,UACjF,SAAQ,MAAM,GAAG;AACtB,iBAAW,YAAY,YAAY,QAAQ,EAAE,GAAG;AAAA,IACjD;AAAA,EACD;AACA,WAAS,QAAQ,IAAI,MAAM;AAC1B,mBAAe,IAAI,OAAO,CAAC,GAAG,UAAU,OAAO,WAAW,aAAa,MAAM,MAAM,IAAI,aAAa,MAAM,SAAS,IAAI,GAAG,MAAM,EAAE,UAAU,aAAa,MAAM,SAAS,CAAC,GAAG,IAAI;AAChL,oBAAgB,QAAQ;AAAA,EACzB;AACA,WAAS,KAAK,IAAI,MAAM;AACvB,UAAM,eAAe,OAAO,CAAC,GAAG,aAAa,OAAO,UAAU,OAAO;AAAA,MACpE,SAAS;AAAA,MACT,QAAQ,sBAAsB;AAAA,IAC/B,CAAC;AACD,QAA6C,CAAC,UAAU,MAAO,QAAO,iVAAiV;AACvZ,mBAAe,aAAa,SAAS,cAAc,IAAI;AACvD,mBAAe,IAAI,OAAO,CAAC,GAAG,WAAW,gBAAgB,OAAO,IAAI,IAAI,GAAG,EAAE,UAAU,aAAa,WAAW,EAAE,GAAG,IAAI,GAAG,KAAK;AAChI,oBAAgB,QAAQ;AAAA,EACzB;AACA,SAAO;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACD;AACD;AAMA,SAAS,iBAAiB,MAAM;AAC/B,SAAO,cAAc,IAAI;AACzB,QAAM,oBAAoB,0BAA0B,IAAI;AACxD,QAAM,mBAAmB,oBAAoB,MAAM,kBAAkB,OAAO,kBAAkB,UAAU,kBAAkB,OAAO;AACjI,WAAS,GAAG,OAAO,mBAAmB,MAAM;AAC3C,QAAI,CAAC,iBAAkB,kBAAiB,eAAe;AACvD,YAAQ,GAAG,KAAK;AAAA,EACjB;AACA,QAAM,gBAAgB,OAAO;AAAA,IAC5B,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,YAAY,WAAW,KAAK,MAAM,IAAI;AAAA,EACvC,GAAG,mBAAmB,gBAAgB;AACtC,SAAO,eAAe,eAAe,YAAY;AAAA,IAChD,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,SAAS;AAAA,EACvC,CAAC;AACD,SAAO,eAAe,eAAe,SAAS;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,MAAM;AAAA,EACpC,CAAC;AACD,SAAO;AACR;AAWA,SAAS,oBAAoB,OAAO,IAAI;AACvC,MAAI,YAAY,CAAC;AACjB,MAAI,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACxB,MAAI,WAAW;AACf,SAAO,cAAc,IAAI;AACzB,WAAS,YAAY,YAAY,QAAQ,CAAC,GAAG;AAC5C;AACA,QAAI,aAAa,MAAM,OAAQ,OAAM,OAAO,QAAQ;AACpD,UAAM,KAAK,CAAC,YAAY,KAAK,CAAC;AAAA,EAC/B;AACA,WAAS,iBAAiB,IAAI,MAAM,EAAE,WAAW,MAAM,GAAG;AACzD,UAAM,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,MACA,MAAM,eAAe;AAAA,IACtB;AACA,eAAW,YAAY,UAAW,UAAS,IAAI,MAAM,IAAI;AAAA,EAC1D;AACA,QAAM,gBAAgB;AAAA,IACrB,UAAU;AAAA,IACV,OAAO,CAAC;AAAA,IACR;AAAA,IACA,YAAY,WAAW,KAAK,MAAM,IAAI;AAAA,IACtC,QAAQ,IAAI,OAAO;AAClB,YAAM,OAAO,YAAY,CAAC;AAC1B,kBAAY,IAAI,KAAK;AAAA,IACtB;AAAA,IACA,KAAK,IAAI,OAAO;AACf,kBAAY,IAAI,KAAK;AAAA,IACtB;AAAA,IACA,OAAO,UAAU;AAChB,gBAAU,KAAK,QAAQ;AACvB,aAAO,MAAM;AACZ,cAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,YAAI,QAAQ,GAAI,WAAU,OAAO,OAAO,CAAC;AAAA,MAC1C;AAAA,IACD;AAAA,IACA,UAAU;AACT,kBAAY,CAAC;AACb,cAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACpB,iBAAW;AAAA,IACZ;AAAA,IACA,GAAG,OAAO,gBAAgB,MAAM;AAC/B,YAAM,OAAO,KAAK;AAClB,YAAM,YAAY,QAAQ,IAAI,oBAAoB,OAAO,oBAAoB;AAC7E,iBAAW,KAAK,IAAI,GAAG,KAAK,IAAI,WAAW,OAAO,MAAM,SAAS,CAAC,CAAC;AACnE,UAAI,cAAe,kBAAiB,KAAK,UAAU,MAAM;AAAA,QACxD;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACA,SAAO,eAAe,eAAe,YAAY;AAAA,IAChD,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC;AAAA,EAC7B,CAAC;AACD,SAAO,eAAe,eAAe,SAAS;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC;AAAA,EAC7B,CAAC;AACD,SAAO;AACR;AA4BA,SAAS,qBAAqB,MAAM;AACnC,SAAO,SAAS,OAAO,QAAQ,SAAS,WAAW,SAAS,SAAS;AACrE,MAAI,CAAC,KAAK,SAAS,GAAG,EAAG,SAAQ;AACjC,MAA6C,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,SAAS,GAAG,EAAG,QAAO;AAAA,GAAsC,IAAI,gBAAgB,KAAK,QAAQ,QAAQ,GAAG,CAAC,IAAI;AACxL,SAAO,iBAAiB,IAAI;AAC7B;AAIA,IAAI,YAA4B,SAAS,aAAa;AACrD,cAAY,YAAY,QAAQ,IAAI,CAAC,IAAI;AACzC,cAAY,YAAY,OAAO,IAAI,CAAC,IAAI;AACxC,cAAY,YAAY,OAAO,IAAI,CAAC,IAAI;AACxC,SAAO;AACR,EAAE,CAAC,CAAC;AACJ,IAAI,iBAAiC,SAAS,kBAAkB;AAC/D,mBAAiB,iBAAiB,QAAQ,IAAI,CAAC,IAAI;AACnD,mBAAiB,iBAAiB,OAAO,IAAI,CAAC,IAAI;AAClD,mBAAiB,iBAAiB,aAAa,IAAI,CAAC,IAAI;AACxD,mBAAiB,iBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC3D,mBAAiB,iBAAiB,YAAY,IAAI,CAAC,IAAI;AACvD,SAAO;AACR,EAAE,kBAAkB,CAAC,CAAC;AACtB,IAAM,aAAa;AAAA,EAClB,MAAM,UAAU;AAAA,EAChB,OAAO;AACR;AACA,IAAM,iBAAiB;AACvB,SAAS,aAAa,MAAM;AAC3B,MAAI,CAAC,KAAM,QAAO,CAAC,CAAC,CAAC;AACrB,MAAI,SAAS,IAAK,QAAO,CAAC,CAAC,UAAU,CAAC;AACtC,MAAI,CAAC,KAAK,WAAW,GAAG,EAAG,OAAM,IAAI,MAAM,OAAwC,yCAAyC,IAAI,iBAAiB,IAAI,OAAO,iBAAiB,IAAI,GAAG;AACpL,WAAS,MAAM,SAAS;AACvB,UAAM,IAAI,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO,EAAE;AAAA,EACzD;AACA,MAAI,QAAQ,eAAe;AAC3B,MAAI,gBAAgB;AACpB,QAAM,SAAS,CAAC;AAChB,MAAI;AACJ,WAAS,kBAAkB;AAC1B,QAAI,QAAS,QAAO,KAAK,OAAO;AAChC,cAAU,CAAC;AAAA,EACZ;AACA,MAAI,IAAI;AACR,MAAI;AACJ,MAAI,SAAS;AACb,MAAI,WAAW;AACf,WAAS,gBAAgB;AACxB,QAAI,CAAC,OAAQ;AACb,QAAI,UAAU,eAAe,OAAQ,SAAQ,KAAK;AAAA,MACjD,MAAM,UAAU;AAAA,MAChB,OAAO;AAAA,IACR,CAAC;AAAA,aACQ,UAAU,eAAe,SAAS,UAAU,eAAe,eAAe,UAAU,eAAe,gBAAgB;AAC3H,UAAI,QAAQ,SAAS,MAAM,SAAS,OAAO,SAAS,KAAM,OAAM,uBAAuB,MAAM,8CAA8C;AAC3I,cAAQ,KAAK;AAAA,QACZ,MAAM,UAAU;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY,SAAS,OAAO,SAAS;AAAA,QACrC,UAAU,SAAS,OAAO,SAAS;AAAA,MACpC,CAAC;AAAA,IACF,MAAO,OAAM,iCAAiC;AAC9C,aAAS;AAAA,EACV;AACA,WAAS,kBAAkB;AAC1B,cAAU;AAAA,EACX;AACA,SAAO,IAAI,KAAK,QAAQ;AACvB,WAAO,KAAK,GAAG;AACf,QAAI,SAAS,QAAQ,UAAU,eAAe,aAAa;AAC1D,sBAAgB;AAChB,cAAQ,eAAe;AACvB;AAAA,IACD;AACA,YAAQ,OAAO;AAAA,MACd,KAAK,eAAe;AACnB,YAAI,SAAS,KAAK;AACjB,cAAI,OAAQ,eAAc;AAC1B,0BAAgB;AAAA,QACjB,WAAW,SAAS,KAAK;AACxB,wBAAc;AACd,kBAAQ,eAAe;AAAA,QACxB,MAAO,iBAAgB;AACvB;AAAA,MACD,KAAK,eAAe;AACnB,wBAAgB;AAChB,gBAAQ;AACR;AAAA,MACD,KAAK,eAAe;AACnB,YAAI,SAAS,IAAK,SAAQ,eAAe;AAAA,iBAChC,eAAe,KAAK,IAAI,EAAG,iBAAgB;AAAA,aAC/C;AACJ,wBAAc;AACd,kBAAQ,eAAe;AACvB,cAAI,SAAS,OAAO,SAAS,OAAO,SAAS,IAAK;AAAA,QACnD;AACA;AAAA,MACD,KAAK,eAAe;AACnB,YAAI,SAAS,IAAK,KAAI,SAAS,SAAS,SAAS,CAAC,KAAK,KAAM,YAAW,SAAS,MAAM,GAAG,EAAE,IAAI;AAAA,YAC3F,SAAQ,eAAe;AAAA,YACvB,aAAY;AACjB;AAAA,MACD,KAAK,eAAe;AACnB,sBAAc;AACd,gBAAQ,eAAe;AACvB,YAAI,SAAS,OAAO,SAAS,OAAO,SAAS,IAAK;AAClD,mBAAW;AACX;AAAA,MACD;AACC,cAAM,eAAe;AACrB;AAAA,IACF;AAAA,EACD;AACA,MAAI,UAAU,eAAe,YAAa,OAAM,uCAAuC,MAAM,GAAG;AAChG,gBAAc;AACd,kBAAgB;AAChB,SAAO;AACR;AAIA,IAAM,qBAAqB;AAC3B,IAAM,2BAA2B;AAAA,EAChC,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AACN;AACA,IAAI,YAA4B,SAAS,aAAa;AACrD,cAAY,YAAY,aAAa,IAAI,EAAE,IAAI;AAC/C,cAAY,YAAY,MAAM,IAAI,EAAE,IAAI;AACxC,cAAY,YAAY,SAAS,IAAI,EAAE,IAAI;AAC3C,cAAY,YAAY,YAAY,IAAI,EAAE,IAAI;AAC9C,cAAY,YAAY,QAAQ,IAAI,EAAE,IAAI;AAC1C,cAAY,YAAY,SAAS,IAAI,EAAE,IAAI;AAC3C,cAAY,YAAY,mBAAmB,IAAI,EAAE,IAAI;AACrD,cAAY,YAAY,eAAe,IAAI,GAAG,IAAI;AAClD,cAAY,YAAY,iBAAiB,IAAI,GAAG,IAAI;AACpD,cAAY,YAAY,eAAe,IAAI,EAAE,IAAI;AACjD,cAAY,YAAY,aAAa,IAAI,kBAAiB,IAAI;AAC9D,cAAY,YAAY,oBAAoB,IAAI,IAAG,IAAI;AACvD,SAAO;AACR,EAAE,aAAa,CAAC,CAAC;AACjB,IAAM,iBAAiB;AAQvB,SAAS,eAAe,UAAU,cAAc;AAC/C,QAAM,UAAU,OAAO,CAAC,GAAG,0BAA0B,YAAY;AACjE,QAAM,QAAQ,CAAC;AACf,MAAI,UAAU,QAAQ,QAAQ,MAAM;AACpC,QAAM,OAAO,CAAC;AACd,aAAW,WAAW,UAAU;AAC/B,UAAM,gBAAgB,QAAQ,SAAS,CAAC,IAAI,CAAC,UAAU,IAAI;AAC3D,QAAI,QAAQ,UAAU,CAAC,QAAQ,OAAQ,YAAW;AAClD,aAAS,aAAa,GAAG,aAAa,QAAQ,QAAQ,cAAc;AACnE,YAAM,QAAQ,QAAQ,UAAU;AAChC,UAAI,kBAAkB,UAAU,WAAW,QAAQ,YAAY,UAAU,qBAAqB;AAC9F,UAAI,MAAM,SAAS,UAAU,QAAQ;AACpC,YAAI,CAAC,WAAY,YAAW;AAC5B,mBAAW,MAAM,MAAM,QAAQ,gBAAgB,MAAM;AACrD,2BAAmB,UAAU;AAAA,MAC9B,WAAW,MAAM,SAAS,UAAU,OAAO;AAC1C,cAAM,EAAE,OAAO,YAAY,UAAU,OAAO,IAAI;AAChD,aAAK,KAAK;AAAA,UACT,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACD,CAAC;AACD,cAAM,OAAO,SAAS,SAAS;AAC/B,YAAI,SAAS,oBAAoB;AAChC,6BAAmB,UAAU;AAC7B,cAAI;AACH,eAAG,IAAI;AAAA,UACR,SAAS,KAAK;AACb,kBAAM,IAAI,MAAM,oCAAoC,KAAK,MAAM,IAAI,QAAQ,IAAI,OAAO;AAAA,UACvF;AAAA,QACD;AACA,YAAI,aAAa,aAAa,OAAO,IAAI,WAAW,IAAI,SAAS,IAAI,IAAI;AACzE,YAAI,CAAC,WAAY,cAAa,YAAY,QAAQ,SAAS,IAAI,OAAO,UAAU,MAAM,MAAM;AAC5F,YAAI,SAAU,eAAc;AAC5B,mBAAW;AACX,2BAAmB,UAAU;AAC7B,YAAI,SAAU,oBAAmB,UAAU;AAC3C,YAAI,WAAY,oBAAmB,UAAU;AAC7C,YAAI,SAAS,KAAM,oBAAmB,UAAU;AAAA,MACjD;AACA,oBAAc,KAAK,eAAe;AAAA,IACnC;AACA,UAAM,KAAK,aAAa;AAAA,EACzB;AACA,MAAI,QAAQ,UAAU,QAAQ,KAAK;AAClC,UAAM,IAAI,MAAM,SAAS;AACzB,UAAM,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,KAAK,UAAU;AAAA,EAC5C;AACA,MAAI,CAAC,QAAQ,OAAQ,YAAW;AAChC,MAAI,QAAQ,IAAK,YAAW;AAAA,WACnB,QAAQ,UAAU,CAAC,QAAQ,SAAS,GAAG,EAAG,YAAW;AAC9D,QAAM,KAAK,IAAI,OAAO,SAAS,QAAQ,YAAY,KAAK,GAAG;AAC3D,WAAS,MAAM,MAAM;AACpB,UAAM,QAAQ,KAAK,MAAM,EAAE;AAC3B,UAAM,SAAS,CAAC;AAChB,QAAI,CAAC,MAAO,QAAO;AACnB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,MAAM,KAAK,IAAI,CAAC;AACtB,aAAO,IAAI,IAAI,IAAI,SAAS,IAAI,aAAa,MAAM,MAAM,GAAG,IAAI;AAAA,IACjE;AACA,WAAO;AAAA,EACR;AACA,WAAS,UAAU,QAAQ;AAC1B,QAAI,OAAO;AACX,QAAI,uBAAuB;AAC3B,eAAW,WAAW,UAAU;AAC/B,UAAI,CAAC,wBAAwB,CAAC,KAAK,SAAS,GAAG,EAAG,SAAQ;AAC1D,6BAAuB;AACvB,iBAAW,SAAS,QAAS,KAAI,MAAM,SAAS,UAAU,OAAQ,SAAQ,MAAM;AAAA,eACvE,MAAM,SAAS,UAAU,OAAO;AACxC,cAAM,EAAE,OAAO,YAAY,SAAS,IAAI;AACxC,cAAM,QAAQ,SAAS,SAAS,OAAO,KAAK,IAAI;AAChD,YAAI,QAAQ,KAAK,KAAK,CAAC,WAAY,OAAM,IAAI,MAAM,mBAAmB,KAAK,2DAA2D;AACtI,cAAM,OAAO,QAAQ,KAAK,IAAI,MAAM,KAAK,GAAG,IAAI;AAChD,YAAI,CAAC,KAAM,KAAI,UAAU;AACxB,cAAI,QAAQ,SAAS,EAAG,KAAI,KAAK,SAAS,GAAG,EAAG,QAAO,KAAK,MAAM,GAAG,EAAE;AAAA,cAClE,wBAAuB;AAAA,QAC7B,MAAO,OAAM,IAAI,MAAM,2BAA2B,KAAK,GAAG;AAC1D,gBAAQ;AAAA,MACT;AAAA,IACD;AACA,WAAO,QAAQ;AAAA,EAChB;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAUA,SAAS,kBAAkB,GAAG,GAAG;AAChC,MAAI,IAAI;AACR,SAAO,IAAI,EAAE,UAAU,IAAI,EAAE,QAAQ;AACpC,UAAM,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AACvB,QAAI,KAAM,QAAO;AACjB;AAAA,EACD;AACA,MAAI,EAAE,SAAS,EAAE,OAAQ,QAAO,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM,UAAU,SAAS,UAAU,UAAU,KAAK;AAAA,WAC9F,EAAE,SAAS,EAAE,OAAQ,QAAO,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM,UAAU,SAAS,UAAU,UAAU,IAAI;AAC3G,SAAO;AACR;AAQA,SAAS,uBAAuB,GAAG,GAAG;AACrC,MAAI,IAAI;AACR,QAAM,SAAS,EAAE;AACjB,QAAM,SAAS,EAAE;AACjB,SAAO,IAAI,OAAO,UAAU,IAAI,OAAO,QAAQ;AAC9C,UAAM,OAAO,kBAAkB,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACnD,QAAI,KAAM,QAAO;AACjB;AAAA,EACD;AACA,MAAI,KAAK,IAAI,OAAO,SAAS,OAAO,MAAM,MAAM,GAAG;AAClD,QAAI,oBAAoB,MAAM,EAAG,QAAO;AACxC,QAAI,oBAAoB,MAAM,EAAG,QAAO;AAAA,EACzC;AACA,SAAO,OAAO,SAAS,OAAO;AAC/B;AAOA,SAAS,oBAAoB,OAAO;AACnC,QAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AACnC,SAAO,MAAM,SAAS,KAAK,KAAK,KAAK,SAAS,CAAC,IAAI;AACpD;AACA,IAAM,+BAA+B;AAAA,EACpC,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,WAAW;AACZ;AAIA,SAAS,yBAAyB,QAAQ,QAAQ,SAAS;AAC1D,QAAM,SAAS,eAAe,aAAa,OAAO,IAAI,GAAG,OAAO;AAChE,MAAI,MAAuC;AAC1C,UAAM,eAA+B,oBAAI,IAAI;AAC7C,eAAW,OAAO,OAAO,MAAM;AAC9B,UAAI,aAAa,IAAI,IAAI,IAAI,EAAG,QAAO,sCAAsC,IAAI,IAAI,eAAe,OAAO,IAAI,4DAA4D;AAC3K,mBAAa,IAAI,IAAI,IAAI;AAAA,IAC1B;AAAA,EACD;AACA,QAAM,UAAU,OAAO,QAAQ;AAAA,IAC9B;AAAA,IACA;AAAA,IACA,UAAU,CAAC;AAAA,IACX,OAAO,CAAC;AAAA,EACT,CAAC;AACD,MAAI,QAAQ;AACX,QAAI,CAAC,QAAQ,OAAO,YAAY,CAAC,OAAO,OAAO,QAAS,QAAO,SAAS,KAAK,OAAO;AAAA,EACrF;AACA,SAAO;AACR;AAWA,SAAS,oBAAoB,QAAQ,eAAe;AACnD,QAAM,WAAW,CAAC;AAClB,QAAM,aAA6B,oBAAI,IAAI;AAC3C,kBAAgB,aAAa,8BAA8B,aAAa;AACxE,WAAS,iBAAiB,MAAM;AAC/B,WAAO,WAAW,IAAI,IAAI;AAAA,EAC3B;AACA,WAAS,SAAS,QAAQ,QAAQ,gBAAgB;AACjD,UAAM,YAAY,CAAC;AACnB,UAAM,uBAAuB,qBAAqB,MAAM;AACxD,QAAI,KAAuC,oCAAmC,sBAAsB,MAAM;AAC1G,yBAAqB,UAAU,kBAAkB,eAAe;AAChE,UAAM,UAAU,aAAa,eAAe,MAAM;AAClD,UAAM,oBAAoB,CAAC,oBAAoB;AAC/C,QAAI,WAAW,QAAQ;AACtB,YAAM,UAAU,OAAO,OAAO,UAAU,WAAW,CAAC,OAAO,KAAK,IAAI,OAAO;AAC3E,iBAAW,SAAS,QAAS,mBAAkB,KAAK,qBAAqB,OAAO,CAAC,GAAG,sBAAsB;AAAA,QACzG,YAAY,iBAAiB,eAAe,OAAO,aAAa,qBAAqB;AAAA,QACrF,MAAM;AAAA,QACN,SAAS,iBAAiB,eAAe,SAAS;AAAA,MACnD,CAAC,CAAC,CAAC;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACJ,eAAW,oBAAoB,mBAAmB;AACjD,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI,UAAU,KAAK,CAAC,MAAM,KAAK;AAC9B,cAAM,aAAa,OAAO,OAAO;AACjC,cAAM,kBAAkB,WAAW,WAAW,SAAS,CAAC,MAAM,MAAM,KAAK;AACzE,yBAAiB,OAAO,OAAO,OAAO,QAAQ,QAAQ,kBAAkB;AAAA,MACzE;AACA,UAA6C,iBAAiB,SAAS,IAAK,OAAM,IAAI,MAAM,yKAA2K;AACvQ,gBAAU,yBAAyB,kBAAkB,QAAQ,OAAO;AACpE,UAA6C,UAAU,KAAK,CAAC,MAAM,IAAK,kCAAiC,SAAS,MAAM;AACxH,UAAI,gBAAgB;AACnB,uBAAe,MAAM,KAAK,OAAO;AACjC,YAAI,KAAuC,iBAAgB,gBAAgB,OAAO;AAAA,MACnF,OAAO;AACN,0BAAkB,mBAAmB;AACrC,YAAI,oBAAoB,QAAS,iBAAgB,MAAM,KAAK,OAAO;AACnE,YAAI,aAAa,OAAO,QAAQ,CAAC,cAAc,OAAO,GAAG;AACxD,cAAI,KAAuC,yBAAwB,QAAQ,MAAM;AACjF,sBAAY,OAAO,IAAI;AAAA,QACxB;AAAA,MACD;AACA,UAAI,YAAY,OAAO,EAAG,eAAc,OAAO;AAC/C,UAAI,qBAAqB,UAAU;AAClC,cAAM,WAAW,qBAAqB;AACtC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAK,UAAS,SAAS,CAAC,GAAG,SAAS,kBAAkB,eAAe,SAAS,CAAC,CAAC;AAAA,MACtH;AACA,uBAAiB,kBAAkB;AAAA,IACpC;AACA,WAAO,kBAAkB,MAAM;AAC9B,kBAAY,eAAe;AAAA,IAC5B,IAAI;AAAA,EACL;AACA,WAAS,YAAY,YAAY;AAChC,QAAI,YAAY,UAAU,GAAG;AAC5B,YAAM,UAAU,WAAW,IAAI,UAAU;AACzC,UAAI,SAAS;AACZ,mBAAW,OAAO,UAAU;AAC5B,iBAAS,OAAO,SAAS,QAAQ,OAAO,GAAG,CAAC;AAC5C,gBAAQ,SAAS,QAAQ,WAAW;AACpC,gBAAQ,MAAM,QAAQ,WAAW;AAAA,MAClC;AAAA,IACD,OAAO;AACN,YAAM,QAAQ,SAAS,QAAQ,UAAU;AACzC,UAAI,QAAQ,IAAI;AACf,iBAAS,OAAO,OAAO,CAAC;AACxB,YAAI,WAAW,OAAO,KAAM,YAAW,OAAO,WAAW,OAAO,IAAI;AACpE,mBAAW,SAAS,QAAQ,WAAW;AACvC,mBAAW,MAAM,QAAQ,WAAW;AAAA,MACrC;AAAA,IACD;AAAA,EACD;AACA,WAAS,YAAY;AACpB,WAAO;AAAA,EACR;AACA,WAAS,cAAc,SAAS;AAC/B,UAAM,QAAQ,mBAAmB,SAAS,QAAQ;AAClD,aAAS,OAAO,OAAO,GAAG,OAAO;AACjC,QAAI,QAAQ,OAAO,QAAQ,CAAC,cAAc,OAAO,EAAG,YAAW,IAAI,QAAQ,OAAO,MAAM,OAAO;AAAA,EAChG;AACA,WAAS,QAAQ,YAAY,iBAAiB;AAC7C,QAAI;AACJ,QAAI,SAAS,CAAC;AACd,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU,cAAc,WAAW,MAAM;AAC5C,gBAAU,WAAW,IAAI,WAAW,IAAI;AACxC,UAAI,CAAC,QAAS,OAAM,kBAAkB,WAAW,mBAAmB,EAAE,UAAU,WAAW,CAAC;AAC5F,UAAI,MAAuC;AAC1C,cAAM,gBAAgB,OAAO,KAAK,WAAW,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,KAAK,CAAC,MAAM,EAAE,SAAS,SAAS,CAAC;AAChI,YAAI,cAAc,OAAQ,QAAO,+BAA+B,cAAc,KAAK,MAAQ,CAAC,gIAAgI;AAAA,MAC7N;AACA,aAAO,QAAQ,OAAO;AACtB,eAAS,OAAO,WAAW,gBAAgB,QAAQ,QAAQ,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,OAAO,QAAQ,SAAS,QAAQ,OAAO,KAAK,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,WAAW,UAAU,WAAW,WAAW,QAAQ,QAAQ,KAAK,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/Q,aAAO,QAAQ,UAAU,MAAM;AAAA,IAChC,WAAW,WAAW,QAAQ,MAAM;AACnC,aAAO,WAAW;AAClB,UAA6C,CAAC,KAAK,WAAW,GAAG,EAAG,QAAO,2DAA2D,IAAI,oDAAoD,IAAI,wHAAwH;AAC1T,gBAAU,SAAS,KAAK,CAAC,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC;AAC9C,UAAI,SAAS;AACZ,iBAAS,QAAQ,MAAM,IAAI;AAC3B,eAAO,QAAQ,OAAO;AAAA,MACvB;AAAA,IACD,OAAO;AACN,gBAAU,gBAAgB,OAAO,WAAW,IAAI,gBAAgB,IAAI,IAAI,SAAS,KAAK,CAAC,MAAM,EAAE,GAAG,KAAK,gBAAgB,IAAI,CAAC;AAC5H,UAAI,CAAC,QAAS,OAAM,kBAAkB,WAAW,mBAAmB;AAAA,QACnE,UAAU;AAAA,QACV;AAAA,MACD,CAAC;AACD,aAAO,QAAQ,OAAO;AACtB,eAAS,OAAO,CAAC,GAAG,gBAAgB,QAAQ,WAAW,MAAM;AAC7D,aAAO,QAAQ,UAAU,MAAM;AAAA,IAChC;AACA,UAAM,UAAU,CAAC;AACjB,QAAI,gBAAgB;AACpB,WAAO,eAAe;AACrB,cAAQ,QAAQ,cAAc,MAAM;AACpC,sBAAgB,cAAc;AAAA,IAC/B;AACA,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,gBAAgB,OAAO;AAAA,IAC9B;AAAA,EACD;AACA,SAAO,QAAQ,CAAC,UAAU,SAAS,KAAK,CAAC;AACzC,WAAS,cAAc;AACtB,aAAS,SAAS;AAClB,eAAW,MAAM;AAAA,EAClB;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAOA,SAAS,WAAW,QAAQ,MAAM;AACjC,QAAM,YAAY,CAAC;AACnB,aAAW,OAAO,KAAM,KAAI,OAAO,OAAQ,WAAU,GAAG,IAAI,OAAO,GAAG;AACtE,SAAO;AACR;AAOA,SAAS,qBAAqB,QAAQ;AACrC,QAAM,aAAa;AAAA,IAClB,MAAM,OAAO;AAAA,IACb,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,MAAM,OAAO,QAAQ,CAAC;AAAA,IACtB,SAAS,OAAO;AAAA,IAChB,aAAa,OAAO;AAAA,IACpB,OAAO,qBAAqB,MAAM;AAAA,IAClC,UAAU,OAAO,YAAY,CAAC;AAAA,IAC9B,WAAW,CAAC;AAAA,IACZ,aAA6B,oBAAI,IAAI;AAAA,IACrC,cAA8B,oBAAI,IAAI;AAAA,IACtC,gBAAgB,CAAC;AAAA,IACjB,YAAY,gBAAgB,SAAS,OAAO,cAAc,OAAO,OAAO,aAAa,EAAE,SAAS,OAAO,UAAU;AAAA,EAClH;AACA,SAAO,eAAe,YAAY,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC;AACvD,SAAO;AACR;AAMA,SAAS,qBAAqB,QAAQ;AACrC,QAAM,cAAc,CAAC;AACrB,QAAM,QAAQ,OAAO,SAAS;AAC9B,MAAI,eAAe,OAAQ,aAAY,UAAU;AAAA,MAC5C,YAAW,QAAQ,OAAO,WAAY,aAAY,IAAI,IAAI,OAAO,UAAU,WAAW,MAAM,IAAI,IAAI;AACzG,SAAO;AACR;AAKA,SAAS,cAAc,QAAQ;AAC9B,SAAO,QAAQ;AACd,QAAI,OAAO,OAAO,QAAS,QAAO;AAClC,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;AAMA,SAAS,gBAAgB,SAAS;AACjC,SAAO,QAAQ,OAAO,CAAC,MAAM,WAAW,OAAO,MAAM,OAAO,IAAI,GAAG,CAAC,CAAC;AACtE;AACA,SAAS,YAAY,GAAG,GAAG;AAC1B,SAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE;AAC7E;AAOA,SAAS,gBAAgB,GAAG,GAAG;AAC9B,aAAW,OAAO,EAAE,KAAM,KAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC,EAAG,QAAO,OAAO,UAAU,EAAE,OAAO,IAAI,+BAA+B,EAAE,OAAO,IAAI,2CAA2C,IAAI,IAAI,GAAG;AACjO,aAAW,OAAO,EAAE,KAAM,KAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC,EAAG,QAAO,OAAO,UAAU,EAAE,OAAO,IAAI,+BAA+B,EAAE,OAAO,IAAI,2CAA2C,IAAI,IAAI,GAAG;AAClO;AAOA,SAAS,mCAAmC,sBAAsB,QAAQ;AACzE,MAAI,UAAU,OAAO,OAAO,QAAQ,CAAC,qBAAqB,QAAQ,CAAC,qBAAqB,KAAM,QAAO,oBAAoB,OAAO,OAAO,OAAO,IAAI,CAAC,4OAA4O;AAChY;AACA,SAAS,wBAAwB,QAAQ,QAAQ;AAChD,WAAS,WAAW,QAAQ,UAAU,WAAW,SAAS,OAAQ,KAAI,SAAS,OAAO,SAAS,OAAO,KAAM,OAAM,IAAI,MAAM,kBAAkB,OAAO,OAAO,IAAI,CAAC,yBAAyB,WAAW,WAAW,UAAU,YAAY,wHAAwH;AAC/V;AACA,SAAS,iCAAiC,QAAQ,QAAQ;AACzD,aAAW,OAAO,OAAO,KAAM,KAAI,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC,EAAG,QAAO,OAAO,kBAAkB,OAAO,OAAO,IAAI,2CAA2C,IAAI,IAAI,oBAAoB,OAAO,OAAO,IAAI,IAAI;AACnO;AAUA,SAAS,mBAAmB,SAAS,UAAU;AAC9C,MAAI,QAAQ;AACZ,MAAI,QAAQ,SAAS;AACrB,SAAO,UAAU,OAAO;AACvB,UAAM,MAAM,QAAQ,SAAS;AAC7B,QAAI,uBAAuB,SAAS,SAAS,GAAG,CAAC,IAAI,EAAG,SAAQ;AAAA,QAC3D,SAAQ,MAAM;AAAA,EACpB;AACA,QAAM,oBAAoB,qBAAqB,OAAO;AACtD,MAAI,mBAAmB;AACtB,YAAQ,SAAS,YAAY,mBAAmB,QAAQ,CAAC;AACzD,QAA6C,QAAQ,EAAG,QAAO,2BAA2B,kBAAkB,OAAO,IAAI,iBAAiB,QAAQ,OAAO,IAAI,GAAG;AAAA,EAC/J;AACA,SAAO;AACR;AACA,SAAS,qBAAqB,SAAS;AACtC,MAAI,WAAW;AACf,SAAO,WAAW,SAAS,OAAQ,KAAI,YAAY,QAAQ,KAAK,uBAAuB,SAAS,QAAQ,MAAM,EAAG,QAAO;AACzH;AAQA,SAAS,YAAY,EAAE,OAAO,GAAG;AAChC,SAAO,CAAC,EAAE,OAAO,QAAQ,OAAO,cAAc,OAAO,KAAK,OAAO,UAAU,EAAE,UAAU,OAAO;AAC/F;AASA,SAAS,QAAQ,OAAO;AACvB,QAAM,SAAS,OAAO,SAAS;AAC/B,QAAM,eAAe,OAAO,gBAAgB;AAC5C,MAAI,cAAc;AAClB,MAAI,aAAa;AACjB,QAAM,QAAQ,SAAS,MAAM;AAC5B,UAAM,KAAK,MAAM,MAAM,EAAE;AACzB,QAA8C,CAAC,eAAe,OAAO,YAAa;AACjF,UAAI,CAAC,gBAAgB,EAAE,EAAG,KAAI,YAAa,QAAO;AAAA,QAAmD,IAAI;AAAA,iBAAoB,YAAY;AAAA,WAAc,KAAK;AAAA,UACvJ,QAAO;AAAA,QAAmD,IAAI;AAAA,WAAc,KAAK;AACtF,mBAAa;AACb,oBAAc;AAAA,IACf;AACA,WAAO,OAAO,QAAQ,EAAE;AAAA,EACzB,CAAC;AACD,QAAM,oBAAoB,SAAS,MAAM;AACxC,UAAM,EAAE,QAAQ,IAAI,MAAM;AAC1B,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,eAAe,QAAQ,SAAS,CAAC;AACvC,UAAM,iBAAiB,aAAa;AACpC,QAAI,CAAC,gBAAgB,CAAC,eAAe,OAAQ,QAAO;AACpD,UAAM,QAAQ,eAAe,UAAU,kBAAkB,KAAK,MAAM,YAAY,CAAC;AACjF,QAAI,QAAQ,GAAI,QAAO;AACvB,UAAM,mBAAmB,gBAAgB,QAAQ,SAAS,CAAC,CAAC;AAC5D,WAAO,SAAS,KAAK,gBAAgB,YAAY,MAAM,oBAAoB,eAAe,eAAe,SAAS,CAAC,EAAE,SAAS,mBAAmB,eAAe,UAAU,kBAAkB,KAAK,MAAM,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI;AAAA,EAChO,CAAC;AACD,QAAM,WAAW,SAAS,MAAM,kBAAkB,QAAQ,MAAM,eAAe,aAAa,QAAQ,MAAM,MAAM,MAAM,CAAC;AACvH,QAAM,gBAAgB,SAAS,MAAM,kBAAkB,QAAQ,MAAM,kBAAkB,UAAU,aAAa,QAAQ,SAAS,KAAK,0BAA0B,aAAa,QAAQ,MAAM,MAAM,MAAM,CAAC;AACtM,WAAS,SAAS,IAAI,CAAC,GAAG;AACzB,QAAI,WAAW,CAAC,GAAG;AAClB,YAAM,IAAI,OAAO,MAAM,MAAM,OAAO,IAAI,YAAY,MAAM,EAAE,MAAM,MAAM,EAAE,CAAC,EAAE,MAAM,IAAI;AACvF,UAAI,MAAM,kBAAkB,OAAO,aAAa,eAAe,yBAAyB,SAAU,UAAS,oBAAoB,MAAM,CAAC;AACtI,aAAO;AAAA,IACR;AACA,WAAO,QAAQ,QAAQ;AAAA,EACxB;AACA,MAAwE,WAAW;AAClF,UAAM,WAAW,mBAAmB;AACpC,QAAI,UAAU;AACb,YAAM,sBAAsB;AAAA,QAC3B,OAAO,MAAM;AAAA,QACb,UAAU,SAAS;AAAA,QACnB,eAAe,cAAc;AAAA,QAC7B,OAAO;AAAA,MACR;AACA,eAAS,iBAAiB,SAAS,kBAAkB,CAAC;AACtD,eAAS,eAAe,KAAK,mBAAmB;AAChD,kBAAY,MAAM;AACjB,4BAAoB,QAAQ,MAAM;AAClC,4BAAoB,WAAW,SAAS;AACxC,4BAAoB,gBAAgB,cAAc;AAClD,4BAAoB,QAAQ,gBAAgB,MAAM,MAAM,EAAE,CAAC,IAAI,OAAO;AAAA,MACvE,GAAG,EAAE,OAAO,OAAO,CAAC;AAAA,IACrB;AAAA,EACD;AAIA,SAAO;AAAA,IACN;AAAA,IACA,MAAM,SAAS,MAAM,MAAM,MAAM,IAAI;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AACA,SAAS,kBAAkB,QAAQ;AAClC,SAAO,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAC1C;AACA,IAAM,iBAAiC,gBAAgB;AAAA,EACtD,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,OAAO;AAAA,IACN,IAAI;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,UAAU;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACA,gBAAgB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACvB,UAAM,OAAO,SAAS,QAAQ,KAAK,CAAC;AACpC,UAAM,EAAE,QAAQ,IAAI,OAAO,SAAS;AACpC,UAAM,UAAU,SAAS,OAAO;AAAA,MAC/B,CAAC,aAAa,MAAM,aAAa,QAAQ,iBAAiB,oBAAoB,CAAC,GAAG,KAAK;AAAA,MACvF,CAAC,aAAa,MAAM,kBAAkB,QAAQ,sBAAsB,0BAA0B,CAAC,GAAG,KAAK;AAAA,IACxG,EAAE;AACF,WAAO,MAAM;AACZ,YAAM,WAAW,MAAM,WAAW,kBAAkB,MAAM,QAAQ,IAAI,CAAC;AACvE,aAAO,MAAM,SAAS,WAAW,EAAE,KAAK;AAAA,QACvC,gBAAgB,KAAK,gBAAgB,MAAM,mBAAmB;AAAA,QAC9D,MAAM,KAAK;AAAA,QACX,SAAS,KAAK;AAAA,QACd,OAAO,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACZ;AAAA,EACD;AACD,CAAC;AAID,IAAM,aAAa;AACnB,SAAS,WAAW,GAAG;AACtB,MAAI,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,SAAU;AACtD,MAAI,EAAE,iBAAkB;AACxB,MAAI,EAAE,WAAW,UAAU,EAAE,WAAW,EAAG;AAC3C,MAAI,EAAE,iBAAiB,EAAE,cAAc,cAAc;AACpD,UAAM,SAAS,EAAE,cAAc,aAAa,QAAQ;AACpD,QAAI,cAAc,KAAK,MAAM,EAAG;AAAA,EACjC;AACA,MAAI,EAAE,eAAgB,GAAE,eAAe;AACvC,SAAO;AACR;AACA,SAAS,eAAe,OAAO,OAAO;AACrC,aAAW,OAAO,OAAO;AACxB,UAAM,aAAa,MAAM,GAAG;AAC5B,UAAM,aAAa,MAAM,GAAG;AAC5B,QAAI,OAAO,eAAe,UAAU;AACnC,UAAI,eAAe,WAAY,QAAO;AAAA,IACvC,WAAW,CAAC,QAAQ,UAAU,KAAK,WAAW,WAAW,WAAW,UAAU,WAAW,KAAK,CAAC,OAAO,MAAM,UAAU,WAAW,CAAC,CAAC,EAAG,QAAO;AAAA,EAC9I;AACA,SAAO;AACR;AAKA,SAAS,gBAAgB,QAAQ;AAChC,SAAO,SAAS,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAO;AACtE;AAOA,IAAM,eAAe,CAAC,WAAW,aAAa,iBAAiB,aAAa,OAAO,YAAY,eAAe,OAAO,cAAc;AAInI,IAAM,iBAAiC,gBAAgB;AAAA,EACtD,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACA,OAAO;AAAA,EACR;AAAA,EACA,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,MAAM,OAAO,EAAE,OAAO,MAAM,GAAG;AAC9B,IAAyC,oBAAoB;AAC7D,UAAM,gBAAgB,OAAO,qBAAqB;AAClD,UAAM,iBAAiB,SAAS,MAAM,MAAM,SAAS,cAAc,KAAK;AACxE,UAAM,gBAAgB,OAAO,cAAc,CAAC;AAC5C,UAAM,QAAQ,SAAS,MAAM;AAC5B,UAAI,eAAe,MAAM,aAAa;AACtC,YAAM,EAAE,QAAQ,IAAI,eAAe;AACnC,UAAI;AACJ,cAAQ,eAAe,QAAQ,YAAY,MAAM,CAAC,aAAa,WAAY;AAC3E,aAAO;AAAA,IACR,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM,eAAe,MAAM,QAAQ,MAAM,KAAK,CAAC;AAChF,YAAQ,cAAc,SAAS,MAAM,MAAM,QAAQ,CAAC,CAAC;AACrD,YAAQ,iBAAiB,eAAe;AACxC,YAAQ,uBAAuB,cAAc;AAC7C,UAAM,UAAU,IAAI;AACpB,UAAM,MAAM;AAAA,MACX,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,MAAM;AAAA,IACP,GAAG,CAAC,CAAC,UAAU,IAAI,IAAI,GAAG,CAAC,aAAa,MAAM,OAAO,MAAM;AAC1D,UAAI,IAAI;AACP,WAAG,UAAU,IAAI,IAAI;AACrB,YAAI,QAAQ,SAAS,MAAM,YAAY,aAAa,aAAa;AAChE,cAAI,CAAC,GAAG,YAAY,KAAM,IAAG,cAAc,KAAK;AAChD,cAAI,CAAC,GAAG,aAAa,KAAM,IAAG,eAAe,KAAK;AAAA,QACnD;AAAA,MACD;AACA,UAAI,YAAY,OAAO,CAAC,QAAQ,CAAC,kBAAkB,IAAI,IAAI,KAAK,CAAC,aAAc,EAAC,GAAG,eAAe,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,aAAa,SAAS,QAAQ,CAAC;AAAA,IACxJ,GAAG,EAAE,OAAO,OAAO,CAAC;AACpB,WAAO,MAAM;AACZ,YAAM,QAAQ,eAAe;AAC7B,YAAM,cAAc,MAAM;AAC1B,YAAM,eAAe,gBAAgB;AACrC,YAAM,gBAAgB,gBAAgB,aAAa,WAAW,WAAW;AACzE,UAAI,CAAC,cAAe,QAAO,cAAc,MAAM,SAAS;AAAA,QACvD,WAAW;AAAA,QACX;AAAA,MACD,CAAC;AACD,YAAM,mBAAmB,aAAa,MAAM,WAAW;AACvD,YAAM,aAAa,mBAAmB,qBAAqB,OAAO,MAAM,SAAS,OAAO,qBAAqB,aAAa,iBAAiB,KAAK,IAAI,mBAAmB;AACvK,YAAM,mBAAmB,CAAC,UAAU;AACnC,YAAI,MAAM,UAAU,YAAa,cAAa,UAAU,WAAW,IAAI;AAAA,MACxE;AACA,YAAM,YAAY,EAAE,eAAe,OAAO,CAAC,GAAG,YAAY,OAAO;AAAA,QAChE;AAAA,QACA,KAAK;AAAA,MACN,CAAC,CAAC;AACF,UAAwE,aAAa,UAAU,KAAK;AACnG,cAAM,OAAO;AAAA,UACZ,OAAO,MAAM;AAAA,UACb,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,QACpB;AACA,SAAC,QAAQ,UAAU,GAAG,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,QAAQ,CAAC,aAAa;AAClG,mBAAS,iBAAiB;AAAA,QAC3B,CAAC;AAAA,MACF;AACA,aAAO,cAAc,MAAM,SAAS;AAAA,QACnC,WAAW;AAAA,QACX;AAAA,MACD,CAAC,KAAK;AAAA,IACP;AAAA,EACD;AACD,CAAC;AACD,SAAS,cAAc,MAAM,MAAM;AAClC,MAAI,CAAC,KAAM,QAAO;AAClB,QAAM,cAAc,KAAK,IAAI;AAC7B,SAAO,YAAY,WAAW,IAAI,YAAY,CAAC,IAAI;AACpD;AAIA,IAAM,aAAa;AACnB,SAAS,sBAAsB;AAC9B,QAAM,WAAW,mBAAmB;AACpC,QAAM,aAAa,SAAS,UAAU,SAAS,OAAO,KAAK;AAC3D,QAAM,oBAAoB,SAAS,UAAU,SAAS,OAAO,WAAW,SAAS,OAAO,QAAQ;AAChG,MAAI,eAAe,eAAe,eAAe,WAAW,SAAS,YAAY,MAAM,OAAO,sBAAsB,YAAY,kBAAkB,SAAS,cAAc;AACxK,UAAM,OAAO,eAAe,cAAc,eAAe;AACzD,WAAO;AAAA;AAAA;AAAA;AAAA,KAIJ,IAAI;AAAA;AAAA,MAA6C,IAAI;AAAA,eAAmB;AAAA,EAC5E;AACD;AASA,SAAS,aAAa,SAAS;AAC9B,QAAM,UAAU,oBAAoB,QAAQ,QAAQ,OAAO;AAC3D,QAAM,eAAe,QAAQ,cAAc;AAC3C,QAAM,mBAAmB,QAAQ,kBAAkB;AACnD,QAAM,gBAAgB,QAAQ;AAC9B,MAA6C,CAAC,cAAe,OAAM,IAAI,MAAM,gIAAoI;AACjN,QAAM,eAAe,aAAa;AAClC,QAAM,sBAAsB,aAAa;AACzC,QAAM,cAAc,aAAa;AACjC,QAAM,eAAe,WAAW,yBAAyB;AACzD,MAAI,kBAAkB;AACtB,MAAI,aAAa,QAAQ,kBAAkB,uBAAuB,QAAS,SAAQ,oBAAoB;AACvG,QAAM,kBAAkB,cAAc,KAAK,MAAM,CAAC,eAAe,KAAK,UAAU;AAChF,QAAM,eAAe,cAAc,KAAK,MAAM,WAAW;AACzD,QAAM,eAAe,cAAc,KAAK,MAAM,MAAM;AACpD,WAAS,SAAS,eAAe,OAAO;AACvC,QAAI;AACJ,QAAI;AACJ,QAAI,YAAY,aAAa,GAAG;AAC/B,eAAS,QAAQ,iBAAiB,aAAa;AAC/C,UAA6C,CAAC,OAAQ,QAAO,iBAAiB,OAAO,aAAa,CAAC,uCAAuC,KAAK;AAC/I,eAAS;AAAA,IACV,MAAO,UAAS;AAChB,WAAO,QAAQ,SAAS,QAAQ,MAAM;AAAA,EACvC;AACA,WAAS,YAAY,MAAM;AAC1B,UAAM,gBAAgB,QAAQ,iBAAiB,IAAI;AACnD,QAAI,cAAe,SAAQ,YAAY,aAAa;AAAA,aAC3C,KAAuC,QAAO,qCAAqC,OAAO,IAAI,CAAC,GAAG;AAAA,EAC5G;AACA,WAAS,YAAY;AACpB,WAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,iBAAiB,aAAa,MAAM;AAAA,EACrE;AACA,WAAS,SAAS,MAAM;AACvB,WAAO,CAAC,CAAC,QAAQ,iBAAiB,IAAI;AAAA,EACvC;AACA,WAAS,QAAQ,aAAa,iBAAiB;AAC9C,sBAAkB,OAAO,CAAC,GAAG,mBAAmB,aAAa,KAAK;AAClE,QAAI,OAAO,gBAAgB,UAAU;AACpC,YAAM,qBAAqB,SAAS,cAAc,aAAa,gBAAgB,IAAI;AACnF,YAAM,iBAAiB,QAAQ,QAAQ,EAAE,MAAM,mBAAmB,KAAK,GAAG,eAAe;AACzF,YAAM,SAAS,cAAc,WAAW,mBAAmB,QAAQ;AACnE,UAAI,MAAuC;AAC1C,YAAI,OAAO,WAAW,IAAI,EAAG,QAAO,aAAa,WAAW,kBAAkB,MAAM,4DAA4D;AAAA,iBACvI,CAAC,eAAe,QAAQ,OAAQ,QAAO,0CAA0C,WAAW,GAAG;AAAA,MACzG;AACA,aAAO,OAAO,oBAAoB,gBAAgB;AAAA,QACjD,QAAQ,aAAa,eAAe,MAAM;AAAA,QAC1C,MAAM,OAAO,mBAAmB,IAAI;AAAA,QACpC,gBAAgB;AAAA,QAChB,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AACA,QAA6C,CAAC,gBAAgB,WAAW,GAAG;AAC3E,aAAO;AAAA,cAA+F,WAAW;AACjH,aAAO,QAAQ,CAAC,CAAC;AAAA,IAClB;AACA,QAAI;AACJ,QAAI,YAAY,QAAQ,MAAM;AAC7B,UAA6C,YAAY,eAAe,EAAE,UAAU,gBAAgB,OAAO,KAAK,YAAY,MAAM,EAAE,OAAQ,QAAO,SAAS,YAAY,IAAI,gGAAgG;AAC5Q,wBAAkB,OAAO,CAAC,GAAG,aAAa,EAAE,MAAM,SAAS,cAAc,YAAY,MAAM,gBAAgB,IAAI,EAAE,KAAK,CAAC;AAAA,IACxH,OAAO;AACN,YAAM,eAAe,OAAO,CAAC,GAAG,YAAY,MAAM;AAClD,iBAAW,OAAO,aAAc,KAAI,aAAa,GAAG,KAAK,KAAM,QAAO,aAAa,GAAG;AACtF,wBAAkB,OAAO,CAAC,GAAG,aAAa,EAAE,QAAQ,aAAa,YAAY,EAAE,CAAC;AAChF,sBAAgB,SAAS,aAAa,gBAAgB,MAAM;AAAA,IAC7D;AACA,UAAM,eAAe,QAAQ,QAAQ,iBAAiB,eAAe;AACrE,UAAM,OAAO,YAAY,QAAQ;AACjC,QAA6C,QAAQ,CAAC,KAAK,WAAW,GAAG,EAAG,QAAO,mEAAmE,IAAI,YAAY,IAAI,IAAI;AAC9K,iBAAa,SAAS,gBAAgB,aAAa,aAAa,MAAM,CAAC;AACvE,UAAM,WAAW,aAAa,kBAAkB,OAAO,CAAC,GAAG,aAAa;AAAA,MACvE,MAAM,WAAW,IAAI;AAAA,MACrB,MAAM,aAAa;AAAA,IACpB,CAAC,CAAC;AACF,UAAM,OAAO,cAAc,WAAW,QAAQ;AAC9C,QAAI,MAAuC;AAC1C,UAAI,KAAK,WAAW,IAAI,EAAG,QAAO,aAAa,WAAW,kBAAkB,IAAI,4DAA4D;AAAA,eACnI,CAAC,aAAa,QAAQ,OAAQ,QAAO,0CAA0C,YAAY,QAAQ,OAAO,YAAY,OAAO,WAAW,GAAG;AAAA,IACrJ;AACA,WAAO,OAAO;AAAA,MACb;AAAA,MACA;AAAA,MACA,OAAO,qBAAqB,iBAAiB,eAAe,YAAY,KAAK,IAAI,YAAY,SAAS,CAAC;AAAA,IACxG,GAAG,cAAc;AAAA,MAChB,gBAAgB;AAAA,MAChB;AAAA,IACD,CAAC;AAAA,EACF;AACA,WAAS,iBAAiB,IAAI;AAC7B,WAAO,OAAO,OAAO,WAAW,SAAS,cAAc,IAAI,aAAa,MAAM,IAAI,IAAI,OAAO,CAAC,GAAG,EAAE;AAAA,EACpG;AACA,WAAS,wBAAwB,IAAI,MAAM;AAC1C,QAAI,oBAAoB,GAAI,QAAO,kBAAkB,WAAW,sBAAsB;AAAA,MACrF;AAAA,MACA;AAAA,IACD,CAAC;AAAA,EACF;AACA,WAAS,KAAK,IAAI;AACjB,WAAO,iBAAiB,EAAE;AAAA,EAC3B;AACA,WAAS,QAAQ,IAAI;AACpB,WAAO,KAAK,OAAO,iBAAiB,EAAE,GAAG,EAAE,SAAS,KAAK,CAAC,CAAC;AAAA,EAC5D;AACA,WAAS,qBAAqB,IAAI,MAAM;AACvC,UAAM,cAAc,GAAG,QAAQ,GAAG,QAAQ,SAAS,CAAC;AACpD,QAAI,eAAe,YAAY,UAAU;AACxC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,oBAAoB,OAAO,aAAa,aAAa,SAAS,IAAI,IAAI,IAAI;AAC9E,UAAI,OAAO,sBAAsB,UAAU;AAC1C,4BAAoB,kBAAkB,SAAS,GAAG,KAAK,kBAAkB,SAAS,GAAG,IAAI,oBAAoB,iBAAiB,iBAAiB,IAAI,EAAE,MAAM,kBAAkB;AAC7K,0BAAkB,SAAS,CAAC;AAAA,MAC7B;AACA,UAA6C,kBAAkB,QAAQ,QAAQ,EAAE,UAAU,oBAAoB;AAC9G,eAAO;AAAA,EAA4B,KAAK,UAAU,mBAAmB,MAAM,CAAC,CAAC;AAAA,uBAA0B,GAAG,QAAQ,2EAA2E;AAC7L,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACnC;AACA,aAAO,OAAO;AAAA,QACb,OAAO,GAAG;AAAA,QACV,MAAM,GAAG;AAAA,QACT,QAAQ,kBAAkB,QAAQ,OAAO,CAAC,IAAI,GAAG;AAAA,MAClD,GAAG,iBAAiB;AAAA,IACrB;AAAA,EACD;AACA,WAAS,iBAAiB,IAAI,gBAAgB;AAC7C,UAAM,iBAAiB,kBAAkB,QAAQ,EAAE;AACnD,UAAM,OAAO,aAAa;AAC1B,UAAM,OAAO,GAAG;AAChB,UAAM,QAAQ,GAAG;AACjB,UAAM,YAAY,GAAG,YAAY;AACjC,UAAM,iBAAiB,qBAAqB,gBAAgB,IAAI;AAChE,QAAI,eAAgB,QAAO,iBAAiB,OAAO,iBAAiB,cAAc,GAAG;AAAA,MACpF,OAAO,OAAO,mBAAmB,WAAW,OAAO,CAAC,GAAG,MAAM,eAAe,KAAK,IAAI;AAAA,MACrF;AAAA,MACA,SAAS;AAAA,IACV,CAAC,GAAG,kBAAkB,cAAc;AACpC,UAAM,aAAa;AACnB,eAAW,iBAAiB;AAC5B,QAAI;AACJ,QAAI,CAAC,SAAS,oBAAoB,kBAAkB,MAAM,cAAc,GAAG;AAC1E,gBAAU,kBAAkB,WAAW,uBAAuB;AAAA,QAC7D,IAAI;AAAA,QACJ;AAAA,MACD,CAAC;AACD,mBAAa,MAAM,MAAM,MAAM,KAAK;AAAA,IACrC;AACA,YAAQ,UAAU,QAAQ,QAAQ,OAAO,IAAI,SAAS,YAAY,IAAI,GAAG,MAAM,CAAC,UAAU,oBAAoB,KAAK,IAAI,oBAAoB,OAAO,WAAW,yBAAyB,IAAI,QAAQ,YAAY,KAAK,IAAI,aAAa,OAAO,YAAY,IAAI,CAAC,EAAE,KAAK,CAAC,cAAc;AACjR,UAAI,WAAW;AACd,YAAI,oBAAoB,WAAW,WAAW,yBAAyB,GAAG;AACzE,cAA6C,oBAAoB,kBAAkB,QAAQ,UAAU,EAAE,GAAG,UAAU,KAAK,mBAAmB,eAAe,SAAS,eAAe,SAAS,eAAe,SAAS,IAAI,KAAK,IAAI;AAChO,mBAAO,mFAAmF,KAAK,QAAQ,SAAS,WAAW,QAAQ;AAAA,gNAAyP;AAC5X,mBAAO,QAAQ,OAAuB,IAAI,MAAM,uCAAuC,CAAC;AAAA,UACzF;AACA,iBAAO,iBAAiB,OAAO,EAAE,SAAS,UAAU,GAAG,iBAAiB,UAAU,EAAE,GAAG;AAAA,YACtF,OAAO,OAAO,UAAU,OAAO,WAAW,OAAO,CAAC,GAAG,MAAM,UAAU,GAAG,KAAK,IAAI;AAAA,YACjF;AAAA,UACD,CAAC,GAAG,kBAAkB,UAAU;AAAA,QACjC;AAAA,MACD,MAAO,aAAY,mBAAmB,YAAY,MAAM,MAAM,WAAW,IAAI;AAC7E,uBAAiB,YAAY,MAAM,SAAS;AAC5C,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAMA,WAAS,iCAAiC,IAAI,MAAM;AACnD,UAAM,QAAQ,wBAAwB,IAAI,IAAI;AAC9C,WAAO,QAAQ,QAAQ,OAAO,KAAK,IAAI,QAAQ,QAAQ;AAAA,EACxD;AACA,WAAS,eAAe,IAAI;AAC3B,UAAM,MAAM,cAAc,OAAO,EAAE,KAAK,EAAE;AAC1C,WAAO,OAAO,OAAO,IAAI,mBAAmB,aAAa,IAAI,eAAe,EAAE,IAAI,GAAG;AAAA,EACtF;AACA,WAAS,SAAS,IAAI,MAAM;AAC3B,QAAI;AACJ,UAAM,CAAC,gBAAgB,iBAAiB,eAAe,IAAI,uBAAuB,IAAI,IAAI;AAC1F,aAAS,wBAAwB,eAAe,QAAQ,GAAG,oBAAoB,IAAI,IAAI;AACvF,eAAW,UAAU,eAAgB,QAAO,YAAY,QAAQ,CAAC,UAAU;AAC1E,aAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,IAC9C,CAAC;AACD,UAAM,0BAA0B,iCAAiC,KAAK,MAAM,IAAI,IAAI;AACpF,WAAO,KAAK,uBAAuB;AACnC,WAAO,cAAc,MAAM,EAAE,KAAK,MAAM;AACvC,eAAS,CAAC;AACV,iBAAW,SAAS,aAAa,KAAK,EAAG,QAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AACtF,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC5B,CAAC,EAAE,KAAK,MAAM;AACb,eAAS,wBAAwB,iBAAiB,qBAAqB,IAAI,IAAI;AAC/E,iBAAW,UAAU,gBAAiB,QAAO,aAAa,QAAQ,CAAC,UAAU;AAC5E,eAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,MAC9C,CAAC;AACD,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC5B,CAAC,EAAE,KAAK,MAAM;AACb,eAAS,CAAC;AACV,iBAAW,UAAU,gBAAiB,KAAI,OAAO,YAAa,KAAI,QAAQ,OAAO,WAAW,EAAG,YAAW,eAAe,OAAO,YAAa,QAAO,KAAK,iBAAiB,aAAa,IAAI,IAAI,CAAC;AAAA,UAC3L,QAAO,KAAK,iBAAiB,OAAO,aAAa,IAAI,IAAI,CAAC;AAC/D,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC5B,CAAC,EAAE,KAAK,MAAM;AACb,SAAG,QAAQ,QAAQ,CAAC,WAAW,OAAO,iBAAiB,CAAC,CAAC;AACzD,eAAS,wBAAwB,iBAAiB,oBAAoB,IAAI,MAAM,cAAc;AAC9F,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC5B,CAAC,EAAE,KAAK,MAAM;AACb,eAAS,CAAC;AACV,iBAAW,SAAS,oBAAoB,KAAK,EAAG,QAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAC7F,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC5B,CAAC,EAAE,MAAM,CAAC,QAAQ,oBAAoB,KAAK,WAAW,oBAAoB,IAAI,MAAM,QAAQ,OAAO,GAAG,CAAC;AAAA,EACxG;AACA,WAAS,iBAAiB,IAAI,MAAM,SAAS;AAC5C,gBAAY,KAAK,EAAE,QAAQ,CAAC,UAAU,eAAe,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,CAAC;AAAA,EACrF;AAMA,WAAS,mBAAmB,YAAY,MAAM,QAAQ,WAAW,MAAM;AACtE,UAAM,QAAQ,wBAAwB,YAAY,IAAI;AACtD,QAAI,MAAO,QAAO;AAClB,UAAM,oBAAoB,SAAS;AACnC,UAAM,QAAQ,CAAC,YAAY,CAAC,IAAI,QAAQ;AACxC,QAAI,OAAQ,KAAI,aAAa,kBAAmB,eAAc,QAAQ,WAAW,UAAU,OAAO,EAAE,QAAQ,qBAAqB,SAAS,MAAM,OAAO,GAAG,IAAI,CAAC;AAAA,QAC1J,eAAc,KAAK,WAAW,UAAU,IAAI;AACjD,iBAAa,QAAQ;AACrB,iBAAa,YAAY,MAAM,QAAQ,iBAAiB;AACxD,gBAAY;AAAA,EACb;AACA,MAAI;AACJ,WAAS,iBAAiB;AACzB,QAAI,sBAAuB;AAC3B,4BAAwB,cAAc,OAAO,CAAC,IAAI,OAAO,SAAS;AACjE,UAAI,CAAC,OAAO,UAAW;AACvB,YAAM,aAAa,QAAQ,EAAE;AAC7B,YAAM,iBAAiB,qBAAqB,YAAY,OAAO,aAAa,KAAK;AACjF,UAAI,gBAAgB;AACnB,yBAAiB,OAAO,gBAAgB;AAAA,UACvC,SAAS;AAAA,UACT,OAAO;AAAA,QACR,CAAC,GAAG,UAAU,EAAE,MAAM,IAAI;AAC1B;AAAA,MACD;AACA,wBAAkB;AAClB,YAAM,OAAO,aAAa;AAC1B,UAAI,UAAW,oBAAmB,aAAa,KAAK,UAAU,KAAK,KAAK,GAAG,sBAAsB,CAAC;AAClG,eAAS,YAAY,IAAI,EAAE,MAAM,CAAC,UAAU;AAC3C,YAAI,oBAAoB,OAAO,WAAW,qBAAqB,WAAW,oBAAoB,EAAG,QAAO;AACxG,YAAI,oBAAoB,OAAO,WAAW,yBAAyB,GAAG;AACrE,2BAAiB,OAAO,iBAAiB,MAAM,EAAE,GAAG,EAAE,OAAO,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,CAAC,YAAY;AACnG,gBAAI,oBAAoB,SAAS,WAAW,qBAAqB,WAAW,qBAAqB,KAAK,CAAC,KAAK,SAAS,KAAK,SAAS,eAAe,IAAK,eAAc,GAAG,IAAI,KAAK;AAAA,UAClL,CAAC,EAAE,MAAM,IAAI;AACb,iBAAO,QAAQ,OAAO;AAAA,QACvB;AACA,YAAI,KAAK,MAAO,eAAc,GAAG,CAAC,KAAK,OAAO,KAAK;AACnD,eAAO,aAAa,OAAO,YAAY,IAAI;AAAA,MAC5C,CAAC,EAAE,KAAK,CAAC,YAAY;AACpB,kBAAU,WAAW,mBAAmB,YAAY,MAAM,KAAK;AAC/D,YAAI,SAAS;AACZ,cAAI,KAAK,SAAS,CAAC,oBAAoB,SAAS,WAAW,oBAAoB,EAAG,eAAc,GAAG,CAAC,KAAK,OAAO,KAAK;AAAA,mBAC5G,KAAK,SAAS,eAAe,OAAO,oBAAoB,SAAS,WAAW,qBAAqB,WAAW,qBAAqB,EAAG,eAAc,GAAG,IAAI,KAAK;AAAA,QACxK;AACA,yBAAiB,YAAY,MAAM,OAAO;AAAA,MAC3C,CAAC,EAAE,MAAM,IAAI;AAAA,IACd,CAAC;AAAA,EACF;AACA,MAAI,gBAAgB,aAAa;AACjC,MAAI,iBAAiB,aAAa;AAClC,MAAI;AASJ,WAAS,aAAa,OAAO,IAAI,MAAM;AACtC,gBAAY,KAAK;AACjB,UAAM,OAAO,eAAe,KAAK;AACjC,QAAI,KAAK,OAAQ,MAAK,QAAQ,CAAC,YAAY,QAAQ,OAAO,IAAI,IAAI,CAAC;AAAA,SAC9D;AACJ,UAAI,KAAuC,QAAO,yCAAyC;AAC3F,cAAQ,MAAM,KAAK;AAAA,IACpB;AACA,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC5B;AACA,WAAS,UAAU;AAClB,QAAI,SAAS,aAAa,UAAU,0BAA2B,QAAO,QAAQ,QAAQ;AACtF,WAAO,IAAI,QAAQ,CAAC,WAAW,WAAW;AACzC,oBAAc,IAAI,CAAC,WAAW,MAAM,CAAC;AAAA,IACtC,CAAC;AAAA,EACF;AACA,WAAS,YAAY,KAAK;AACzB,QAAI,CAAC,OAAO;AACX,cAAQ,CAAC;AACT,qBAAe;AACf,oBAAc,KAAK,EAAE,QAAQ,CAAC,CAAC,WAAW,MAAM,MAAM,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC;AACrF,oBAAc,MAAM;AAAA,IACrB;AACA,WAAO;AAAA,EACR;AACA,WAAS,aAAa,IAAI,MAAM,QAAQ,mBAAmB;AAC1D,UAAM,EAAE,eAAe,IAAI;AAC3B,QAAI,CAAC,aAAa,CAAC,eAAgB,QAAO,QAAQ,QAAQ;AAC1D,UAAM,iBAAiB,CAAC,UAAU,uBAAuB,aAAa,GAAG,UAAU,CAAC,CAAC,MAAM,qBAAqB,CAAC,WAAW,QAAQ,SAAS,QAAQ,MAAM,UAAU;AACrK,WAAO,SAAS,EAAE,KAAK,MAAM,eAAe,IAAI,MAAM,cAAc,CAAC,EAAE,KAAK,CAAC,aAAa,YAAY,iBAAiB,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,aAAa,KAAK,IAAI,IAAI,CAAC;AAAA,EAC7K;AACA,QAAM,KAAK,CAAC,UAAU,cAAc,GAAG,KAAK;AAC5C,MAAI;AACJ,QAAM,gBAAgC,oBAAI,IAAI;AAC9C,QAAM,SAAS;AAAA,IACd;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,aAAa,QAAQ;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,MAAM,GAAG,EAAE;AAAA,IACjB,SAAS,MAAM,GAAG,CAAC;AAAA,IACnB,YAAY,aAAa;AAAA,IACzB,eAAe,oBAAoB;AAAA,IACnC,WAAW,YAAY;AAAA,IACvB,SAAS,eAAe;AAAA,IACxB;AAAA,IACA,QAAQ,KAAK;AACZ,UAAI,UAAU,cAAc,UAAU;AACtC,UAAI,UAAU,cAAc,UAAU;AACtC,UAAI,OAAO,iBAAiB,UAAU;AACtC,aAAO,eAAe,IAAI,OAAO,kBAAkB,UAAU;AAAA,QAC5D,YAAY;AAAA,QACZ,KAAK,MAAM,MAAM,YAAY;AAAA,MAC9B,CAAC;AACD,UAAI,aAAa,CAAC,WAAW,aAAa,UAAU,2BAA2B;AAC9E,kBAAU;AACV,aAAK,cAAc,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC3C,cAAI,KAAuC,QAAO,8CAA8C,GAAG;AAAA,QACpG,CAAC;AAAA,MACF;AACA,YAAM,gBAAgB,CAAC;AACvB,iBAAW,OAAO,0BAA2B,QAAO,eAAe,eAAe,KAAK;AAAA,QACtF,KAAK,MAAM,aAAa,MAAM,GAAG;AAAA,QACjC,YAAY;AAAA,MACb,CAAC;AACD,UAAI,QAAQ,WAAW,MAAM;AAC7B,UAAI,QAAQ,kBAAkB,gBAAgB,aAAa,CAAC;AAC5D,UAAI,QAAQ,uBAAuB,YAAY;AAC/C,YAAM,aAAa,IAAI;AACvB,oBAAc,IAAI,GAAG;AACrB,UAAI,UAAU,WAAW;AACxB,sBAAc,OAAO,GAAG;AACxB,YAAI,cAAc,OAAO,GAAG;AAC3B,4BAAkB;AAClB,mCAAyB,sBAAsB;AAC/C,kCAAwB;AACxB,uBAAa,QAAQ;AACrB,oBAAU;AACV,kBAAQ;AAAA,QACT;AACA,mBAAW;AAAA,MACZ;AACA,UAAwE,UAAW,aAAY,KAAK,QAAQ,OAAO;AAAA,IACpH;AAAA,EACD;AACA,WAAS,cAAc,QAAQ;AAC9B,WAAO,OAAO,OAAO,CAAC,SAAS,UAAU,QAAQ,KAAK,MAAM,eAAe,KAAK,CAAC,GAAG,QAAQ,QAAQ,CAAC;AAAA,EACtG;AACA,SAAO;AACR;AAQA,SAAS,YAAY;AACpB,SAAO,OAAO,SAAS;AACxB;AAKA,SAAS,SAAS,OAAO;AACxB,SAAO,OAAO,gBAAgB;AAC/B;", "names": ["location", "location"]}